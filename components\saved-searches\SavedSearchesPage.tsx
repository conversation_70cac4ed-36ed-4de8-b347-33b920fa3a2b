'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Bell, 
  BellOff, 
  Plus, 
  Edit2, 
  Trash2, 
  Calendar,
  MapPin,
  Filter,
  Mail,
  Clock,
  Eye,
  X
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface SavedSearch {
  id: string;
  name: string;
  searchParams: {
    search?: string;
    location?: string;
    propertyType?: string;
    listingType?: string;
    minPrice?: number;
    maxPrice?: number;
    bedrooms?: number;
    bathrooms?: number;
    minArea?: number;
    maxArea?: number;
    furnished?: string;
    amenities?: string[];
  };
  emailAlerts: boolean;
  frequency: 'daily' | 'weekly' | 'monthly';
  isActive: boolean;
  lastSent?: string;
  createdAt: string;
  updatedAt: string;
}

export function SavedSearchesPage() {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingSearch, setEditingSearch] = useState<SavedSearch | null>(null);
  const [newSearchName, setNewSearchName] = useState('');
  const [emailAlerts, setEmailAlerts] = useState(true);
  const [frequency, setFrequency] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  useEffect(() => {
    fetchSavedSearches();
  }, []);

  const fetchSavedSearches = async () => {
    try {
      const response = await fetch('/api/saved-searches');
      if (response.ok) {
        const data = await response.json();
        setSavedSearches(data.savedSearches);
      }
    } catch (error) {
      console.error('Error fetching saved searches:', error);
    } finally {
      setLoading(false);
    }
  };

  const createSavedSearch = async () => {
    if (!newSearchName.trim()) return;

    try {
      // Get current search parameters from URL or use defaults
      const currentSearch = new URLSearchParams(window.location.search);
      const searchParams = {
        search: currentSearch.get('search') || '',
        location: currentSearch.get('location') || '',
        propertyType: currentSearch.get('propertyType') || '',
        listingType: currentSearch.get('listingType') || '',
        minPrice: currentSearch.get('minPrice') ? Number(currentSearch.get('minPrice')) : undefined,
        maxPrice: currentSearch.get('maxPrice') ? Number(currentSearch.get('maxPrice')) : undefined,
        bedrooms: currentSearch.get('bedrooms') ? Number(currentSearch.get('bedrooms')) : undefined,
        bathrooms: currentSearch.get('bathrooms') ? Number(currentSearch.get('bathrooms')) : undefined,
        minArea: currentSearch.get('minArea') ? Number(currentSearch.get('minArea')) : undefined,
        maxArea: currentSearch.get('maxArea') ? Number(currentSearch.get('maxArea')) : undefined,
        furnished: currentSearch.get('furnished') || '',
        amenities: currentSearch.get('amenities')?.split(',') || [],
      };

      const response = await fetch('/api/saved-searches', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newSearchName,
          searchParams,
          emailAlerts,
          frequency,
        }),
      });

      if (response.ok) {
        fetchSavedSearches();
        setShowCreateModal(false);
        setNewSearchName('');
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create saved search');
      }
    } catch (error) {
      console.error('Error creating saved search:', error);
      alert('Failed to create saved search');
    }
  };

  const updateSavedSearch = async (search: SavedSearch) => {
    try {
      const response = await fetch(`/api/saved-searches?id=${search.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: search.name,
          searchParams: search.searchParams,
          emailAlerts: search.emailAlerts,
          frequency: search.frequency,
        }),
      });

      if (response.ok) {
        fetchSavedSearches();
        setEditingSearch(null);
      }
    } catch (error) {
      console.error('Error updating saved search:', error);
    }
  };

  const deleteSavedSearch = async (id: string) => {
    if (!confirm('Are you sure you want to delete this saved search?')) return;

    try {
      const response = await fetch(`/api/saved-searches?id=${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        fetchSavedSearches();
      }
    } catch (error) {
      console.error('Error deleting saved search:', error);
    }
  };

  const runSearch = (searchParams: any) => {
    const params = new URLSearchParams();
    
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value && value !== '') {
        if (Array.isArray(value)) {
          params.set(key, value.join(','));
        } else {
          params.set(key, value.toString());
        }
      }
    });

    window.location.href = `/properties?${params.toString()}`;
  };

  const formatSearchSummary = (searchParams: any) => {
    const parts = [];
    
    if (searchParams.search) parts.push(`"${searchParams.search}"`);
    if (searchParams.location) parts.push(`in ${searchParams.location}`);
    if (searchParams.propertyType) parts.push(searchParams.propertyType);
    if (searchParams.listingType) parts.push(`for ${searchParams.listingType}`);
    if (searchParams.bedrooms) parts.push(`${searchParams.bedrooms} BHK`);
    if (searchParams.minPrice && searchParams.maxPrice) {
      parts.push(`₹${Number(searchParams.minPrice).toLocaleString()} - ₹${Number(searchParams.maxPrice).toLocaleString()}`);
    } else if (searchParams.minPrice) {
      parts.push(`above ₹${Number(searchParams.minPrice).toLocaleString()}`);
    } else if (searchParams.maxPrice) {
      parts.push(`under ₹${Number(searchParams.maxPrice).toLocaleString()}`);
    }

    return parts.length > 0 ? parts.join(' • ') : 'All properties';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Search className="w-6 h-6 mr-2 text-blue-600" />
                Saved Searches
              </h1>
              <p className="text-gray-600 mt-1">
                {savedSearches.length} saved {savedSearches.length === 1 ? 'search' : 'searches'}
              </p>
            </div>
            
            <Button onClick={() => setShowCreateModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Save Current Search
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {savedSearches.length === 0 ? (
          // Empty State
          <div className="text-center py-16">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              No Saved Searches Yet
            </h3>
            <p className="text-gray-600 mb-6">
              Save your property searches and get email alerts when new matching properties are added
            </p>
            <div className="space-x-4">
              <Button onClick={() => setShowCreateModal(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Your First Search
              </Button>
              <Link href="/properties">
                <Button variant="outline">Browse Properties</Button>
              </Link>
            </div>
          </div>
        ) : (
          // Saved Searches List
          <div className="grid gap-6">
            {savedSearches.map((search) => (
              <motion.div
                key={search.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-lg shadow-card p-6 hover:shadow-card-hover transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{search.name}</h3>
                      <div className="flex items-center gap-2">
                        {search.emailAlerts ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <Bell className="w-3 h-3 mr-1" />
                            Alerts On
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                            <BellOff className="w-3 h-3 mr-1" />
                            Alerts Off
                          </span>
                        )}
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          <Clock className="w-3 h-3 mr-1" />
                          {search.frequency}
                        </span>
                      </div>
                    </div>
                    
                    <p className="text-gray-600 mb-3">
                      {formatSearchSummary(search.searchParams)}
                    </p>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span className="flex items-center">
                        <Calendar className="w-4 h-4 mr-1" />
                        Created {new Date(search.createdAt).toLocaleDateString()}
                      </span>
                      {search.lastSent && (
                        <span className="flex items-center">
                          <Mail className="w-4 h-4 mr-1" />
                          Last alert {new Date(search.lastSent).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => runSearch(search.searchParams)}
                    >
                      <Eye className="w-4 h-4 mr-1" />
                      Run Search
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setEditingSearch(search)}
                    >
                      <Edit2 className="w-4 h-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => deleteSavedSearch(search.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Create Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Save Current Search</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search Name
                </label>
                <Input
                  placeholder="e.g., 3 BHK in Gurgaon under 1 Cr"
                  value={newSearchName}
                  onChange={(e) => setNewSearchName(e.target.value)}
                />
              </div>
              
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={emailAlerts}
                    onChange={(e) => setEmailAlerts(e.target.checked)}
                    className="mr-2"
                  />
                  <span className="text-sm">Send email alerts for new matching properties</span>
                </label>
              </div>
              
              {emailAlerts && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Alert Frequency
                  </label>
                  <select
                    value={frequency}
                    onChange={(e) => setFrequency(e.target.value as any)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              )}
              
              <div className="flex gap-3 pt-4">
                <Button onClick={createSavedSearch} className="flex-1">
                  Save Search
                </Button>
                <Button variant="outline" onClick={() => setShowCreateModal(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Edit Modal */}
      {editingSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Edit Saved Search</h3>
              <button
                onClick={() => setEditingSearch(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Search Name
                </label>
                <Input
                  value={editingSearch.name}
                  onChange={(e) => setEditingSearch({
                    ...editingSearch,
                    name: e.target.value
                  })}
                />
              </div>
              
              <div>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editingSearch.emailAlerts}
                    onChange={(e) => setEditingSearch({
                      ...editingSearch,
                      emailAlerts: e.target.checked
                    })}
                    className="mr-2"
                  />
                  <span className="text-sm">Send email alerts for new matching properties</span>
                </label>
              </div>
              
              {editingSearch.emailAlerts && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Alert Frequency
                  </label>
                  <select
                    value={editingSearch.frequency}
                    onChange={(e) => setEditingSearch({
                      ...editingSearch,
                      frequency: e.target.value as any
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="daily">Daily</option>
                    <option value="weekly">Weekly</option>
                    <option value="monthly">Monthly</option>
                  </select>
                </div>
              )}
              
              <div className="flex gap-3 pt-4">
                <Button onClick={() => updateSavedSearch(editingSearch)} className="flex-1">
                  Update Search
                </Button>
                <Button variant="outline" onClick={() => setEditingSearch(null)}>
                  Cancel
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}