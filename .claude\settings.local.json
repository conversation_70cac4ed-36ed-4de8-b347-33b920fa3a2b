{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(npm run dev:*)", "Bash(npm install:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npm run:*)", "Bash(docker logs:*)", "Bash(npx drizzle-kit push:*)", "<PERSON><PERSON>(docker exec:*)", "Bash(node:*)", "Bash(rm:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(taskkill:*)", "<PERSON><PERSON>(touch:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(curl:*)", "Bash(start \"C:\\Program Files\\Docker\\Docker\\Docker Desktop.exe\")", "Bash(git add:*)", "Bash(git commit:*)", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)", "Bash(npx tailwindcss:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm cache clean:*)", "Bash(npx tsc:*)"], "deny": []}}