import { db } from './config';
import { properties, users } from './schema';
import { sampleProperties } from '@/data/sampleProperties';

// Function to generate slug from title
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

// Function to migrate sample data to PostgreSQL
export async function migrateSampleData() {
  try {
    console.log('Starting data migration...');
    
    // Create admin user
    const adminUser = await db.insert(users).values({
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      password: null, // Will be set during authentication setup
      provider: 'credentials',
      emailVerified: true,
      isActive: true,
    }).returning();

    console.log('Created admin user:', adminUser[0].id);

    // Migrate properties
    const migratedProperties = [];
    
    for (const property of sampleProperties) {
      const slug = generateSlug(property.title || 'untitled');
      
      const newProperty = {
        title: property.title || 'Untitled Property',
        description: property.description || 'No description available',
        slug: slug,
        price: property.price?.toString() || '0',
        
        // Location
        area: property.location?.area || 'Unknown',
        city: property.location?.city || 'Unknown',
        state: 'Delhi NCR', // Default for all properties
        address: property.location?.address || 'Unknown',
        pincode: property.location?.pincode || null,
        latitude: property.location?.coordinates?.[0]?.toString() || null,
        longitude: property.location?.coordinates?.[1]?.toString() || null,
        
        // Specifications
        bedrooms: property.specifications?.bedrooms || null,
        bathrooms: property.specifications?.bathrooms || null,
        totalArea: property.specifications?.area || null,
        floors: property.specifications?.floors || null,
        parking: (property.specifications?.parking ? 1 : 0),
        balconies: property.specifications?.balconies || 0,
        furnished: property.specifications?.furnished?.toLowerCase() || 'unfurnished',
        ageOfProperty: property.specifications?.ageOfProperty || null,
        
        // Property details
        propertyType: property.propertyType || 'apartment',
        listingType: property.listingType || 'sale',
        status: property.status || 'available',
        
        // Features
        amenities: property.amenities || [],
        
        // Media
        images: property.images || [],
        
        // Contact
        ownerId: adminUser[0].id,
        contactName: 'Armaan Sharma',
        contactPhone: property.contactDetails?.phone || '+91 98765 43210',
        contactEmail: property.contactDetails?.email || '<EMAIL>',
        contactWhatsapp: property.contactDetails?.whatsapp || '+91 98765 43210',
        
        // Marketing
        featured: property.featured || false,
        verified: true,
        published: true,
        views: property.views || 0,
        
        // SEO
        metaTitle: property.title || null,
        metaDescription: property.description?.substring(0, 160) || null,
        
        // Timestamps
        publishedAt: new Date(),
      };
      
      const inserted = await db.insert(properties).values(newProperty).returning();
      migratedProperties.push(inserted[0]);
      
      console.log(`Migrated property: ${property.title}`);
    }
    
    console.log(`Migration completed! Migrated ${migratedProperties.length} properties.`);
    return migratedProperties;
    
  } catch (error) {
    console.error('Migration failed:', error);
    throw error;
  }
}

// Function to seed initial data
export async function seedDatabase() {
  try {
    console.log('Seeding database with initial data...');
    
    // Check if data already exists
    const existingProperties = await db.select().from(properties).limit(1);
    
    if (existingProperties.length > 0) {
      console.log('Database already contains data, skipping seed...');
      return;
    }
    
    await migrateSampleData();
    console.log('Database seeding completed successfully!');
    
  } catch (error) {
    console.error('Database seeding failed:', error);
    throw error;
  }
}