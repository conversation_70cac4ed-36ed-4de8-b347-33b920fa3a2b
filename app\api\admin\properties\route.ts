import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { properties } from '@/lib/db/schema';
import { desc, eq, like, and, or } from 'drizzle-orm';

// GET - List all properties with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const propertyType = searchParams.get('propertyType') || '';
    const listingType = searchParams.get('listingType') || '';

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [];

    if (search) {
      whereConditions.push(
        or(
          like(properties.title, `%${search}%`),
          like(properties.area, `%${search}%`),
          like(properties.city, `%${search}%`)
        )
      );
    }

    if (status) {
      whereConditions.push(eq(properties.status, status));
    }

    if (propertyType) {
      whereConditions.push(eq(properties.propertyType, propertyType));
    }

    if (listingType) {
      whereConditions.push(eq(properties.listingType, listingType));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Get properties with pagination
    const propertiesList = await db
      .select()
      .from(properties)
      .where(whereClause)
      .orderBy(desc(properties.createdAt))
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalCount = await db
      .select({ count: properties.id })
      .from(properties)
      .where(whereClause);

    const totalPages = Math.ceil(totalCount.length / limit);

    return NextResponse.json({
      properties: propertiesList.map(property => ({
        ...property,
        price: property.price ? parseFloat(property.price) : 0,
        pricePerSqft: property.pricePerSqft ? parseFloat(property.pricePerSqft) : null,
        latitude: property.latitude ? parseFloat(property.latitude) : null,
        longitude: property.longitude ? parseFloat(property.longitude) : null,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount: totalCount.length,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Properties API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new property
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const propertyData = await request.json();

    // Generate slug if not provided
    if (!propertyData.slug) {
      propertyData.slug = propertyData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Calculate price per sqft if not provided
    if (!propertyData.pricePerSqft && propertyData.price && propertyData.totalArea) {
      propertyData.pricePerSqft = propertyData.price / propertyData.totalArea;
    }

    // Auto-generate meta fields if not provided
    if (!propertyData.metaTitle) {
      propertyData.metaTitle = propertyData.title;
    }

    if (!propertyData.metaDescription) {
      propertyData.metaDescription = propertyData.description.substring(0, 160);
    }

    // Set owner as current admin user
    propertyData.ownerId = session.user.id;

    // Set published date if publishing
    if (propertyData.published) {
      propertyData.publishedAt = new Date();
    }

    const newProperty = await db
      .insert(properties)
      .values(propertyData)
      .returning();

    return NextResponse.json(
      {
        message: 'Property created successfully',
        property: newProperty[0],
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Create property error:', error);
    return NextResponse.json(
      { message: 'Failed to create property' },
      { status: 500 }
    );
  }
}