#!/usr/bin/env tsx

// Load environment variables
import dotenv from 'dotenv';
import path from 'path';

// Load .env.local file
dotenv.config({ path: path.join(process.cwd(), '.env.local') });

console.log('🔍 Environment Diagnostics:');
console.log('Current working directory:', process.cwd());
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'Set ✅' : 'Missing ❌');

if (process.env.DATABASE_URL) {
  console.log('Database URL value:', process.env.DATABASE_URL);
  
  // Parse the DATABASE_URL to show components
  try {
    const url = new URL(process.env.DATABASE_URL);
    console.log('DB Host:', url.hostname);
    console.log('DB Port:', url.port);
    console.log('DB Username:', url.username);
    console.log('DB Database:', url.pathname.slice(1));
    console.log('DB Password:', url.password ? '***hidden***' : 'Missing ❌');
  } catch (error) {
    console.log('❌ Invalid DATABASE_URL format:', error);
  }
}

// Test database connection
console.log('\n🔄 Testing database connection...');

try {
  const { db } = await import('./config');
  
  // Simple test query
  const result = await db.execute('SELECT NOW() as current_time, version() as pg_version');
  console.log('✅ Database connection successful!');
  console.log('Current time:', result.rows[0]);
  
  // Test users table
  console.log('\n📋 Testing users table...');
  const usersResult = await db.execute('SELECT COUNT(*) as user_count FROM users');
  console.log('Total users:', usersResult.rows[0]);
  
  // Test admin users
  const adminResult = await db.execute("SELECT email FROM users WHERE role = 'admin' LIMIT 1");
  if (adminResult.rows.length > 0) {
    console.log('✅ Admin user found:', adminResult.rows[0].email);
  } else {
    console.log('❌ No admin users found');
  }
  
  console.log('\n🎉 All database tests passed!');
  
} catch (error) {
  console.error('\n❌ Database connection failed:');
  console.error('Error type:', error.constructor.name);
  console.error('Error message:', error.message);
  
  if (error.cause) {
    console.error('Underlying cause:', error.cause);
  }
  
  console.log('\n🔧 Debugging tips:');
  console.log('1. Ensure PostgreSQL container is running: docker ps');
  console.log('2. Check DATABASE_URL format in .env.local');
  console.log('3. Verify database credentials');
  console.log('4. Test direct connection: docker exec armaan_properties_db psql -U armaan_user -d armaan_properties');
}

process.exit(0);