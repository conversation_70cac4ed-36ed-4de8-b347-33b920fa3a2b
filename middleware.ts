import { withAuth } from 'next-auth/middleware';
import { NextResponse, NextRequest } from 'next/server';
import { checkRateLimit, getRateLimitHeaders } from '@/lib/rate-limit';
import { csrfProtection } from '@/lib/security/csrf';

async function handleRateLimit(req: NextRequest) {
  const pathname = req.nextUrl.pathname;
  
  // Determine rate limit type based on path
  let limitType: 'api' | 'auth' | 'upload' | 'search' | 'contact' | 'admin' = 'api';
  
  if (pathname.startsWith('/api/auth')) {
    limitType = 'auth';
  } else if (pathname.startsWith('/api/upload')) {
    limitType = 'upload';
  } else if (pathname.startsWith('/api/properties') && req.nextUrl.searchParams.has('search')) {
    limitType = 'search';
  } else if (pathname.startsWith('/api/enquiries') || pathname.startsWith('/api/contact')) {
    limitType = 'contact';
  } else if (pathname.startsWith('/api/admin')) {
    limitType = 'admin';
  }
  
  const result = await checkRateLimit(req, limitType);
  
  if (!result.success) {
    const headers = getRateLimitHeaders(result);
    
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: result.retryAfter,
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          ...Object.fromEntries(headers.entries()),
        },
      }
    );
  }
  
  return null; // Continue processing
}

export default withAuth(
  async function middleware(req) {
    // Apply rate limiting to API routes
    if (req.nextUrl.pathname.startsWith('/api/')) {
      try {
        const rateLimitResponse = await handleRateLimit(req);
        if (rateLimitResponse) {
          return rateLimitResponse;
        }
      } catch (error) {
        console.error('Rate limiting error in middleware:', error);
        // Continue processing if rate limiting fails
      }

      // Temporarily disable CSRF protection to fix admin panel issues
      // TODO: Re-implement CSRF protection with proper client-side token handling

      // Apply CSRF protection to non-public API routes (DISABLED)
      const isPublicRoute = true; // Temporarily allow all routes

      if (!isPublicRoute) {
        try {
          const csrfResult = await csrfProtection(req);
          if (!csrfResult.isValid) {
            return new NextResponse(
              JSON.stringify({
                error: 'CSRF Protection',
                message: csrfResult.error || 'CSRF token validation failed',
              }),
              {
                status: 403,
                headers: {
                  'Content-Type': 'application/json',
                },
              }
            );
          }
        } catch (error) {
          console.error('CSRF protection error in middleware:', error);
          // Continue processing if CSRF fails
        }
      }
    }

    // Check if user is admin for admin routes
    if (req.nextUrl.pathname.startsWith('/admin')) {
      if (req.nextauth.token?.role !== 'admin') {
        return NextResponse.redirect(new URL('/auth/signin', req.url));
      }
    }

    // Check if user is authenticated for protected routes
    if (req.nextUrl.pathname.startsWith('/dashboard')) {
      if (!req.nextauth.token) {
        return NextResponse.redirect(new URL('/auth/signin', req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to public routes
        if (req.nextUrl.pathname.startsWith('/api/auth')) {
          return true;
        }

        // Admin routes require admin role
        if (req.nextUrl.pathname.startsWith('/admin')) {
          return token?.role === 'admin';
        }

        // Dashboard routes require authentication
        if (req.nextUrl.pathname.startsWith('/dashboard')) {
          return !!token;
        }

        // Allow access to all other routes
        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    '/admin/:path*',
    '/dashboard/:path*',
    '/api/:path*',
  ],
};