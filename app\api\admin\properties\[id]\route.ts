import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { properties } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET - Get single property
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const property = await db
      .select()
      .from(properties)
      .where(eq(properties.id, params.id))
      .limit(1);

    if (!property[0]) {
      return NextResponse.json(
        { message: 'Property not found' },
        { status: 404 }
      );
    }

    // Convert decimal fields to numbers
    const propertyData = {
      ...property[0],
      price: property[0].price ? parseFloat(property[0].price) : 0,
      pricePerSqft: property[0].pricePerSqft ? parseFloat(property[0].pricePerSqft) : null,
      latitude: property[0].latitude ? parseFloat(property[0].latitude) : null,
      longitude: property[0].longitude ? parseFloat(property[0].longitude) : null,
    };

    return NextResponse.json(propertyData);
  } catch (error) {
    console.error('Get property error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update property
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const propertyData = await request.json();

    // Check if property exists
    const existingProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.id, params.id))
      .limit(1);

    if (!existingProperty[0]) {
      return NextResponse.json(
        { message: 'Property not found' },
        { status: 404 }
      );
    }

    // Update slug if title changed
    if (propertyData.title && propertyData.title !== existingProperty[0].title) {
      propertyData.slug = propertyData.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '');
    }

    // Calculate price per sqft if not provided
    if (!propertyData.pricePerSqft && propertyData.price && propertyData.totalArea) {
      propertyData.pricePerSqft = propertyData.price / propertyData.totalArea;
    }

    // Update meta fields if not provided
    if (!propertyData.metaTitle) {
      propertyData.metaTitle = propertyData.title || existingProperty[0].title;
    }

    if (!propertyData.metaDescription) {
      propertyData.metaDescription = (propertyData.description || existingProperty[0].description)?.substring(0, 160);
    }

    // Set published date if publishing for the first time
    if (propertyData.published && !existingProperty[0].publishedAt) {
      propertyData.publishedAt = new Date();
    }

    // Update timestamp
    propertyData.updatedAt = new Date();

    const updatedProperty = await db
      .update(properties)
      .set(propertyData)
      .where(eq(properties.id, params.id))
      .returning();

    return NextResponse.json({
      message: 'Property updated successfully',
      property: updatedProperty[0],
    });
  } catch (error) {
    console.error('Update property error:', error);
    return NextResponse.json(
      { message: 'Failed to update property' },
      { status: 500 }
    );
  }
}

// DELETE - Delete property
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if property exists
    const existingProperty = await db
      .select()
      .from(properties)
      .where(eq(properties.id, params.id))
      .limit(1);

    if (!existingProperty[0]) {
      return NextResponse.json(
        { message: 'Property not found' },
        { status: 404 }
      );
    }

    await db
      .delete(properties)
      .where(eq(properties.id, params.id));

    return NextResponse.json({
      message: 'Property deleted successfully',
    });
  } catch (error) {
    console.error('Delete property error:', error);
    return NextResponse.json(
      { message: 'Failed to delete property' },
      { status: 500 }
    );
  }
}