import mongoose from 'mongoose'
import { Property } from '@/types/property'

const PropertySchema = new mongoose.Schema<Property>({
  title: {
    type: String,
    required: true,
    trim: true,
  },
  description: {
    type: String,
    required: true,
  },
  price: {
    type: Number,
    required: true,
    min: 0,
  },
  location: {
    area: {
      type: String,
      required: true,
    },
    city: {
      type: String,
      required: true,
    },
    address: {
      type: String,
      required: true,
    },
    pincode: String,
    coordinates: {
      type: [Number],
      index: '2dsphere',
    },
  },
  specifications: {
    bedrooms: {
      type: Number,
      required: true,
      min: 0,
    },
    bathrooms: {
      type: Number,
      required: true,
      min: 0,
    },
    area: {
      type: Number,
      required: true,
      min: 0,
    },
    floors: {
      type: Number,
      min: 0,
    },
    parking: {
      type: Boolean,
      default: false,
    },
    furnished: {
      type: String,
      enum: ['Furnished', 'Semi-Furnished', 'Unfurnished'],
      required: true,
    },
    balconies: {
      type: Number,
      min: 0,
    },
    ageOfProperty: {
      type: Number,
      min: 0,
    },
  },
  images: [{
    type: String,
    required: true,
  }],
  propertyType: {
    type: String,
    enum: ['apartment', 'house', 'builder-floor', 'commercial', 'plot'],
    required: true,
  },
  listingType: {
    type: String,
    enum: ['sale', 'rent'],
    required: true,
  },
  status: {
    type: String,
    enum: ['available', 'sold', 'rented', 'under-negotiation'],
    default: 'available',
  },
  amenities: [{
    type: String,
  }],
  featured: {
    type: Boolean,
    default: false,
  },
  pricePerSqFt: {
    type: Number,
    min: 0,
  },
  contactDetails: {
    phone: {
      type: String,
      required: true,
    },
    email: String,
    whatsapp: String,
  },
  views: {
    type: Number,
    default: 0,
  },
}, {
  timestamps: true,
})

// Create indexes for better search performance
PropertySchema.index({ 'location.area': 1 })
PropertySchema.index({ 'location.city': 1 })
PropertySchema.index({ propertyType: 1 })
PropertySchema.index({ listingType: 1 })
PropertySchema.index({ price: 1 })
PropertySchema.index({ featured: 1 })
PropertySchema.index({ status: 1 })

// Virtual for price per sq ft calculation
PropertySchema.virtual('calculatedPricePerSqFt').get(function() {
  return this.price / this.specifications.area
})

// Pre-save middleware to calculate price per sq ft
PropertySchema.pre('save', function(next) {
  if (this.price && this.specifications?.area) {
    this.pricePerSqFt = Math.round(this.price / this.specifications.area)
  }
  next()
})

const PropertyModel = mongoose.models.Property || mongoose.model<Property>('Property', PropertySchema)

export default PropertyModel 