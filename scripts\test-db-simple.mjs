#!/usr/bin/env node

/**
 * Simple Database Connection Test
 * Tests database connectivity without requiring TypeScript compilation
 */

import { createRequire } from 'module';
import path from 'path';
import { fileURLToPath } from 'url';

const require = createRequire(import.meta.url);
const dotenv = require('dotenv');

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const nodeEnv = process.env.NODE_ENV || 'development';
console.log(`🔍 Testing database connection for: ${nodeEnv}`);

if (nodeEnv === 'production') {
  dotenv.config({ path: path.join(process.cwd(), '.env.production') });
  console.log('📁 Loaded .env.production');
} else {
  dotenv.config({ path: path.join(process.cwd(), '.env.local') });
  console.log('📁 Loaded .env.local');
}

// Define database URLs for different environments
const DATABASE_URLS = {
  development: 'postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties',
  production: 'postgresql://yash123414:<EMAIL>:5432/armaan_properties',
  test: 'postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties_test'
};

// Determine environment and database URL
let environment = nodeEnv;
let databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  databaseUrl = DATABASE_URLS[environment] || DATABASE_URLS.development;
  console.log(`⚠️  DATABASE_URL not set, using default for ${environment}`);
}

// Additional check for production environment detection
if (process.env.NEXTAUTH_URL === 'https://property-trendz.com' && environment !== 'production') {
  environment = 'production';
  databaseUrl = DATABASE_URLS.production;
  console.log('🔄 Detected production environment from NEXTAUTH_URL');
}

console.log('\n🔗 Database Configuration:');
console.log('Environment:', environment);
console.log('Database URL:', databaseUrl.replace(/:[^:@]*@/, ':***@')); // Hide password

// Parse URL to show components
try {
  const url = new URL(databaseUrl);
  console.log('Host:', url.hostname);
  console.log('Port:', url.port || '5432');
  console.log('Database:', url.pathname.slice(1));
  console.log('Username:', url.username);
  console.log('Docker:', url.hostname === 'localhost' ? 'Yes' : 'No');
} catch (error) {
  console.error('❌ Invalid DATABASE_URL format:', error.message);
  process.exit(1);
}

// Test database connection
console.log('\n🔌 Testing Database Connection...');

async function testConnection() {
  try {
    // Import postgres
    const postgres = (await import('postgres')).default;
    
    // Create connection
    const sql = postgres(databaseUrl, {
      max: 1, // Only one connection for testing
      idle_timeout: 5,
      connect_timeout: 10,
    });
    
    // Test query
    const result = await sql`SELECT NOW() as current_time, version() as pg_version`;
    console.log('✅ Database connection successful!');
    console.log('Current time:', result[0].current_time);
    console.log('PostgreSQL version:', result[0].pg_version.split(' ')[0] + ' ' + result[0].pg_version.split(' ')[1]);
    
    // Test basic tables
    try {
      const usersResult = await sql`SELECT COUNT(*) as user_count FROM users`;
      console.log('Total users:', usersResult[0].user_count);
    } catch (error) {
      console.log('⚠️  Users table not accessible (may not exist yet)');
    }
    
    try {
      const propertiesResult = await sql`SELECT COUNT(*) as property_count FROM properties`;
      console.log('Total properties:', propertiesResult[0].property_count);
    } catch (error) {
      console.log('⚠️  Properties table not accessible (may not exist yet)');
    }
    
    // Close connection
    await sql.end();
    
    console.log('\n🎉 Database test completed successfully!');
    
    // Environment validation
    console.log('\n📊 Environment Validation:');
    const isDocker = url.hostname === 'localhost';
    const expectedDocker = environment === 'development';
    
    console.log(`Expected for ${environment}:`, expectedDocker ? 'Docker (localhost)' : 'Production server');
    console.log(`Actual:`, isDocker ? 'Docker (localhost)' : 'Production server');
    console.log(`Match:`, (expectedDocker === isDocker) ? '✅' : '❌');
    
    if (expectedDocker !== isDocker) {
      console.log('\n⚠️  Environment Mismatch Warning:');
      if (environment === 'development' && !isDocker) {
        console.log('- You are in development but using production database');
        console.log('- Consider using Docker for development');
      } else if (environment === 'production' && isDocker) {
        console.log('- You are in production but using Docker database');
        console.log('- Ensure you are using the production database server');
      }
    }
    
  } catch (error) {
    console.error('\n❌ Database connection failed:');
    console.error('Error:', error.message);
    
    console.log('\n🔧 Troubleshooting Suggestions:');
    
    const isDocker = databaseUrl.includes('localhost');
    if (isDocker) {
      console.log('- Ensure Docker PostgreSQL container is running');
      console.log('- Check: docker ps | grep postgres');
      console.log('- Start container: docker-compose up -d postgres');
      console.log('- Check container logs: docker logs armaan_properties_db');
    } else {
      console.log('- Verify production database server is accessible');
      console.log('- Check network connectivity: ping srv949995.hstar.cloud');
      console.log('- Verify database credentials and permissions');
      console.log('- Check firewall rules');
    }
    
    console.log('- Verify DATABASE_URL format is correct');
    console.log('- Check environment file is loaded correctly');
    
    process.exit(1);
  }
}

testConnection().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
