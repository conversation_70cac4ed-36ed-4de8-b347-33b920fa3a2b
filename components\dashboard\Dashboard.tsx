'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  Edit3, 
  Save, 
  X, 
  Heart, 
  Search, 
  Scale, 
  Settings,
  Eye,
  MessageSquare,
  TrendingUp,
  Home as HomeIcon
} from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

interface UserProfile {
  name: string;
  email: string;
  phone?: string;
  location?: string;
  bio?: string;
  joinedAt: string;
}

interface UserStats {
  favorites: number;
  savedSearches: number;
  comparisons: number;
  propertyViews: number;
  enquiries: number;
}

export function Dashboard() {
  const { data: session, status } = useSession();
  const [isEditing, setIsEditing] = useState(false);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userStats, setUserStats] = useState<UserStats>({
    favorites: 0,
    savedSearches: 0,
    comparisons: 0,
    propertyViews: 0,
    enquiries: 0
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch user profile and stats
  useEffect(() => {
    if (session?.user) {
      // Initialize with session data
      setUserProfile({
        name: session.user.name || '',
        email: session.user.email || '',
        phone: '',
        location: '',
        bio: '',
        joinedAt: new Date().toISOString().split('T')[0] // Current date
      });
      
      // Fetch actual user stats from API
      fetchUserStats();
      
      setLoading(false);
    }
  }, [session]);

  const fetchUserStats = async () => {
    try {
      // For now, set default stats - can be enhanced with real API calls
      setUserStats({
        favorites: 0,
        savedSearches: 0,
        comparisons: 0,
        propertyViews: 0,
        enquiries: 0
      });
    } catch (error) {
      console.error('Error fetching user stats:', error);
      // Set default stats on error
      setUserStats({
        favorites: 0,
        savedSearches: 0,
        comparisons: 0,
        propertyViews: 0,
        enquiries: 0
      });
    }
  };

  const handleSaveProfile = async () => {
    if (!userProfile) return;

    setSaving(true);
    try {
      // Profile saving functionality - currently updates local state only
      // In a full implementation, this would call an API to update user profile
      setIsEditing(false);

      // Show success message
      if (typeof window !== 'undefined' && window.showToast) {
        window.showToast('Profile updated successfully');
      }
    } catch (error) {
      console.error('Error saving profile:', error);
      if (typeof window !== 'undefined' && window.showToast) {
        window.showToast('Failed to update profile');
      }
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof UserProfile, value: string) => {
    if (!userProfile) return;
    setUserProfile({
      ...userProfile,
      [field]: value
    });
  };

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-8">You need to be signed in to access your dashboard.</p>
          <Link href="/auth/signin">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!userProfile) return null;

  const statCards = [
    { icon: Heart, label: 'Favorites', value: userStats.favorites, color: 'text-red-500', href: '/favorites' },
    { icon: Search, label: 'Saved Searches', value: userStats.savedSearches, color: 'text-blue-500', href: '/saved-searches' },
    { icon: Scale, label: 'Comparisons', value: userStats.comparisons, color: 'text-green-500', href: '/compare' },
    { icon: Eye, label: 'Property Views', value: userStats.propertyViews, color: 'text-purple-500', href: '#' },
    { icon: MessageSquare, label: 'Enquiries', value: userStats.enquiries, color: 'text-orange-500', href: '#' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container-custom py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
              <p className="text-gray-600 mt-1">Manage your account and preferences</p>
            </div>
            {session.user.role === 'admin' && (
              <Link href="/admin">
                <Button variant="outline" className="flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Admin Panel
                </Button>
              </Link>
            )}
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Information */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">Profile Information</h2>
                {!isEditing ? (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setIsEditing(true)}
                    className="flex items-center gap-2"
                  >
                    <Edit3 className="w-4 h-4" />
                    Edit
                  </Button>
                ) : (
                  <div className="flex gap-2">
                    <Button 
                      size="sm"
                      onClick={handleSaveProfile}
                      disabled={saving}
                      className="flex items-center gap-2"
                    >
                      <Save className="w-4 h-4" />
                      {saving ? 'Saving...' : 'Save'}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => setIsEditing(false)}
                      className="flex items-center gap-2"
                    >
                      <X className="w-4 h-4" />
                      Cancel
                    </Button>
                  </div>
                )}
              </div>

              <div className="space-y-6">
                {/* Avatar and Basic Info */}
                <div className="flex items-center space-x-4">
                  {session.user.image ? (
                    <img 
                      src={session.user.image} 
                      alt={userProfile.name} 
                      className="w-20 h-20 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-20 h-20 bg-primary-100 rounded-full flex items-center justify-center">
                      <User className="w-10 h-10 text-primary-600" />
                    </div>
                  )}
                  <div className="flex-1">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Full Name
                        </label>
                        {isEditing ? (
                          <input
                            type="text"
                            value={userProfile.name}
                            onChange={(e) => handleInputChange('name', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                          />
                        ) : (
                          <p className="text-gray-900 py-2">{userProfile.name}</p>
                        )}
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email
                        </label>
                        <p className="text-gray-900 py-2 flex items-center gap-2">
                          <Mail className="w-4 h-4 text-gray-400" />
                          {userProfile.email}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Additional Fields */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Phone Number
                    </label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={userProfile.phone || ''}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="+91 9876543210"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900 py-2 flex items-center gap-2">
                        <Phone className="w-4 h-4 text-gray-400" />
                        {userProfile.phone || 'Not provided'}
                      </p>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Location
                    </label>
                    {isEditing ? (
                      <input
                        type="text"
                        value={userProfile.location || ''}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        placeholder="Delhi NCR"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    ) : (
                      <p className="text-gray-900 py-2 flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        {userProfile.location || 'Not provided'}
                      </p>
                    )}
                  </div>
                </div>

                {/* Bio */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bio
                  </label>
                  {isEditing ? (
                    <textarea
                      value={userProfile.bio || ''}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Tell us about yourself..."
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    />
                  ) : (
                    <p className="text-gray-900 py-2">
                      {userProfile.bio || 'No bio provided'}
                    </p>
                  )}
                </div>

                {/* Join Date */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Member Since
                  </label>
                  <p className="text-gray-900 py-2 flex items-center gap-2">
                    <Calendar className="w-4 h-4 text-gray-400" />
                    {new Date(userProfile.joinedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Stats and Quick Actions */}
          <div className="space-y-6">
            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <TrendingUp className="w-5 h-5" />
                Your Activity
              </h3>
              <div className="space-y-4">
                {statCards.map((stat, index) => (
                  <div key={stat.label} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <stat.icon className={`w-5 h-5 ${stat.color}`} />
                      <span className="text-gray-700">{stat.label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold text-gray-900">{stat.value}</span>
                      {stat.href !== '#' && (
                        <Link href={stat.href}>
                          <Button variant="ghost" size="sm" className="p-1 h-auto">
                            <HomeIcon className="w-3 h-3" />
                          </Button>
                        </Link>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="bg-white rounded-lg shadow-sm border p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <Link href="/favorites" className="block">
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Heart className="w-4 h-4" />
                    View Favorites
                  </Button>
                </Link>
                <Link href="/saved-searches" className="block">
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Search className="w-4 h-4" />
                    Saved Searches
                  </Button>
                </Link>
                <Link href="/compare" className="block">
                  <Button variant="outline" className="w-full justify-start gap-2">
                    <Scale className="w-4 h-4" />
                    Compare Properties
                  </Button>
                </Link>
                <Link href="/properties" className="block">
                  <Button className="w-full justify-start gap-2">
                    <HomeIcon className="w-4 h-4" />
                    Browse Properties
                  </Button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}