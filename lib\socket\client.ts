'use client';

import { useEffect, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  senderName: string;
  senderRole: 'user' | 'admin' | 'agent';
  message: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  propertyId?: string;
  isRead: boolean;
}

interface ChatRoom {
  id: string;
  propertyId?: string;
  userId: string;
  userName: string;
  userEmail: string;
  status: 'active' | 'closed' | 'pending';
  lastMessage?: string;
  lastMessageAt?: Date;
  unreadCount: number;
  createdAt: Date;
}

interface TypingUser {
  userId: string;
  userName: string;
  isTyping: boolean;
}

interface UseSocketOptions {
  autoConnect?: boolean;
  serverPath?: string;
}

export function useSocket(options: UseSocketOptions = {}) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [currentRoom, setCurrentRoom] = useState<string | null>(null);
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<string[]>([]);

  const {
    autoConnect = true,
    serverPath = '/api/socket'
  } = options;

  // Initialize socket connection
  useEffect(() => {
    if (!autoConnect) return;

    const socketUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://property-trendz.com';
    
    const newSocket = io(socketUrl, {
      path: serverPath,
      transports: ['websocket', 'polling'],
      autoConnect: true,
    });

    // Connection events
    newSocket.on('connect', () => {
      console.log('Connected to socket server');
      setIsConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from socket server');
      setIsConnected(false);
    });

    // Chat events
    newSocket.on('new-message', (message: ChatMessage) => {
      setMessages(prev => [...prev, message]);
    });

    newSocket.on('previous-messages', (messagesList: ChatMessage[]) => {
      setMessages(messagesList);
    });

    newSocket.on('user-typing', (data: TypingUser) => {
      setTypingUsers(prev => {
        const filtered = prev.filter(user => user.userId !== data.userId);
        return data.isTyping ? [...filtered, data] : filtered;
      });
    });

    newSocket.on('user-joined', (data: { userId: string; userName: string }) => {
      setOnlineUsers(prev => 
        prev.includes(data.userId) ? prev : [...prev, data.userId]
      );
    });

    newSocket.on('error', (error: { message: string }) => {
      console.error('Socket error:', error.message);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [autoConnect, serverPath]);

  // Join a room
  const joinRoom = useCallback((roomId: string, userId: string, userName: string) => {
    if (socket && socket.connected) {
      socket.emit('join-room', { roomId, userId, userName });
      setCurrentRoom(roomId);
      setMessages([]); // Clear previous messages
    }
  }, [socket]);

  // Leave current room
  const leaveRoom = useCallback(() => {
    if (socket && currentRoom) {
      socket.emit('leave-room', currentRoom);
      setCurrentRoom(null);
      setMessages([]);
      setTypingUsers([]);
    }
  }, [socket, currentRoom]);

  // Send a message
  const sendMessage = useCallback((data: {
    roomId: string;
    senderId: string;
    senderName: string;
    senderRole: 'user' | 'admin' | 'agent';
    message: string;
    type?: 'text' | 'image' | 'file';
    propertyId?: string;
  }) => {
    if (socket && socket.connected) {
      socket.emit('send-message', {
        ...data,
        type: data.type || 'text'
      });
    }
  }, [socket]);

  // Typing indicators
  const startTyping = useCallback((roomId: string, userId: string, userName: string) => {
    if (socket && socket.connected) {
      socket.emit('typing-start', { roomId, userId, userName });
    }
  }, [socket]);

  const stopTyping = useCallback((roomId: string, userId: string) => {
    if (socket && socket.connected) {
      socket.emit('typing-stop', { roomId, userId });
    }
  }, [socket]);

  // Admin functions
  const joinAsAdmin = useCallback((adminId: string, adminName: string) => {
    if (socket && socket.connected) {
      socket.emit('admin-join', { adminId, adminName });
    }
  }, [socket]);

  const getActiveRooms = useCallback(() => {
    if (socket && socket.connected) {
      socket.emit('get-active-rooms');
    }
  }, [socket]);

  return {
    socket,
    isConnected,
    messages,
    currentRoom,
    typingUsers,
    onlineUsers,
    joinRoom,
    leaveRoom,
    sendMessage,
    startTyping,
    stopTyping,
    joinAsAdmin,
    getActiveRooms,
  };
}

// Hook for creating chat rooms
export function useChatRoom() {
  const createRoom = useCallback(async (data: {
    userId: string;
    userName: string;
    userEmail: string;
    propertyId?: string;
  }): Promise<string> => {
    const response = await fetch('/api/socket', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'create-room',
        data,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to create chat room');
    }

    const result = await response.json();
    return result.roomId;
  }, []);

  const getRoomMessages = useCallback(async (roomId: string): Promise<ChatMessage[]> => {
    const response = await fetch('/api/socket', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'get-messages',
        data: { roomId },
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to get room messages');
    }

    const result = await response.json();
    return result.messages;
  }, []);

  return {
    createRoom,
    getRoomMessages,
  };
}