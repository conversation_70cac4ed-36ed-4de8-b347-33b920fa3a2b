import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { db } from '@/lib/db/config';
import { favorites, properties } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';

// GET /api/favorites - Get user's favorite properties
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's favorites with property details
    const userFavorites = await db
      .select({
        favoriteId: favorites.id,
        createdAt: favorites.createdAt,
        property: {
          id: properties.id,
          title: properties.title,
          slug: properties.slug,
          price: properties.price,
          area: properties.area,
          city: properties.city,
          propertyType: properties.propertyType,
          listingType: properties.listingType,
          bedrooms: properties.bedrooms,
          bathrooms: properties.bathrooms,
          totalArea: properties.totalArea,
          images: properties.images,
          status: properties.status,
        }
      })
      .from(favorites)
      .innerJoin(properties, eq(favorites.propertyId, properties.id))
      .where(eq(favorites.userId, session.user.id))
      .orderBy(desc(favorites.createdAt));

    return NextResponse.json({ 
      favorites: userFavorites,
      total: userFavorites.length 
    });

  } catch (error) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { error: 'Failed to fetch favorites' }, 
      { status: 500 }
    );
  }
}

// POST /api/favorites - Add property to favorites
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { propertyId } = await request.json();

    if (!propertyId) {
      return NextResponse.json({ error: 'Property ID required' }, { status: 400 });
    }

    // Check if property exists
    const property = await db
      .select({ id: properties.id })
      .from(properties)
      .where(eq(properties.id, propertyId))
      .limit(1);

    if (property.length === 0) {
      return NextResponse.json({ error: 'Property not found' }, { status: 404 });
    }

    // Check if already in favorites
    const existingFavorite = await db
      .select({ id: favorites.id })
      .from(favorites)
      .where(and(
        eq(favorites.userId, session.user.id),
        eq(favorites.propertyId, propertyId)
      ))
      .limit(1);

    if (existingFavorite.length > 0) {
      return NextResponse.json({ error: 'Property already in favorites' }, { status: 409 });
    }

    // Add to favorites
    const newFavorite = await db
      .insert(favorites)
      .values({
        userId: session.user.id,
        propertyId: propertyId,
      })
      .returning();

    return NextResponse.json({ 
      message: 'Property added to favorites',
      favorite: newFavorite[0] 
    });

  } catch (error) {
    console.error('Error adding to favorites:', error);
    return NextResponse.json(
      { error: 'Failed to add to favorites' }, 
      { status: 500 }
    );
  }
}

// DELETE /api/favorites - Remove property from favorites
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const propertyId = searchParams.get('propertyId');

    if (!propertyId) {
      return NextResponse.json({ error: 'Property ID required' }, { status: 400 });
    }

    // Remove from favorites
    const deletedFavorite = await db
      .delete(favorites)
      .where(and(
        eq(favorites.userId, session.user.id),
        eq(favorites.propertyId, propertyId)
      ))
      .returning();

    if (deletedFavorite.length === 0) {
      return NextResponse.json({ error: 'Favorite not found' }, { status: 404 });
    }

    return NextResponse.json({ 
      message: 'Property removed from favorites',
      favorite: deletedFavorite[0] 
    });

  } catch (error) {
    console.error('Error removing from favorites:', error);
    return NextResponse.json(
      { error: 'Failed to remove from favorites' }, 
      { status: 500 }
    );
  }
}