'use client';

import { useState, useCallback, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface RangeSliderProps {
  min: number;
  max: number;
  step?: number;
  value: [number, number];
  onValueChange: (value: [number, number]) => void;
  formatValue?: (value: number) => string;
  className?: string;
  disabled?: boolean;
  label?: string;
}

export function RangeSlider({
  min,
  max,
  step = 1,
  value,
  onValueChange,
  formatValue = (val) => val.toString(),
  className,
  disabled = false,
  label
}: RangeSliderProps) {
  const [localValue, setLocalValue] = useState<[number, number]>(value);
  const [isDragging, setIsDragging] = useState<'min' | 'max' | null>(null);

  useEffect(() => {
    setLocalValue(value);
  }, [value]);

  const getPercentage = useCallback((val: number) => {
    return ((val - min) / (max - min)) * 100;
  }, [min, max]);

  const getValue = useCallback((percentage: number) => {
    const val = min + (percentage / 100) * (max - min);
    return Math.round(val / step) * step;
  }, [min, max, step]);

  const handleMouseDown = (type: 'min' | 'max') => (e: React.MouseEvent) => {
    if (disabled) return;
    setIsDragging(type);
    e.preventDefault();
  };

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || disabled) return;

    const slider = document.getElementById('range-slider-track');
    if (!slider) return;

    const rect = slider.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(100, ((e.clientX - rect.left) / rect.width) * 100));
    const newValue = getValue(percentage);

    setLocalValue(prev => {
      let newRange: [number, number];
      
      if (isDragging === 'min') {
        newRange = [Math.min(newValue, prev[1] - step), prev[1]];
      } else {
        newRange = [prev[0], Math.max(newValue, prev[0] + step)];
      }
      
      // Ensure values are within bounds
      newRange[0] = Math.max(min, Math.min(max, newRange[0]));
      newRange[1] = Math.max(min, Math.min(max, newRange[1]));
      
      return newRange;
    });
  }, [isDragging, disabled, getValue, step, min, max]);

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(null);
      onValueChange(localValue);
    }
  }, [isDragging, localValue, onValueChange]);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  const handleTrackClick = (e: React.MouseEvent) => {
    if (disabled || isDragging) return;

    const slider = e.currentTarget;
    const rect = slider.getBoundingClientRect();
    const percentage = ((e.clientX - rect.left) / rect.width) * 100;
    const clickValue = getValue(percentage);

    // Determine which handle is closer to the click
    const distanceToMin = Math.abs(clickValue - localValue[0]);
    const distanceToMax = Math.abs(clickValue - localValue[1]);
    
    const newValue: [number, number] = [...localValue];
    
    if (distanceToMin < distanceToMax) {
      newValue[0] = Math.min(clickValue, localValue[1] - step);
    } else {
      newValue[1] = Math.max(clickValue, localValue[0] + step);
    }

    // Ensure values are within bounds
    newValue[0] = Math.max(min, Math.min(max, newValue[0]));
    newValue[1] = Math.max(min, Math.min(max, newValue[1]));

    setLocalValue(newValue);
    onValueChange(newValue);
  };

  const minPercentage = getPercentage(localValue[0]);
  const maxPercentage = getPercentage(localValue[1]);

  return (
    <div className={cn('w-full', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label}
        </label>
      )}
      
      <div className="relative">
        {/* Track */}
        <div
          id="range-slider-track"
          className={cn(
            'relative h-2 bg-gray-200 rounded-full cursor-pointer',
            disabled && 'cursor-not-allowed opacity-50'
          )}
          onClick={handleTrackClick}
        >
          {/* Active range */}
          <div
            className="absolute h-2 bg-primary-500 rounded-full"
            style={{
              left: `${minPercentage}%`,
              width: `${maxPercentage - minPercentage}%`,
            }}
          />
          
          {/* Min handle */}
          <div
            className={cn(
              'absolute w-5 h-5 bg-white border-2 border-primary-500 rounded-full cursor-grab transform -translate-y-1/2 -translate-x-1/2 shadow-md transition-shadow',
              isDragging === 'min' && 'cursor-grabbing shadow-lg scale-110',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            style={{
              left: `${minPercentage}%`,
              top: '50%',
            }}
            onMouseDown={handleMouseDown('min')}
          />
          
          {/* Max handle */}
          <div
            className={cn(
              'absolute w-5 h-5 bg-white border-2 border-primary-500 rounded-full cursor-grab transform -translate-y-1/2 -translate-x-1/2 shadow-md transition-shadow',
              isDragging === 'max' && 'cursor-grabbing shadow-lg scale-110',
              disabled && 'cursor-not-allowed opacity-50'
            )}
            style={{
              left: `${maxPercentage}%`,
              top: '50%',
            }}
            onMouseDown={handleMouseDown('max')}
          />
        </div>
        
        {/* Value display */}
        <div className="flex justify-between mt-2 text-sm text-gray-600">
          <span>{formatValue(localValue[0])}</span>
          <span>{formatValue(localValue[1])}</span>
        </div>
      </div>
    </div>
  );
}

// Preset slider configurations for common use cases
export const sliderPresets = {
  price: {
    min: 0,
    max: 50000000, // 5 Crores
    step: 100000, // 1 Lakh
    formatValue: (value: number) => {
      if (value >= 10000000) {
        return `₹${(value / 10000000).toFixed(1)}Cr`;
      } else if (value >= 100000) {
        return `₹${(value / 100000).toFixed(0)}L`;
      } else if (value >= 1000) {
        return `₹${(value / 1000).toFixed(0)}K`;
      }
      return `₹${value}`;
    }
  },
  area: {
    min: 100,
    max: 10000,
    step: 50,
    formatValue: (value: number) => `${value} sq ft`
  },
  bedrooms: {
    min: 1,
    max: 10,
    step: 1,
    formatValue: (value: number) => `${value} BHK`
  },
  bathrooms: {
    min: 1,
    max: 10,
    step: 1,
    formatValue: (value: number) => `${value} Bath`
  }
};

// Utility function to create a price range slider
export function PriceRangeSlider({
  value,
  onValueChange,
  className,
  disabled
}: {
  value: [number, number];
  onValueChange: (value: [number, number]) => void;
  className?: string;
  disabled?: boolean;
}) {
  return (
    <RangeSlider
      {...sliderPresets.price}
      value={value}
      onValueChange={onValueChange}
      className={className}
      disabled={disabled}
      label="Price Range"
    />
  );
}

// Utility function to create an area range slider
export function AreaRangeSlider({
  value,
  onValueChange,
  className,
  disabled
}: {
  value: [number, number];
  onValueChange: (value: [number, number]) => void;
  className?: string;
  disabled?: boolean;
}) {
  return (
    <RangeSlider
      {...sliderPresets.area}
      value={value}
      onValueChange={onValueChange}
      className={className}
      disabled={disabled}
      label="Area Range"
    />
  );
}