import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { enquiries, properties } from '@/lib/db/schema';
import { eq, sql } from 'drizzle-orm';
import { withValidation } from '@/lib/validation/middleware';
import { enquirySchema } from '@/lib/validation/schemas';

export const POST = withValidation(
  async (request: NextRequest, { body }) => {
    try {
      const { name, email, phone, message, propertyId, enquiryType, budget } = body!;

      // Verify property exists
      const property = await db
        .select({ id: properties.id, title: properties.title })
        .from(properties)
        .where(eq(properties.id, propertyId))
        .limit(1);

      if (!property[0]) {
        return NextResponse.json(
          { message: 'Property not found' },
          { status: 404 }
        );
      }

      // Create enquiry
      const newEnquiry = await db
        .insert(enquiries)
        .values({
          name,
          email,
          phone,
          message: message || `Interested in: ${property[0].title}`,
          propertyId,
          enquiryType: enquiryType || 'general',
          budget: budget ? String(budget) : null,
          status: 'new',
          source: 'website',
        })
        .returning();

      // Update enquiry count on property
      await db
        .update(properties)
        .set({ 
          enquiries: sql`${properties.enquiries} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(properties.id, propertyId));

      // Here you could also send email notifications to admin/agent
      // TODO: Implement email notification

      return NextResponse.json(
        {
          message: 'Enquiry submitted successfully',
          enquiry: newEnquiry[0],
        },
        { status: 201 }
      );
    } catch (error) {
      console.error('Enquiry submission error:', error);
      return NextResponse.json(
        { message: 'Failed to submit enquiry' },
        { status: 500 }
      );
    }
  },
  {
    body: enquirySchema,
  }
);