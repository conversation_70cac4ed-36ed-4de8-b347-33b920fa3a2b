import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
// import { seedProperties } from '@/lib/db/seed-properties'; // Temporarily disabled due to TypeScript issues

// POST /api/admin/seed - Seed database with dummy properties (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    console.log(`🔒 Admin ${session.user.email} initiated property seeding`);

    // Parse request body for options
    const body = await request.json().catch(() => ({}));
    const { force = false } = body;

    // Run the seeding function
    // const result = await seedProperties(); // Temporarily disabled
    return NextResponse.json({
      error: 'Seeding temporarily disabled',
      message: 'Property seeding is currently disabled due to ongoing maintenance',
    }, { status: 503 });

  } catch (error) {
    console.error('❌ Property seeding API error:', error);

    // Handle specific error types
    if (error instanceof Error) {
      if (error.message.includes('No admin user found')) {
        return NextResponse.json(
          {
            error: 'Admin user not found',
            message: 'Please ensure an admin user exists before seeding properties',
          },
          { status: 400 }
        );
      }

      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        return NextResponse.json(
          {
            error: 'Duplicate data detected',
            message: 'Some properties may already exist. Use force=true to override.',
          },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      {
        error: 'Seeding failed',
        message: 'An error occurred while seeding properties',
        details: process.env.NODE_ENV === 'development' ? error : undefined,
      },
      { status: 500 }
    );
  }
}

// GET /api/admin/seed - Get seeding status and information
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    // You could add logic here to check current property count
    // and provide seeding recommendations

    return NextResponse.json({
      message: 'Property seeding endpoint ready',
      instructions: {
        post: 'Send POST request to seed dummy properties',
        parameters: {
          force: 'boolean - Force seed even if properties exist (optional)',
        },
      },
      dummyDataInfo: {
        propertiesCount: 10,
        types: ['apartment', 'villa', 'office', 'penthouse', 'builder-floor', 'shop', 'studio', 'farmhouse'],
        locations: ['Gurgaon', 'Noida', 'Delhi', 'Faridabad'],
        priceRange: '₹28L - ₹7.5Cr',
        features: ['All property types', 'Realistic prices', 'Multiple images', 'Complete amenities'],
      },
    });

  } catch (error) {
    console.error('❌ Seeding info API error:', error);
    return NextResponse.json(
      { error: 'Failed to get seeding information' },
      { status: 500 }
    );
  }
}