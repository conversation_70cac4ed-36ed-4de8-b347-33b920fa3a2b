{"name": "property-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p ${PORT:-3000}", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:seed": "tsx lib/db/migrate.ts", "db:test": "node scripts/test-db-simple.mjs", "db:test-old": "node lib/db/test-connection.mjs", "db:validate": "node scripts/validate-db-config.mjs", "seed": "node scripts/seed-db.js", "seed-properties": "tsx lib/db/seed-properties.ts", "set-admin": "tsx scripts/set-admin.ts", "deploy:build": "npm ci --only=production && npm run build", "deploy:start": "pm2 start ecosystem.config.js --env production", "deploy:stop": "pm2 stop ecosystem.config.js", "deploy:restart": "pm2 restart ecosystem.config.js", "deploy:logs": "pm2 logs", "docker:build": "docker build -t properties-trends .", "docker:run": "docker run -p 3000:3000 --env-file .env.production properties-trends"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@next/third-parties": "^15.4.5", "@paralleldrive/cuid2": "^2.2.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "@types/pg": "^8.15.5", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "autoprefixer": "^10.4.16", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^1.41.0", "clsx": "^2.1.1", "dompurify": "^3.2.6", "dotenv": "^16.4.5", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "framer-motion": "^10.16.5", "jsdom": "^26.1.0", "lottie-react": "^2.4.0", "lucide-react": "^0.294.0", "mongoose": "^8.0.3", "multer": "^2.0.2", "next": "^14.2.31", "next-auth": "^4.24.11", "next-themes": "^0.2.1", "node-cron": "^4.2.1", "nodemailer": "^6.10.1", "pg": "^8.16.3", "postcss": "^8.4.32", "postgres": "^3.4.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-image-gallery": "^1.3.0", "react-intersection-observer": "^9.5.3", "sharp": "^0.34.3", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwindcss": "^3.3.6", "tsx": "^4.20.3", "validator": "^13.15.15", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^20.9.2", "@types/nodemailer": "^6.4.17", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "eslint": "^8.54.0", "eslint-config-next": "14.0.3", "typescript": "^5.3.2"}}