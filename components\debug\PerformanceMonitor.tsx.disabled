'use client';

import React, { useState, useEffect } from 'react';
import { useMemoryMonitor } from '@/lib/performance/optimization';

interface PerformanceMonitorProps {
  enabled?: boolean;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export function PerformanceMonitor({ 
  enabled = process.env.NODE_ENV === 'development',
  position = 'bottom-right' 
}: PerformanceMonitorProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [fps, setFps] = useState(0);
  const [loadTime, setLoadTime] = useState(0);
  const memoryInfo = useMemoryMonitor();

  useEffect(() => {
    if (!enabled) return;

    // Calculate page load time
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      setLoadTime(Math.round(navigationEntry.loadEventEnd - navigationEntry.startTime));
    }

    // FPS monitoring
    let frameCount = 0;
    let lastTime = performance.now();
    let animationId: number;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime >= lastTime + 1000) {
        setFps(Math.round((frameCount * 1000) / (currentTime - lastTime)));
        frameCount = 0;
        lastTime = currentTime;
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };

    measureFPS();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [enabled]);

  if (!enabled) return null;

  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };

  return (
    <div className={`fixed ${positionClasses[position]} z-50`}>
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="bg-black/80 text-white text-xs px-2 py-1 rounded mb-2 hover:bg-black/90 transition-colors"
      >
        📊 Perf
      </button>
      
      {isVisible && (
        <div className="bg-black/90 text-white text-xs p-3 rounded-lg font-mono min-w-[200px] space-y-1">
          <div className="font-bold border-b border-white/20 pb-1 mb-2">
            Performance Monitor
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <div>FPS:</div>
            <div className={fps < 30 ? 'text-red-400' : fps < 50 ? 'text-yellow-400' : 'text-green-400'}>
              {fps}
            </div>
            
            <div>Load:</div>
            <div className={loadTime > 3000 ? 'text-red-400' : loadTime > 1000 ? 'text-yellow-400' : 'text-green-400'}>
              {loadTime}ms
            </div>
            
            <div>Memory:</div>
            <div className={memoryInfo.memoryUsage > 80 ? 'text-red-400' : memoryInfo.memoryUsage > 60 ? 'text-yellow-400' : 'text-green-400'}>
              {memoryInfo.usedMemory}MB
            </div>
            
            <div>Usage:</div>
            <div className={memoryInfo.memoryUsage > 80 ? 'text-red-400' : memoryInfo.memoryUsage > 60 ? 'text-yellow-400' : 'text-green-400'}>
              {memoryInfo.memoryUsage}%
            </div>
          </div>
          
          <div className="border-t border-white/20 pt-2 mt-2">
            <div className="text-[10px] text-white/60">
              Click to toggle • DEV only
            </div>
          </div>
        </div>
      )}
    </div>
  );
}