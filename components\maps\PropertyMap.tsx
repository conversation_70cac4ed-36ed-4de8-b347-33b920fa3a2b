'use client';

import { useEffect, useRef, useState } from 'react';
import { MapPin, Navigation, Maximize2, Minimize2 } from 'lucide-react';

interface PropertyMapProps {
  latitude: number;
  longitude: number;
  title?: string;
  address?: string;
  className?: string;
  height?: string;
  showFullscreenButton?: boolean;
  showDirectionsButton?: boolean;
  zoom?: number;
}

declare global {
  interface Window {
    google: any;
    initMap: () => void;
  }
}

export function PropertyMap({
  latitude,
  longitude,
  title = 'Property Location',
  address,
  className = '',
  height = '400px',
  showFullscreenButton = true,
  showDirectionsButton = true,
  zoom = 15
}: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<any>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load Google Maps script
  useEffect(() => {
    if (window.google) {
      initializeMap();
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      initializeMap();
    };
    
    script.onerror = () => {
      setError('Failed to load Google Maps');
      setIsLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  const initializeMap = () => {
    if (!mapRef.current || !window.google) return;

    try {
      const mapOptions = {
        center: { lat: latitude, lng: longitude },
        zoom: zoom,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        zoomControl: true,
        streetViewControl: true,
        fullscreenControl: false, // We'll handle this ourselves
        mapTypeControl: true,
        styles: [
          {
            featureType: 'poi.business',
            stylers: [{ visibility: 'off' }]
          },
          {
            featureType: 'transit',
            elementType: 'labels.icon',
            stylers: [{ visibility: 'off' }]
          }
        ]
      };

      const newMap = new window.google.maps.Map(mapRef.current, mapOptions);

      // Add custom marker
      const marker = new window.google.maps.Marker({
        position: { lat: latitude, lng: longitude },
        map: newMap,
        title: title,
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="32" height="40" viewBox="0 0 32 40" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 0C7.163 0 0 7.163 0 16C0 24.837 16 40 16 40S32 24.837 32 16C32 7.163 24.837 0 16 0Z" fill="#3B82F6"/>
              <circle cx="16" cy="16" r="8" fill="white"/>
              <circle cx="16" cy="16" r="4" fill="#3B82F6"/>
            </svg>
          `),
          scaledSize: new window.google.maps.Size(32, 40),
          anchor: new window.google.maps.Point(16, 40)
        },
        animation: window.google.maps.Animation.DROP
      });

      // Add info window
      if (title || address) {
        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div class="p-3 min-w-[200px]">
              <h3 class="font-semibold text-gray-900 mb-1">${title}</h3>
              ${address ? `<p class="text-sm text-gray-600">${address}</p>` : ''}
              <div class="mt-2 flex gap-2">
                <button onclick="window.open('https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}', '_blank')" class="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600">
                  Get Directions
                </button>
                <button onclick="window.open('https://www.google.com/maps/@${latitude},${longitude},15z', '_blank')" class="text-xs bg-gray-500 text-white px-2 py-1 rounded hover:bg-gray-600">
                  View on Google Maps
                </button>
              </div>
            </div>
          `
        });

        marker.addListener('click', () => {
          infoWindow.open(newMap, marker);
        });

        // Open info window by default
        setTimeout(() => {
          infoWindow.open(newMap, marker);
        }, 500);
      }

      setMap(newMap);
      setIsLoading(false);
    } catch (err) {
      setError('Failed to initialize map');
      setIsLoading(false);
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const openDirections = () => {
    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;
    window.open(url, '_blank');
  };

  if (error) {
    return (
      <div 
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center text-gray-500">
          <MapPin className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">{error}</p>
          <p className="text-xs mt-1">
            Location: {latitude.toFixed(6)}, {longitude.toFixed(6)}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapRef}
        className={`w-full transition-all duration-300 ${
          isFullscreen 
            ? 'fixed inset-0 z-50' 
            : 'rounded-lg overflow-hidden'
        }`}
        style={{ height: isFullscreen ? '100vh' : height }}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-100 flex items-center justify-center"
          style={{ height }}
        >
          <div className="text-center text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm">Loading map...</p>
          </div>
        </div>
      )}

      {/* Control Buttons */}
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        {showDirectionsButton && (
          <button
            onClick={openDirections}
            className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors"
            title="Get Directions"
          >
            <Navigation className="w-5 h-5 text-gray-700" />
          </button>
        )}
        
        {showFullscreenButton && (
          <button
            onClick={toggleFullscreen}
            className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors"
            title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
          >
            {isFullscreen ? (
              <Minimize2 className="w-5 h-5 text-gray-700" />
            ) : (
              <Maximize2 className="w-5 h-5 text-gray-700" />
            )}
          </button>
        )}
      </div>

      {/* Fullscreen Overlay Controls */}
      {isFullscreen && (
        <div className="absolute top-4 left-4 bg-white shadow-lg rounded-lg p-3">
          <h3 className="font-semibold text-gray-900 mb-1">{title}</h3>
          {address && <p className="text-sm text-gray-600">{address}</p>}
        </div>
      )}
    </div>
  );
}

// Fallback component when coordinates are not available
export function PropertyMapFallback({ 
  address, 
  className = '', 
  height = '400px' 
}: { 
  address?: string; 
  className?: string; 
  height?: string; 
}) {
  return (
    <div 
      className={`bg-gray-100 flex items-center justify-center ${className}`}
      style={{ height }}
    >
      <div className="text-center text-gray-500">
        <MapPin className="w-8 h-8 mx-auto mb-2" />
        <p className="text-sm mb-1">Map not available</p>
        {address && (
          <p className="text-xs text-gray-400">{address}</p>
        )}
      </div>
    </div>
  );
}