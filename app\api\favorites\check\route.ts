import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { db } from '@/lib/db/config';
import { favorites } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/favorites/check?propertyId=xxx - Check if property is in user's favorites
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ isFavorite: false });
    }

    const userId = session.user.id;

    const { searchParams } = new URL(request.url);
    const propertyId = searchParams.get('propertyId');

    if (!propertyId) {
      return NextResponse.json({ error: 'Property ID required' }, { status: 400 });
    }

    // Check if property is in favorites
    const favorite = await db
      .select({ id: favorites.id })
      .from(favorites)
      .where(and(
        eq(favorites.userId, userId),
        eq(favorites.propertyId, propertyId)
      ))
      .limit(1);

    return NextResponse.json({ 
      isFavorite: favorite.length > 0,
      favoriteId: favorite[0]?.id || null
    });

  } catch (error) {
    console.error('Error checking favorite status:', error);
    return NextResponse.json({ isFavorite: false });
  }
}