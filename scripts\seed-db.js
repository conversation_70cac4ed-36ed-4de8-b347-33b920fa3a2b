#!/usr/bin/env node

/**
 * Database Seeding Script
 * 
 * This script seeds the database with dummy property data for testing.
 * Run this script when you need sample data to test the application.
 * 
 * Usage:
 *   node scripts/seed-db.js
 *   npm run seed
 * 
 * Environment:
 *   Make sure your .env.local file has the correct DATABASE_URL
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🌱 Starting database seeding process...\n');

try {
  // Change to project root directory
  process.chdir(path.join(__dirname, '..'));
  
  console.log('📋 Checking environment...');
  
  // Check if .env.local exists
  const fs = require('fs');
  if (!fs.existsSync('.env.local')) {
    console.error('❌ Error: .env.local file not found');
    console.log('Please create .env.local with your database configuration');
    process.exit(1);
  }
  
  console.log('✅ Environment file found');
  
  // Run the TypeScript seeding script
  console.log('🔄 Executing seeding script...\n');
  
  try {
    // Try to run with ts-node first (if available)
    execSync('npx ts-node --esm lib/db/seed-properties.ts', { 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
  } catch (tsError) {
    console.log('📝 ts-node not available, compiling TypeScript first...');
    
    // Fallback: compile and run with node
    execSync('npx tsc lib/db/seed-properties.ts --target es2020 --module commonjs --outDir temp', { 
      stdio: 'inherit' 
    });
    
    execSync('node temp/lib/db/seed-properties.js', { 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'development' }
    });
    
    // Clean up temporary files
    execSync('rmdir /s /q temp', { stdio: 'ignore' });
  }
  
  console.log('\n🎉 Database seeding completed successfully!');
  console.log('\n📊 What was added:');
  console.log('   • 10 realistic property listings');
  console.log('   • Multiple property images');
  console.log('   • Various property types (apartments, villas, offices, etc.)');
  console.log('   • Properties in Delhi NCR locations');
  console.log('   • Realistic pricing and amenities');
  
  console.log('\n🚀 Next steps:');
  console.log('   1. Visit http://localhost:3002 to see the properties');
  console.log('   2. Login to admin panel at http://localhost:3002/admin');
  console.log('   3. Test user features like favorites and comparison');
  
} catch (error) {
  console.error('\n❌ Seeding failed with error:');
  console.error(error.message);
  
  console.log('\n🔧 Troubleshooting:');
  console.log('   1. Ensure PostgreSQL database is running');
  console.log('   2. Verify DATABASE_URL in .env.local');
  console.log('   3. Make sure admin user exists');
  console.log('   4. Check database connection');
  
  process.exit(1);
}