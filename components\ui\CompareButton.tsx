'use client';

import { useState, useEffect } from 'react';
import { Scale, Check } from 'lucide-react';
import { motion } from 'framer-motion';
import { Button } from './Button';
import { analytics } from '@/components/analytics/GoogleAnalytics';
import { useRouter } from 'next/navigation';

interface CompareButtonProps {
  property: any;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showText?: boolean;
}

const maxComparisons = 3;

// Utility function to clear all comparison data
const clearComparisonData = () => {
  localStorage.removeItem('propertyComparison');
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('comparisonUpdated'));
  }
};

export function CompareButton({ 
  property, 
  className = '', 
  size = 'sm',
  showText = false 
}: CompareButtonProps) {
  const router = useRouter();
  const [isInComparison, setIsInComparison] = useState(false);
  const [comparisonCount, setComparisonCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Check if property is in comparison on mount
  useEffect(() => {
    // Clear any invalid data first
    try {
      const saved = localStorage.getItem('propertyComparison');
      if (saved) {
        const parsed = JSON.parse(saved);
        if (!Array.isArray(parsed)) {
          console.log('Clearing invalid comparison data on mount');
          localStorage.removeItem('propertyComparison');
        }
      }
    } catch {
      localStorage.removeItem('propertyComparison');
    }

    checkComparisonStatus();
    
    // Listen for storage changes to sync across tabs
    const handleStorageChange = () => {
      checkComparisonStatus();
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [property.id]);

  const checkComparisonStatus = () => {
    try {
      const savedComparison = localStorage.getItem('propertyComparison');
      
      if (savedComparison) {
        const comparison = JSON.parse(savedComparison);
        // Validate that comparison is an array and has valid items
        if (Array.isArray(comparison) && comparison.every(p => p && p.id)) {
          const isInComparison = comparison.some((p: any) => p.id === property.id);
          setIsInComparison(isInComparison);
          setComparisonCount(comparison.length);
        } else {
          // Clear invalid data
          localStorage.removeItem('propertyComparison');
          setIsInComparison(false);
          setComparisonCount(0);
        }
      } else {
        setIsInComparison(false);
        setComparisonCount(0);
      }
    } catch (error) {
      console.error('Error checking comparison status:', error);
      // Clear corrupted data
      localStorage.removeItem('propertyComparison');
      setIsInComparison(false);
      setComparisonCount(0);
    }
  };

  const toggleComparison = (event?: React.MouseEvent) => {
    // Prevent parent Link from being triggered
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Validate property data
    if (!property || !property.id) {
      console.error('Invalid property data for comparison:', property);
      return;
    }

    setIsLoading(true);
    
    try {
      const savedComparison = localStorage.getItem('propertyComparison');
      let comparison = [];
      
      if (savedComparison) {
        try {
          const parsed = JSON.parse(savedComparison);
          comparison = Array.isArray(parsed) ? parsed.filter(p => p && p.id) : [];
        } catch {
          comparison = [];
        }
      }

      if (isInComparison) {
        // Remove from comparison
        comparison = comparison.filter((p: any) => p.id !== property.id);
      } else {
        // Add to comparison (if not at limit and not already in list)
        if (comparison.length < maxComparisons && !comparison.some(p => p.id === property.id)) {
          const newProperty = {
            id: property.id,
            title: property.title,
            slug: property.slug,
            price: property.price?.toString() || '0', // Ensure price is string
            area: property.area,
            city: property.city,
            propertyType: property.propertyType,
            listingType: property.listingType,
            bedrooms: property.bedrooms || 0,
            bathrooms: property.bathrooms || 0,
            totalArea: property.totalArea || 0,
            carpetArea: property.carpetArea,
            builtUpArea: property.builtUpArea,
            parking: property.parking,
            balconies: property.balconies,
            furnished: property.furnished || 'Not specified',
            ageOfProperty: property.ageOfProperty,
            facing: property.facing,
            floors: property.floors,
            totalFloors: property.totalFloors,
            images: property.images || [],
            amenities: property.amenities || [],
            features: property.features || [],
            status: property.status,
            pricePerSqft: property.pricePerSqft,
            createdAt: property.createdAt,
          };
          comparison.push(newProperty);
        } else if (comparison.length >= maxComparisons) {
          alert(`You can only compare up to ${maxComparisons} properties at a time.`);
          setIsLoading(false);
          return;
        } else {
          setIsLoading(false);
          return;
        }
      }

      localStorage.setItem('propertyComparison', JSON.stringify(comparison));
      setIsInComparison(!isInComparison);
      setComparisonCount(comparison.length);

      // Dispatch custom event to notify other components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('comparisonUpdated'));
      }

      // Track analytics and show notification
      if (!isInComparison) {
        // Added to comparison
        analytics.addToComparison(property.id);
        if (typeof window !== 'undefined' && window.showToast) {
          window.showToast('Property added to comparison');
        }
      } else {
        // Removed from comparison
        if (typeof window !== 'undefined' && window.showToast) {
          window.showToast('Property removed from comparison');
        }
      }
    } catch (error) {
      console.error('Error toggling comparison:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const goToComparison = (event?: React.MouseEvent) => {
    // Prevent parent Link from being triggered
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    
    router.push('/compare');
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-8 h-8';
      case 'lg':
        return 'w-12 h-12';
      default:
        return 'w-10 h-10';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  if (showText) {
    return (
      <div className={`flex gap-2 ${className}`}>
        <Button
          variant={isInComparison ? 'default' : 'outline'}
          onClick={(e) => toggleComparison(e)}
          disabled={isLoading || (!isInComparison && comparisonCount >= maxComparisons)}
          size={size === 'md' ? 'sm' : size}
          className={isInComparison ? 'bg-blue-500 hover:bg-blue-600 border-blue-500' : 'border-gray-300 hover:border-blue-500'}
        >
          <motion.div
            animate={{ scale: isInComparison ? 1.2 : 1 }}
            transition={{ duration: 0.2 }}
          >
            {isInComparison ? (
              <Check className={`${getIconSize()} mr-2 text-white`} />
            ) : (
              <Scale className={`${getIconSize()} mr-2 text-gray-600`} />
            )}
          </motion.div>
          {isInComparison ? 'In Comparison' : 'Compare'}
        </Button>
        
        {comparisonCount > 0 && (
          <Button variant="outline" onClick={(e) => goToComparison(e)} size={size === 'md' ? 'sm' : size}>
            View Comparison ({comparisonCount})
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={`flex gap-2 ${className}`}>
      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        onClick={(e) => toggleComparison(e)}
        disabled={isLoading || (!isInComparison && comparisonCount >= maxComparisons)}
        className={`${getSizeClasses()} rounded-full bg-white/80 backdrop-blur-sm border border-gray-200 hover:bg-white hover:shadow-lg transition-all duration-300 flex items-center justify-center ${
          isInComparison ? 'bg-blue-500 border-blue-500' : ''
        } ${(!isInComparison && comparisonCount >= maxComparisons) ? 'opacity-50 cursor-not-allowed' : ''}`}
      >
        <motion.div
          animate={{ 
            scale: isInComparison ? 1.2 : 1,
            rotate: isInComparison ? 360 : 0 
          }}
          transition={{ duration: 0.3 }}
        >
          {isInComparison ? (
            <Check className={`${getIconSize()} text-white`} />
          ) : (
            <Scale className={`${getIconSize()} transition-colors duration-300 ${
              comparisonCount >= maxComparisons ? 'text-gray-400' : 'text-gray-600 hover:text-blue-500'
            }`} />
          )}
        </motion.div>
      </motion.button>
      
      {comparisonCount > 0 && (
        <motion.button
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={(e) => goToComparison(e)}
          className={`${getSizeClasses()} rounded-full bg-blue-500 text-white border border-blue-500 hover:bg-blue-600 hover:shadow-lg transition-all duration-300 flex items-center justify-center relative`}
        >
          <Scale className={getIconSize()} />
          <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {comparisonCount}
          </div>
        </motion.button>
      )}
    </div>
  );
}