'use client';

import { ChevronRight, Home } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { BreadcrumbStructuredData } from '@/components/seo/StructuredData';

interface BreadcrumbItem {
  name: string;
  url: string;
  current?: boolean;
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className = '' }: BreadcrumbProps) {
  const pathname = usePathname();

  // Auto-generate breadcrumbs if not provided
  const breadcrumbItems = items || generateBreadcrumbs(pathname);

  if (breadcrumbItems.length <= 1) {
    return null; // Don't show breadcrumbs for home page or single-level pages
  }

  return (
    <>
      <BreadcrumbStructuredData items={breadcrumbItems} />
      <nav 
        className={`flex ${className}`} 
        aria-label="Breadcrumb"
      >
        <ol className="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
          {breadcrumbItems.map((item, index) => (
            <li key={item.url} className="inline-flex items-center">
              {index > 0 && (
                <ChevronRight className="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" />
              )}
              
              <div className="flex items-center">
                {index === 0 && (
                  <Home className="w-3 h-3 me-2.5" />
                )}
                
                {item.current || index === breadcrumbItems.length - 1 ? (
                  <span className="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">
                    {item.name}
                  </span>
                ) : (
                  <Link
                    href={item.url}
                    className="ms-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white transition-colors"
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            </li>
          ))}
        </ol>
      </nav>
    </>
  );
}

function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [
    { name: 'Home', url: '/' }
  ];

  let currentPath = '';
  
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    const isLast = index === segments.length - 1;
    
    // Generate human-readable names for common segments
    let name = segment;
    switch (segment) {
      case 'properties':
        name = 'Properties';
        break;
      case 'compare':
        name = 'Compare';
        break;
      case 'favorites':
        name = 'Favorites';
        break;
      case 'saved-searches':
        name = 'Saved Searches';
        break;
      case 'dashboard':
        name = 'My Profile';
        break;
      case 'admin':
        name = 'Admin Panel';
        break;
      case 'about':
        name = 'About Us';
        break;
      case 'contact':
        name = 'Contact';
        break;
      case 'auth':
        name = 'Authentication';
        break;
      case 'signin':
        name = 'Sign In';
        break;
      case 'signup':
        name = 'Sign Up';
        break;
      default:
        // For property slugs and other dynamic segments, capitalize and format
        name = segment
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');
        break;
    }

    breadcrumbs.push({
      name,
      url: currentPath,
      current: isLast
    });
  });

  return breadcrumbs;
}

// Preset breadcrumbs for common pages
export const breadcrumbPresets = {
  properties: [
    { name: 'Home', url: '/' },
    { name: 'Properties', url: '/properties', current: true }
  ],
  compare: [
    { name: 'Home', url: '/' },
    { name: 'Compare Properties', url: '/compare', current: true }
  ],
  favorites: [
    { name: 'Home', url: '/' },
    { name: 'My Profile', url: '/dashboard' },
    { name: 'Favorites', url: '/favorites', current: true }
  ],
  savedSearches: [
    { name: 'Home', url: '/' },
    { name: 'My Profile', url: '/dashboard' },
    { name: 'Saved Searches', url: '/saved-searches', current: true }
  ],
  dashboard: [
    { name: 'Home', url: '/' },
    { name: 'My Profile', url: '/dashboard', current: true }
  ],
  about: [
    { name: 'Home', url: '/' },
    { name: 'About Us', url: '/about', current: true }
  ],
  contact: [
    { name: 'Home', url: '/' },
    { name: 'Contact', url: '/contact', current: true }
  ],
  admin: [
    { name: 'Home', url: '/' },
    { name: 'Admin Panel', url: '/admin', current: true }
  ]
};

// Utility function to create property-specific breadcrumbs
export function createPropertyBreadcrumb(propertyTitle: string, propertySlug: string): BreadcrumbItem[] {
  return [
    { name: 'Home', url: '/' },
    { name: 'Properties', url: '/properties' },
    { name: propertyTitle, url: `/properties/${propertySlug}`, current: true }
  ];
}