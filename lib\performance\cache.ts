import { unstable_cache } from 'next/cache';

// Cache configuration
export const CACHE_TAGS = {
  properties: 'properties',
  users: 'users',
  enquiries: 'enquiries',
  analytics: 'analytics',
  search: 'search',
} as const;

export const CACHE_DURATIONS = {
  short: 60, // 1 minute
  medium: 300, // 5 minutes
  long: 3600, // 1 hour
  day: 86400, // 24 hours
  week: 604800, // 7 days
} as const;

/**
 * Create a cached function with proper tags and revalidation
 */
export function createCachedFunction<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options: {
    tags: string[];
    revalidate?: number;
    keyGenerator?: (...args: T) => string;
  }
) {
  return unstable_cache(
    fn,
    undefined,
    {
      tags: options.tags,
      revalidate: options.revalidate || CACHE_DURATIONS.medium,
    }
  );
}

/**
 * In-memory cache for client-side performance
 */
class MemoryCache {
  private cache = new Map<string, { data: any; expires: number }>();
  private maxSize = 100; // Maximum number of entries

  set<T>(key: string, data: T, ttl: number = CACHE_DURATIONS.short * 1000): void {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      if (firstKey) {
        this.cache.delete(firstKey);
      }
    }

    this.cache.set(key, {
      data,
      expires: Date.now() + ttl,
    });
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return null;
    }

    return entry.data as T;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }

    if (Date.now() > entry.expires) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    // Clean expired entries first
    const now = Date.now();
    Array.from(this.cache.entries()).forEach(([key, entry]) => {
      if (now > entry.expires) {
        this.cache.delete(key);
      }
    });
    return this.cache.size;
  }
}

// Global memory cache instance
export const memoryCache = new MemoryCache();

/**
 * Local storage cache with expiration
 */
export class LocalStorageCache {
  private prefix = 'armaan_cache_';

  set<T>(key: string, data: T, ttl: number = CACHE_DURATIONS.short * 1000): void {
    try {
      const item = {
        data,
        expires: Date.now() + ttl,
      };
      localStorage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      console.warn('Failed to set localStorage cache:', error);
    }
  }

  get<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(this.prefix + key);
      
      if (!item) {
        return null;
      }

      const parsed = JSON.parse(item);
      
      if (Date.now() > parsed.expires) {
        this.delete(key);
        return null;
      }

      return parsed.data as T;
    } catch (error) {
      console.warn('Failed to get localStorage cache:', error);
      return null;
    }
  }

  has(key: string): boolean {
    try {
      const item = localStorage.getItem(this.prefix + key);
      
      if (!item) {
        return false;
      }

      const parsed = JSON.parse(item);
      
      if (Date.now() > parsed.expires) {
        this.delete(key);
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  delete(key: string): void {
    try {
      localStorage.removeItem(this.prefix + key);
    } catch (error) {
      console.warn('Failed to delete localStorage cache:', error);
    }
  }

  clear(): void {
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.prefix)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.warn('Failed to clear localStorage cache:', error);
    }
  }
}

// Global localStorage cache instance
export const localStorageCache = new LocalStorageCache();

/**
 * Cached fetch function for API requests
 */
export async function cachedFetch<T>(
  url: string,
  options: RequestInit & { cacheKey?: string; ttl?: number } = {}
): Promise<T> {
  const { cacheKey = url, ttl = CACHE_DURATIONS.short * 1000, ...fetchOptions } = options;
  
  // Try memory cache first
  const cachedData = memoryCache.get<T>(cacheKey);
  if (cachedData !== null) {
    return cachedData;
  }

  // Fetch fresh data
  const response = await fetch(url, fetchOptions);
  
  if (!response.ok) {
    throw new Error(`Fetch failed: ${response.statusText}`);
  }

  const data = await response.json() as T;
  
  // Cache the result
  memoryCache.set(cacheKey, data, ttl);
  
  return data;
}

/**
 * Cache invalidation helper
 */
export function invalidateCache(pattern: string): void {
  // Clear memory cache entries matching pattern
  const memKeys = Array.from((memoryCache as any).cache.keys()) as string[];
  memKeys.forEach((key) => {
    if (key.includes(pattern)) {
      memoryCache.delete(key);
    }
  });

  // Clear localStorage cache entries matching pattern
  try {
    const localKeys = Object.keys(localStorage);
    localKeys.forEach(key => {
      if (key.includes(pattern)) {
        localStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.warn('Failed to invalidate localStorage cache:', error);
  }
}