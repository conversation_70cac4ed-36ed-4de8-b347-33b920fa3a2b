import { pgTable, text, integer, decimal, boolean, timestamp, uuid, jsonb, varchar, index } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

// Users table for authentication
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  email: varchar('email', { length: 255 }).unique().notNull(),
  password: text('password'), // For email/password auth
  name: text('name').notNull(),
  phone: varchar('phone', { length: 20 }),
  role: varchar('role', { length: 20 }).default('user').notNull(), // 'admin', 'agent', 'user'
  emailVerified: boolean('email_verified').default(false),
  image: text('image'),
  provider: varchar('provider', { length: 50 }).default('credentials'), // 'google', 'facebook', 'credentials'
  providerId: text('provider_id'),
  isActive: boolean('is_active').default(true),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  emailIdx: index('users_email_idx').on(table.email),
  roleIdx: index('users_role_idx').on(table.role),
}));

// Properties table
export const properties = pgTable('properties', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  slug: varchar('slug', { length: 255 }).unique().notNull(),
  price: decimal('price', { precision: 15, scale: 2 }).notNull(),
  pricePerSqft: decimal('price_per_sqft', { precision: 10, scale: 2 }),
  
  // Location details
  area: text('area').notNull(),
  city: text('city').notNull(),
  state: text('state').notNull(),
  address: text('address').notNull(),
  pincode: varchar('pincode', { length: 10 }),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  
  // Property specifications
  bedrooms: integer('bedrooms'),
  bathrooms: integer('bathrooms'),
  totalArea: integer('total_area'), // in sqft
  carpetArea: integer('carpet_area'), // in sqft
  builtUpArea: integer('built_up_area'), // in sqft
  floors: integer('floors'),
  totalFloors: integer('total_floors'),
  parking: integer('parking').default(0),
  balconies: integer('balconies').default(0),
  furnished: varchar('furnished', { length: 20 }).default('unfurnished'), // 'furnished', 'semi-furnished', 'unfurnished'
  ageOfProperty: integer('age_of_property'), // in years
  facing: varchar('facing', { length: 20 }), // 'north', 'south', 'east', 'west'
  
  // Property details
  propertyType: varchar('property_type', { length: 50 }).notNull(), // 'apartment', 'house', 'villa', 'plot', etc.
  subType: varchar('sub_type', { length: 50 }), // 'studio', '1bhk', '2bhk', etc.
  listingType: varchar('listing_type', { length: 10 }).notNull(), // 'sale' or 'rent'
  status: varchar('status', { length: 20 }).default('available').notNull(), // 'available', 'sold', 'rented', 'under-negotiation'
  
  // Features and amenities
  amenities: jsonb('amenities').default([]), // Array of amenity strings
  features: jsonb('features').default([]), // Array of feature strings
  nearbyPlaces: jsonb('nearby_places').default([]), // Array of nearby place objects
  
  // Media
  images: jsonb('images').default([]), // Array of image URLs
  videos: jsonb('videos').default([]), // Array of video URLs
  documents: jsonb('documents').default([]), // Array of document URLs
  virtualTourUrl: text('virtual_tour_url'),
  
  // Contact and ownership
  ownerId: uuid('owner_id').references(() => users.id),
  agentId: uuid('agent_id').references(() => users.id),
  contactName: text('contact_name'),
  contactPhone: varchar('contact_phone', { length: 20 }),
  contactEmail: varchar('contact_email', { length: 255 }),
  contactWhatsapp: varchar('contact_whatsapp', { length: 20 }),
  
  // Marketing
  featured: boolean('featured').default(false),
  verified: boolean('verified').default(false),
  published: boolean('published').default(true),
  views: integer('views').default(0),
  enquiries: integer('enquiries').default(0),
  
  // SEO
  metaTitle: text('meta_title'),
  metaDescription: text('meta_description'),
  metaKeywords: text('meta_keywords'),
  
  // Timestamps
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  publishedAt: timestamp('published_at'),
  soldAt: timestamp('sold_at'),
}, (table) => ({
  slugIdx: index('properties_slug_idx').on(table.slug),
  cityIdx: index('properties_city_idx').on(table.city),
  areaIdx: index('properties_area_idx').on(table.area),
  typeIdx: index('properties_type_idx').on(table.propertyType),
  listingTypeIdx: index('properties_listing_type_idx').on(table.listingType),
  statusIdx: index('properties_status_idx').on(table.status),
  priceIdx: index('properties_price_idx').on(table.price),
  featuredIdx: index('properties_featured_idx').on(table.featured),
  publishedIdx: index('properties_published_idx').on(table.published),
  locationIdx: index('properties_location_idx').on(table.latitude, table.longitude),
}));

// Property images
export const propertyImages = pgTable('property_images', {
  id: uuid('id').primaryKey().defaultRandom(),
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'cascade' }).notNull(),
  url: text('url').notNull(),
  alt: text('alt'),
  caption: text('caption'),
  order: integer('order').default(1),
  type: varchar('type', { length: 20 }).default('photo'), // 'photo', 'floorplan', 'map'
  isFeatured: boolean('is_featured').default(false),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  propertyIdx: index('property_images_property_idx').on(table.propertyId),
  orderIdx: index('property_images_order_idx').on(table.propertyId, table.order),
}));

// Property enquiries/leads
export const enquiries = pgTable('enquiries', {
  id: uuid('id').primaryKey().defaultRandom(),
  propertyId: uuid('property_id').references(() => properties.id).notNull(),
  userId: uuid('user_id').references(() => users.id),
  
  // Contact details
  name: text('name').notNull(),
  email: varchar('email', { length: 255 }).notNull(),
  phone: varchar('phone', { length: 20 }).notNull(),
  message: text('message'),
  
  // Enquiry details
  enquiryType: varchar('enquiry_type', { length: 20 }).default('general'), // 'general', 'viewing', 'price', 'loan'
  budget: decimal('budget', { precision: 15, scale: 2 }),
  status: varchar('status', { length: 20 }).default('new'), // 'new', 'contacted', 'qualified', 'closed'
  source: varchar('source', { length: 50 }).default('website'), // 'website', 'phone', 'whatsapp', 'email'
  
  // Follow-up
  assignedTo: uuid('assigned_to').references(() => users.id),
  followUpDate: timestamp('follow_up_date'),
  notes: text('notes'),
  
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  propertyIdx: index('enquiries_property_idx').on(table.propertyId),
  statusIdx: index('enquiries_status_idx').on(table.status),
  emailIdx: index('enquiries_email_idx').on(table.email),
}));

// User sessions for NextAuth
export const sessions = pgTable('sessions', {
  sessionToken: text('session_token').primaryKey(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('sessions_user_idx').on(table.userId),
}));

// Account linking for OAuth
export const accounts = pgTable('accounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  type: varchar('type', { length: 255 }).notNull(),
  provider: varchar('provider', { length: 255 }).notNull(),
  providerAccountId: varchar('provider_account_id', { length: 255 }).notNull(),
  refresh_token: text('refresh_token'),
  access_token: text('access_token'),
  expires_at: integer('expires_at'),
  token_type: varchar('token_type', { length: 255 }),
  scope: varchar('scope', { length: 255 }),
  id_token: text('id_token'),
  session_state: varchar('session_state', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('accounts_user_idx').on(table.userId),
  providerIdx: index('accounts_provider_idx').on(table.provider, table.providerAccountId),
}));

// Verification tokens for email verification
export const verificationTokens = pgTable('verification_tokens', {
  identifier: varchar('identifier', { length: 255 }).notNull(),
  token: varchar('token', { length: 255 }).notNull(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  identifierTokenIdx: index('verification_tokens_identifier_token_idx').on(table.identifier, table.token),
}));

// User favorites/wishlist
export const favorites = pgTable('favorites', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'cascade' }).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  userPropertyIdx: index('favorites_user_property_idx').on(table.userId, table.propertyId),
}));

// Property views tracking
export const propertyViews = pgTable('property_views', {
  id: uuid('id').primaryKey().defaultRandom(),
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'cascade' }).notNull(),
  userId: uuid('user_id').references(() => users.id),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  referrer: text('referrer'),
  viewedAt: timestamp('viewed_at').defaultNow().notNull(),
}, (table) => ({
  propertyIdx: index('property_views_property_idx').on(table.propertyId),
  userIdx: index('property_views_user_idx').on(table.userId),
  dateIdx: index('property_views_date_idx').on(table.viewedAt),
}));

// Saved searches
export const savedSearches = pgTable('saved_searches', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  searchParams: jsonb('search_params').notNull(), // Store search filters as JSON
  emailAlerts: boolean('email_alerts').default(true),
  frequency: varchar('frequency', { length: 20 }).default('daily'), // 'daily', 'weekly', 'monthly'
  lastSent: timestamp('last_sent'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('saved_searches_user_idx').on(table.userId),
}));

// Chat rooms table
export const chatRooms = pgTable('chat_rooms', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  userName: text('user_name').notNull(),
  userEmail: text('user_email').notNull(),
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'set null' }),
  status: varchar('status', { length: 20 }).default('active').notNull(), // 'active', 'closed', 'pending'
  lastMessage: text('last_message'),
  lastMessageAt: timestamp('last_message_at'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  userIdx: index('chat_rooms_user_idx').on(table.userId),
  statusIdx: index('chat_rooms_status_idx').on(table.status),
  propertyIdx: index('chat_rooms_property_idx').on(table.propertyId),
}));

// Chat messages table
export const chatMessages = pgTable('chat_messages', {
  id: uuid('id').primaryKey().defaultRandom(),
  roomId: uuid('room_id').references(() => chatRooms.id, { onDelete: 'cascade' }).notNull(),
  senderId: uuid('sender_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  senderName: text('sender_name').notNull(),
  senderRole: varchar('sender_role', { length: 20 }).default('user').notNull(), // 'user', 'admin', 'agent'
  message: text('message').notNull(),
  type: varchar('type', { length: 20 }).default('text').notNull(), // 'text', 'image', 'file'
  propertyId: uuid('property_id').references(() => properties.id, { onDelete: 'set null' }),
  isRead: boolean('is_read').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
}, (table) => ({
  roomIdx: index('chat_messages_room_idx').on(table.roomId),
  senderIdx: index('chat_messages_sender_idx').on(table.senderId),
  createdAtIdx: index('chat_messages_created_at_idx').on(table.createdAt),
}));

// Export types for TypeScript
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Property = typeof properties.$inferSelect;
export type NewProperty = typeof properties.$inferInsert;
export type PropertyImage = typeof propertyImages.$inferSelect;
export type NewPropertyImage = typeof propertyImages.$inferInsert;
export type Enquiry = typeof enquiries.$inferSelect;
export type NewEnquiry = typeof enquiries.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type Account = typeof accounts.$inferSelect;
export type Favorite = typeof favorites.$inferSelect;
export type PropertyView = typeof propertyViews.$inferSelect;
export type SavedSearch = typeof savedSearches.$inferSelect;
export type ChatRoom = typeof chatRooms.$inferSelect;
export type NewChatRoom = typeof chatRooms.$inferInsert;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type NewChatMessage = typeof chatMessages.$inferInsert;