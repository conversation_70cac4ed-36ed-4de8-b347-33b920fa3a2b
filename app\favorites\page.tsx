import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { redirect } from 'next/navigation';
import { FavoritesPage } from '@/components/favorites/FavoritesPage';

export const metadata: Metadata = {
  title: 'My Favorites | Property Trendz',
  description: 'View and manage your saved properties and wishlist.',
};

export default async function Favorites() {
  const session = await getServerSession(authConfig) as Session | null;

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/favorites');
  }

  return <FavoritesPage />;
}