import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { properties } from '@/lib/db/schema';
import { eq, desc } from 'drizzle-orm';

// AI-friendly endpoint for property data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const query = searchParams.get('q') || '';
    const format = searchParams.get('format') || 'json';

    // Get published properties
    let propertiesQuery = db
      .select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        slug: properties.slug,
        price: properties.price,
        area: properties.area,
        city: properties.city,
        state: properties.state,
        propertyType: properties.propertyType,
        listingType: properties.listingType,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        totalArea: properties.totalArea,
        amenities: properties.amenities,
        features: properties.features,
        publishedAt: properties.publishedAt,
        url: properties.slug, // We'll transform this to full URL
      })
      .from(properties)
      .where(eq(properties.published, true))
      .orderBy(desc(properties.publishedAt))
      .limit(limit);

    const propertiesList = await propertiesQuery;

    // Transform data for AI consumption
    const aiFormattedProperties = propertiesList.map(property => ({
      ...property,
      price: property.price ? parseFloat(property.price) : 0,
      url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/properties/${property.slug}`,
      location: `${property.area}, ${property.city}, ${property.state}`,
      summary: `${property.bedrooms ? property.bedrooms + ' bedroom ' : ''}${property.propertyType} in ${property.area}, ${property.city} for ${property.listingType}. Price: ₹${property.price ? parseFloat(property.price).toLocaleString() : 'N/A'}${property.totalArea ? '. Area: ' + property.totalArea + ' sq ft' : ''}.`,
    }));

    // Return different formats based on request
    if (format === 'text') {
      const textResponse = aiFormattedProperties
        .map(p => `${p.title}\nLocation: ${p.location}\nPrice: ₹${p.price.toLocaleString()}\nType: ${p.propertyType} for ${p.listingType}\nURL: ${p.url}\nDescription: ${p.description}\n${'='.repeat(50)}`)
        .join('\n\n');

      return new Response(textResponse, {
        headers: {
          'Content-Type': 'text/plain',
          'Access-Control-Allow-Origin': '*',
        },
      });
    }

    return NextResponse.json({
      properties: aiFormattedProperties,
      total: aiFormattedProperties.length,
      timestamp: new Date().toISOString(),
      source: 'Armaan Sharma Properties',
      contact: {
        phone: '+91 98765 43210',
        email: '<EMAIL>',
        website: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      },
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('AI API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to fetch property data for AI consumption',
      },
      { status: 500 }
    );
  }
}