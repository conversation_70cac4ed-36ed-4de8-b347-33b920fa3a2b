# 🏠 Property Trendz - Modern Property Listing Website

A beautiful, modern, and responsive property listing website built with Next.js 14, TypeScript, Tailwind CSS, and Framer Motion. Designed specifically for property listings in the Delhi NCR region.

## ✨ Features

### 🎨 **Modern UI/UX**
- Responsive design that works on all devices
- Beautiful animations and micro-interactions
- Modern gradients and color schemes
- Loading screens and skeleton loaders
- Hover effects and smooth transitions

### 🏡 **Property Management**
- Comprehensive property listings
- Advanced search and filtering
- Property details with image galleries
- Featured properties showcase
- Property categorization (Apartments, Houses, Builder Floors, Commercial)

### 📱 **Contact Integration**
- WhatsApp click-to-chat integration
- Contact forms on property pages
- Phone number click-to-call
- Email integration for inquiries

### 🔧 **Admin Features**
- Simple admin dashboard for property management
- Add/Edit/Delete properties
- Image upload functionality
- Property status management

### ⚡ **Performance & SEO**
- Fast loading with Next.js 14
- SEO-optimized with dynamic meta tags
- Image optimization
- Server-side rendering

## 🚀 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Animations**: Framer Motion
- **Database**: MongoDB with Mongoose
- **Image Storage**: Cloudinary
- **Icons**: Lucide React
- **UI Components**: Radix UI
- **Notifications**: React Hot Toast

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- MongoDB database (local or MongoDB Atlas)
- Cloudinary account (for image storage)

### 1. Clone the repository
\`\`\`bash
git clone <repository-url>
cd property-website
\`\`\`

### 2. Install dependencies
\`\`\`bash
npm install
\`\`\`

### 3. Environment Setup
Create a `.env.local` file in the root directory with the following variables:

\`\`\`env
# Database (PostgreSQL)
DATABASE_URL="postgresql://username:password@host:port/database"

# NextAuth.js Configuration
NEXTAUTH_SECRET="your-super-secret-key-32-characters-long-minimum"
NEXTAUTH_URL="http://localhost:3000"

# Email Configuration (for contact forms)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# Site Configuration
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_NAME="Property Trendz"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_CONTACT_PHONE="+**********"
NEXT_PUBLIC_WHATSAPP_NUMBER="**********"

# Security
CSRF_SECRET="your-super-secret-csrf-protection-key-64-chars-long"
ENCRYPTION_KEY="your-32-character-encryption-key"
JWT_SECRET="your-jwt-secret-key"
\`\`\`

### 4. Run the development server
\`\`\`bash
npm run dev
\`\`\`

Open [http://localhost:3000](http://localhost:3000) to view the website.

## 🚀 Production Deployment

### Prerequisites
- Node.js 18+ installed on your server
- PostgreSQL database
- PM2 for process management
- Nginx (recommended for reverse proxy)

### Production Environment Setup

1. **Clone the repository on your server:**
   ```bash
   git clone https://github.com/your-username/Properties-Trends.git
   cd Properties-Trends
   ```

2. **Install dependencies:**
   ```bash
   npm ci --only=production
   ```

3. **Create production environment file:**
   ```bash
   cp .env.production.example .env.production
   ```

4. **Configure production environment variables in `.env.production`:**
   - Update `DATABASE_URL` with your production database
   - Set `NEXTAUTH_URL` to your domain (https://yourdomain.com)
   - Generate secure secrets for `NEXTAUTH_SECRET`, `CSRF_SECRET`, etc.
   - Configure SMTP settings for email functionality
   - Update contact information and WhatsApp number

5. **Build the application:**
   ```bash
   npm run build
   ```

6. **Start with PM2:**
   ```bash
   npm run deploy:start
   ```

### Docker Deployment

1. **Build Docker image:**
   ```bash
   docker build -t property-trendz .
   ```

2. **Run with Docker Compose:**
   ```bash
   docker-compose up -d
   ```

### Security Considerations

- Use HTTPS in production (configure SSL certificates)
- Set strong, unique secrets for all environment variables
- Configure firewall rules to restrict database access
- Enable rate limiting with Redis (recommended)
- Regular security updates and monitoring

### Performance Optimization

- Enable CDN for static assets
- Configure Redis for caching and rate limiting
- Set up database connection pooling
- Monitor application performance and logs

## 📁 Project Structure

\`\`\`
property-website/
├── app/                    # Next.js 14 app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx           # Homepage
│   ├── properties/        # Property pages
│   ├── admin/             # Admin dashboard
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/               # UI components
│   ├── property/         # Property-specific components
│   └── forms/            # Forms and input components
├── types/                # TypeScript type definitions
├── lib/                  # Utilities and database connection
├── models/               # Database models
├── data/                 # Sample data and constants
├── public/               # Static assets
└── styles/               # Additional styles
\`\`\`

## 🎯 Key Features Implementation

### 🏠 **Property Listings**
- Grid and list view options
- Advanced filtering by location, price, type, bedrooms
- Sorting by price, date, area, featured status
- Pagination with infinite scroll option

### 🔍 **Search Functionality**
- Real-time search with location autocomplete
- Filter by property type, price range, amenities
- Save search preferences

### 📸 **Image Gallery**
- Responsive image galleries with lightbox
- Lazy loading for performance
- Cloudinary integration for optimization

### 📱 **Mobile Experience**
- Touch-friendly interface
- Swipe gestures for image galleries
- Mobile-optimized navigation

### 🎨 **Animations**
- Smooth page transitions with Framer Motion
- Hover effects on property cards
- Loading animations and skeleton screens
- Micro-interactions for better UX

## 🛠️ Customization

### **Colors**
Update the color scheme in `tailwind.config.js`:
\`\`\`javascript
colors: {
  primary: { /* Your primary colors */ },
  secondary: { /* Your secondary colors */ },
  // ...
}
\`\`\`

### **Content**
- Update contact information in the navigation and footer
- Modify the hero section content
- Add your own property data in `data/sampleProperties.ts`

### **Branding**
- Replace logo and favicon in the `public` folder
- Update site metadata in `app/layout.tsx`

## 📚 API Documentation

### Properties API
- `GET /api/properties` - Get all properties with filtering
- `GET /api/properties/[id]` - Get property by ID
- `POST /api/properties` - Create new property (admin)
- `PUT /api/properties/[id]` - Update property (admin)
- `DELETE /api/properties/[id]` - Delete property (admin)

### Contact API
- `POST /api/contact` - Send contact form

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
The project can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🔧 Development Scripts

\`\`\`bash
npm run dev      # Start development server
npm run build    # Build for production
npm run start    # Start production server
npm run lint     # Run ESLint
\`\`\`

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- WhatsApp: +91 99999 99999

---

**Built with ❤️ for Delhi NCR Property Business** 