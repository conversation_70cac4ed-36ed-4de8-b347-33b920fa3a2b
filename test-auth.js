const bcrypt = require('bcryptjs');

// Test password verification
async function testPassword() {
  const testPassword = 'password123';
  const hashedPassword = '$2b$12$sgMKjKVZqVOKlbVz8.3rKOYjKVZqVOKlbVz8.3rKOYjKVZqVOKlbVz8.3rKO'; // Example hash
  
  console.log('Testing password verification...');
  console.log('Plain password:', testPassword);
  console.log('Hashed password:', hashedPassword);
  
  try {
    const isMatch = await bcrypt.compare(testPassword, hashedPassword);
    console.log('Password match:', isMatch);
  } catch (error) {
    console.error('Error:', error);
  }
  
  // Test with common passwords
  const commonPasswords = ['password', 'password123', 'test123', 'admin', '123456'];
  
  for (const pwd of commonPasswords) {
    try {
      const hash = await bcrypt.hash(pwd, 12);
      console.log(`Password "${pwd}" hashes to: ${hash.substring(0, 20)}...`);
    } catch (error) {
      console.error('Hash error:', error);
    }
  }
}

testPassword();
