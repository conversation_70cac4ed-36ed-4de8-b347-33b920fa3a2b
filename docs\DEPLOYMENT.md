# Production Deployment Guide

## 📋 Pre-Deployment Checklist

### 1. Environment Configuration
- [ ] Copy `.env.production.example` to `.env.production`
- [ ] Fill in all required environment variables
- [ ] Test database connection
- [ ] Verify SMTP configuration
- [ ] Set up domain and SSL certificate

### 2. Database Setup
- [ ] Create production PostgreSQL database
- [ ] Run database migrations
- [ ] Set up database backups
- [ ] Configure connection pooling

### 3. Security Configuration
- [ ] Generate secure NEXTAUTH_SECRET
- [ ] Configure CSRF protection
- [ ] Set up rate limiting with Redis
- [ ] Enable HTTPS/SSL
- [ ] Configure security headers

### 4. Performance Optimization
- [ ] Set up CDN for static assets
- [ ] Configure image optimization
- [ ] Enable compression
- [ ] Set up caching strategy

## 🚀 Deployment Options

### Option 1: Vercel (Recommended)

1. **Connect Repository**
   ```bash
   npm install -g vercel
   vercel
   ```

2. **Environment Variables**
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all variables from `.env.production`

3. **Database Setup**
   - Use Vercel Postgres or external provider
   - Update `DATABASE_URL` in environment variables

4. **Domain Configuration**
   - Add custom domain in Vercel dashboard
   - Update `NEXTAUTH_URL` and `NEXT_PUBLIC_SITE_URL`

### Option 2: Railway

1. **Deploy from GitHub**
   ```bash
   npm install -g @railway/cli
   railway login
   railway link
   railway up
   ```

2. **Environment Variables**
   - Set variables in Railway dashboard
   - Add PostgreSQL service

3. **Custom Domain**
   - Configure custom domain in Railway
   - Set up DNS records

### Option 3: DigitalOcean App Platform

1. **Create App**
   - Connect GitHub repository
   - Choose Node.js environment

2. **Environment Variables**
   - Add all production variables
   - Set up managed PostgreSQL database

3. **Build Configuration**
   ```yaml
   # app.yaml
   name: armaan-properties
   services:
   - name: web
     source_dir: /
     github:
       repo: your-username/your-repo
       branch: main
     run_command: npm start
     environment_slug: node-js
     instance_count: 1
     instance_size_slug: basic-xxs
     envs:
     - key: NODE_ENV
       value: production
   ```

### Option 4: VPS/Dedicated Server

1. **Server Setup**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2
   npm install -g pm2
   
   # Install Nginx
   sudo apt install nginx
   ```

2. **Application Deployment**
   ```bash
   # Clone repository
   git clone https://github.com/your-username/your-repo.git
   cd your-repo
   
   # Install dependencies
   npm ci --production
   
   # Build application
   npm run build
   
   # Start with PM2
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

3. **Nginx Configuration**
   ```nginx
   # /etc/nginx/sites-available/armaan-properties
   server {
       listen 80;
       server_name yourdomain.com www.yourdomain.com;
       return 301 https://$server_name$request_uri;
   }
   
   server {
       listen 443 ssl http2;
       server_name yourdomain.com www.yourdomain.com;
       
       ssl_certificate /path/to/ssl/cert.pem;
       ssl_certificate_key /path/to/ssl/private.key;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🗄️ Database Migration

### PostgreSQL Setup

1. **Create Database**
   ```sql
   CREATE DATABASE armaan_properties;
   CREATE USER armaan_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE armaan_properties TO armaan_user;
   ```

2. **Run Migrations**
   ```bash
   npm run db:push
   npm run db:seed # If you have seed data
   ```

3. **Create Admin User**
   ```bash
   npm run create-admin
   ```

### Database Providers

**Recommended Options:**
- **Vercel Postgres** (if using Vercel)
- **Railway PostgreSQL** (if using Railway)
- **Supabase** (managed PostgreSQL)
- **AWS RDS** (enterprise)
- **Google Cloud SQL** (enterprise)

## 📧 Email Configuration

### Gmail SMTP Setup

1. **Enable 2FA** on your Gmail account
2. **Generate App Password**:
   - Google Account → Security → 2-Step Verification → App passwords
   - Generate password for "Mail"

3. **Environment Variables**:
   ```env
   SMTP_HOST="smtp.gmail.com"
   SMTP_PORT="587"
   SMTP_SECURE="false"
   SMTP_USER="<EMAIL>"
   SMTP_PASSWORD="your-16-digit-app-password"
   ```

### Alternative Email Providers

- **SendGrid** (recommended for high volume)
- **Mailgun** (developer-friendly)
- **Amazon SES** (cost-effective)
- **Postmark** (reliable delivery)

## 🔐 Security Configuration

### SSL/HTTPS Setup

1. **Cloudflare** (Recommended)
   - Add your domain to Cloudflare
   - Enable "Always Use HTTPS"
   - Set SSL mode to "Full (strict)"

2. **Let's Encrypt** (For VPS)
   ```bash
   sudo snap install --classic certbot
   sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
   ```

### Security Headers

Add to `next.config.js`:
```javascript
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];

module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
};
```

## 📊 Analytics Setup

### Google Analytics 4

1. **Create GA4 Property**
   - Go to Google Analytics
   - Create new property
   - Get Measurement ID (G-XXXXXXXXXX)

2. **Add to Environment**
   ```env
   NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
   ```

### Google Search Console

1. **Add Property**
   - Go to Google Search Console
   - Add your domain
   - Verify ownership

2. **Submit Sitemap**
   - Add `https://yourdomain.com/sitemap.xml`

## 🚀 Performance Optimization

### CDN Setup

1. **Cloudflare** (Free)
   - Add domain to Cloudflare
   - Enable caching rules
   - Optimize images

2. **AWS CloudFront** (Advanced)
   - Create distribution
   - Configure caching behaviors

### Image Optimization

```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['your-domain.com'],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
};
```

## 📋 Post-Deployment Tasks

### 1. Testing
- [ ] Test all user flows
- [ ] Verify email sending
- [ ] Check database connections
- [ ] Test file uploads
- [ ] Validate analytics tracking

### 2. SEO Setup
- [ ] Submit sitemap to Google
- [ ] Set up Google My Business
- [ ] Configure social media meta tags
- [ ] Test page speed (PageSpeed Insights)

### 3. Monitoring
- [ ] Set up error tracking (Sentry)
- [ ] Configure uptime monitoring
- [ ] Set up performance monitoring
- [ ] Create backup schedules

### 4. Maintenance
- [ ] Set up automatic backups
- [ ] Configure log rotation
- [ ] Plan update schedules
- [ ] Monitor security alerts

## 🛠️ Maintenance Scripts

Create these scripts for ongoing maintenance:

```json
// package.json
{
  "scripts": {
    "db:backup": "pg_dump $DATABASE_URL > backup-$(date +%Y%m%d).sql",
    "db:restore": "psql $DATABASE_URL < backup.sql",
    "logs": "pm2 logs",
    "restart": "pm2 restart all",
    "update": "git pull && npm ci && npm run build && pm2 restart all"
  }
}
```

## 📞 Support

For deployment issues:
1. Check environment variables
2. Review application logs
3. Verify database connectivity
4. Check domain/DNS configuration
5. Contact hosting provider support

## 🔄 Rollback Plan

In case of issues:
1. **Quick Rollback**: Revert to previous deployment
2. **Database Rollback**: Restore from backup
3. **DNS Rollback**: Update DNS to previous server
4. **Gradual Rollback**: Use blue-green deployment