'use client'

import { Home } from 'lucide-react'
import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-neutral-900 text-white section-padding">
      <div className="container-custom">
        <div className="grid md:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Home className="w-6 h-6 text-primary-500" />
              <span className="text-xl font-display font-bold">Property Trendz</span>
            </div>
            <p className="text-neutral-300">
              Your trusted partner for finding the perfect property in Delhi NCR region.
            </p>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Quick Links</h4>
            <div className="space-y-2">
              <Link href="/properties" className="block text-neutral-300 hover:text-white transition-colors">
                All Properties
              </Link>
              <Link href="/about" className="block text-neutral-300 hover:text-white transition-colors">
                About Us
              </Link>
              <Link href="/contact" className="block text-neutral-300 hover:text-white transition-colors">
                Contact
              </Link>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Property Types</h4>
            <div className="space-y-2">
              <div className="text-neutral-300">Apartments</div>
              <div className="text-neutral-300">Independent Houses</div>
              <div className="text-neutral-300">Builder Floors</div>
              <div className="text-neutral-300">Commercial</div>
            </div>
          </div>
          <div>
            <h4 className="font-semibold mb-4">Contact Info</h4>
            <div className="space-y-2 text-neutral-300">
              <div>📞 +91 9810129777</div>
              <div>📧 <EMAIL></div>
              <div>📍 Delhi NCR, India</div>
            </div>
          </div>
        </div>
        <div className="border-t border-neutral-700 mt-12 pt-8 text-center text-neutral-400">
          <p>&copy; 2024 Property Trendz. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
} 