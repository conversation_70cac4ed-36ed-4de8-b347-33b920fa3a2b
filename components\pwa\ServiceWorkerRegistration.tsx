'use client';

import { useEffect } from 'react';

export function ServiceWorkerRegistration() {
  useEffect(() => {
    // Disable service worker in development to avoid offline issues
    if (typeof window !== 'undefined' && 'serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      const registerServiceWorker = async () => {
        try {
          // Register the service worker
          const registration = await navigator.serviceWorker.register('/sw.js', {
            scope: '/',
          });

          console.log('Service Worker registered successfully:', registration);

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, show update notification
                  if (confirm('A new version is available. Would you like to reload to update?')) {
                    window.location.reload();
                  }
                }
              });
            }
          });

          // Handle service worker messages
          navigator.serviceWorker.addEventListener('message', (event) => {
            console.log('Service Worker message:', event.data);
            
            if (event.data && event.data.type === 'CACHE_UPDATED') {
              // Show notification that content has been cached for offline use
              console.log('Content cached for offline use');
            }
          });

          // Request notification permission for push notifications
          if ('Notification' in window && Notification.permission === 'default') {
            const permission = await Notification.requestPermission();
            console.log('Notification permission:', permission);
          }

          // Register for background sync if supported
          if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            // Background sync will be handled by the service worker
            console.log('Background sync is supported');
          }

          // Check if app is running in standalone mode (PWA)
          const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
          const isInWebAppiOS = (window.navigator as any).standalone === true;
          
          if (isStandalone || isInWebAppiOS) {
            console.log('Running as PWA');
            // Add PWA-specific styling or behavior
            document.body.classList.add('pwa-mode');
          }

          return registration;
        } catch (error) {
          console.error('Service Worker registration failed:', error);
        }
      };

      // Register on page load
      registerServiceWorker();

      // Listen for online/offline events
      const handleOnline = () => {
        console.log('App is online');
        // Trigger background sync if there are pending operations
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
          navigator.serviceWorker.ready.then((registration) => {
            return (registration as any).sync.register('property-inquiry');
          }).catch((error) => {
            console.error('Background sync registration failed:', error);
          });
        }
      };

      const handleOffline = () => {
        console.log('App is offline');
        // Show offline indicator or disable certain features
      };

      window.addEventListener('online', handleOnline);
      window.addEventListener('offline', handleOffline);

      // Cleanup
      return () => {
        window.removeEventListener('online', handleOnline);
        window.removeEventListener('offline', handleOffline);
      };
    }
  }, []);

  return null;
}

// Hook for PWA installation
export function usePWAInstall() {
  useEffect(() => {
    let deferredPrompt: any = null;

    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent Chrome 67 and earlier from automatically showing the prompt
      e.preventDefault();
      // Stash the event so it can be triggered later
      deferredPrompt = e;
      
      // Show your own install promotion UI
      console.log('PWA install prompt available');
    };

    const handleAppInstalled = () => {
      console.log('PWA was installed');
      deferredPrompt = null;
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const installPWA = () => {
    // Implementation would trigger the deferred prompt
    console.log('Install PWA triggered');
  };

  return { installPWA };
}

// Component for install banner
export function PWAInstallBanner() {
  const { installPWA } = usePWAInstall();

  return (
    <div className="hidden" id="pwa-install-banner">
      <div className="bg-blue-500 text-white p-4 text-center">
        <p className="text-sm mb-2">Install Property Trendz for easy access to property listings!</p>
        <button
          onClick={installPWA}
          className="bg-white text-blue-500 px-4 py-2 rounded text-sm font-medium hover:bg-gray-100"
        >
          Install App
        </button>
      </div>
    </div>
  );
}