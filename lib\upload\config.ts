import sharp from 'sharp';
import { createHash, randomBytes } from 'crypto';
import path from 'path';
import fs from 'fs/promises';

// Upload configuration
export const uploadConfig = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedMimeTypes: [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp',
    'application/pdf',
  ],
  allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp', '.pdf'],
  uploadDir: process.env.UPLOAD_DIR || './public/uploads',
  maxFilesPerUpload: 10,
  imageSizes: {
    thumbnail: { width: 300, height: 200 },
    medium: { width: 800, height: 600 },
    large: { width: 1200, height: 900 },
  },
};

// File validation
export function validateFile(file: File): { isValid: boolean; error?: string } {
  // Check file size
  if (file.size > uploadConfig.maxFileSize) {
    return {
      isValid: false,
      error: `File size exceeds ${Math.round(uploadConfig.maxFileSize / 1024 / 1024)}MB limit`,
    };
  }

  // Check MIME type
  if (!uploadConfig.allowedMimeTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type ${file.type} is not allowed`,
    };
  }

  // Check file extension
  const extension = path.extname(file.name).toLowerCase();
  if (!uploadConfig.allowedExtensions.includes(extension)) {
    return {
      isValid: false,
      error: `File extension ${extension} is not allowed`,
    };
  }

  // Check filename for malicious patterns
  const filename = file.name;
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return {
      isValid: false,
      error: 'Invalid filename',
    };
  }

  return { isValid: true };
}

// Generate secure filename
export function generateSecureFilename(originalName: string, userId?: string): string {
  const extension = path.extname(originalName).toLowerCase();
  const timestamp = Date.now();
  const random = randomBytes(8).toString('hex');
  const userPrefix = userId ? createHash('md5').update(userId).digest('hex').substring(0, 8) : 'anon';
  
  return `${userPrefix}_${timestamp}_${random}${extension}`;
}

// File upload result interface
export interface UploadResult {
  success: boolean;
  filename?: string;
  url?: string;
  thumbnailUrl?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    mimeType: string;
    dimensions?: { width: number; height: number };
  };
}

// Process and save image
export async function processAndSaveImage(
  buffer: Buffer,
  filename: string,
  category: 'property' | 'user' | 'document' = 'property'
): Promise<UploadResult> {
  try {
    const categoryDir = path.join(uploadConfig.uploadDir, category);
    
    // Ensure directory exists
    await fs.mkdir(categoryDir, { recursive: true });
    
    const fullPath = path.join(categoryDir, filename);
    
    // Process image with Sharp
    const image = sharp(buffer);
    const metadata = await image.metadata();
    
    // Validate image
    if (!metadata.width || !metadata.height) {
      return { success: false, error: 'Invalid image file' };
    }

    // Check image dimensions (reasonable limits)
    if (metadata.width > 4000 || metadata.height > 4000) {
      return { success: false, error: 'Image dimensions too large (max 4000x4000)' };
    }

    // Process and save original (optimized)
    await image
      .jpeg({ quality: 85, progressive: true })
      .toFile(fullPath);

    // Generate thumbnail
    const thumbnailFilename = filename.replace(/\.(jpg|jpeg|png|webp)$/i, '_thumb.jpg');
    const thumbnailPath = path.join(categoryDir, thumbnailFilename);
    
    await image
      .resize(uploadConfig.imageSizes.thumbnail.width, uploadConfig.imageSizes.thumbnail.height, {
        fit: 'cover',
        position: 'center',
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    
    return {
      success: true,
      filename,
      url: `${baseUrl}/uploads/${category}/${filename}`,
      thumbnailUrl: `${baseUrl}/uploads/${category}/${thumbnailFilename}`,
      metadata: {
        originalName: filename,
        size: buffer.length,
        mimeType: 'image/jpeg',
        dimensions: { width: metadata.width, height: metadata.height },
      },
    };
  } catch (error) {
    console.error('Error processing image:', error);
    return { success: false, error: 'Failed to process image' };
  }
}

// Save non-image file
export async function saveFile(
  buffer: Buffer,
  filename: string,
  mimeType: string,
  originalName: string,
  category: 'property' | 'user' | 'document' = 'document'
): Promise<UploadResult> {
  try {
    const categoryDir = path.join(uploadConfig.uploadDir, category);
    
    // Ensure directory exists
    await fs.mkdir(categoryDir, { recursive: true });
    
    const fullPath = path.join(categoryDir, filename);
    
    // Save file
    await fs.writeFile(fullPath, buffer);
    
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
    
    return {
      success: true,
      filename,
      url: `${baseUrl}/uploads/${category}/${filename}`,
      metadata: {
        originalName,
        size: buffer.length,
        mimeType,
      },
    };
  } catch (error) {
    console.error('Error saving file:', error);
    return { success: false, error: 'Failed to save file' };
  }
}

// Delete file
export async function deleteFile(filename: string, category: 'property' | 'user' | 'document'): Promise<boolean> {
  try {
    const filePath = path.join(uploadConfig.uploadDir, category, filename);
    
    // Also try to delete thumbnail if it's an image
    const thumbnailFilename = filename.replace(/\.(jpg|jpeg|png|webp)$/i, '_thumb.jpg');
    const thumbnailPath = path.join(uploadConfig.uploadDir, category, thumbnailFilename);
    
    await Promise.all([
      fs.unlink(filePath).catch(() => {}), // Ignore errors if file doesn't exist
      fs.unlink(thumbnailPath).catch(() => {}),
    ]);
    
    return true;
  } catch (error) {
    console.error('Error deleting file:', error);
    return false;
  }
}

// Scan file for malware (basic pattern detection)
export function basicMalwareCheck(buffer: Buffer, filename: string): { isSafe: boolean; reason?: string } {
  // Check for executable file signatures
  const signatures = [
    { pattern: [0x4D, 0x5A], name: 'PE Executable' }, // PE header
    { pattern: [0x50, 0x4B, 0x03, 0x04], name: 'ZIP Archive' }, // ZIP (could contain malware)
    { pattern: [0x50, 0x4B, 0x05, 0x06], name: 'ZIP Archive' },
    { pattern: [0x50, 0x4B, 0x07, 0x08], name: 'ZIP Archive' },
    { pattern: [0x7F, 0x45, 0x4C, 0x46], name: 'ELF Executable' }, // Linux executable
    { pattern: [0xCF, 0xFA, 0xED, 0xFE], name: 'Mach-O Executable' }, // macOS executable
  ];

  // Check file header for suspicious signatures
  for (const sig of signatures) {
    if (buffer.length >= sig.pattern.length) {
      let matches = true;
      for (let i = 0; i < sig.pattern.length; i++) {
        if (buffer[i] !== sig.pattern[i]) {
          matches = false;
          break;
        }
      }
      if (matches) {
        return { isSafe: false, reason: `Detected ${sig.name}` };
      }
    }
  }

  // Check filename for suspicious extensions
  const suspiciousExtensions = [
    '.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar',
    '.app', '.deb', '.rpm', '.dmg', '.pkg', '.msi', '.sh', '.ps1'
  ];

  const extension = path.extname(filename).toLowerCase();
  if (suspiciousExtensions.includes(extension)) {
    return { isSafe: false, reason: `Suspicious file extension: ${extension}` };
  }

  // Check for embedded scripts in image files
  const bufferString = buffer.toString('ascii');
  const scriptPatterns = [
    '<script',
    'javascript:',
    'eval(',
    'document.write',
    '<?php',
    '#!/bin/sh',
  ];

  for (const pattern of scriptPatterns) {
    if (bufferString.toLowerCase().includes(pattern)) {
      return { isSafe: false, reason: 'Embedded script detected' };
    }
  }

  return { isSafe: true };
}

// Clean up old files (for maintenance)
export async function cleanupOldFiles(daysOld = 30): Promise<{ deleted: number; errors: number }> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysOld);

  let deleted = 0;
  let errors = 0;

  try {
    const categories = ['property', 'user', 'document'];
    
    for (const category of categories) {
      const categoryDir = path.join(uploadConfig.uploadDir, category);
      
      try {
        const files = await fs.readdir(categoryDir);
        
        for (const file of files) {
          try {
            const filePath = path.join(categoryDir, file);
            const stats = await fs.stat(filePath);
            
            if (stats.mtime < cutoffDate) {
              await fs.unlink(filePath);
              deleted++;
            }
          } catch (error) {
            errors++;
          }
        }
      } catch (error) {
        // Directory might not exist
      }
    }
  } catch (error) {
    console.error('Error during cleanup:', error);
    errors++;
  }

  return { deleted, errors };
}