'use client';

import { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { motion } from 'framer-motion';
import { Button } from './Button';
import { analytics } from '@/components/analytics/GoogleAnalytics';
import { useRouter } from 'next/navigation';

interface FavoriteButtonProps {
  propertyId: string;
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export function FavoriteButton({ 
  propertyId, 
  className = '', 
  showText = false,
  size = 'md' 
}: FavoriteButtonProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [isFavorite, setIsFavorite] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Check favorite status on component mount
  useEffect(() => {
    if (session?.user && propertyId) {
      checkFavoriteStatus();
    }
  }, [session, propertyId]);

  const checkFavoriteStatus = async () => {
    try {
      const response = await fetch(`/api/favorites/check?propertyId=${propertyId}`);
      const data = await response.json();
      setIsFavorite(data.isFavorite);
    } catch (error) {
      console.error('Error checking favorite status:', error);
    }
  };

  const toggleFavorite = async (event?: React.MouseEvent) => {
    // Prevent parent Link from being triggered
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Validate propertyId
    if (!propertyId) {
      console.error('Invalid propertyId for favorite:', propertyId);
      return;
    }

    if (!session?.user) {
      // Redirect to login or show login modal
      router.push('/auth/signin');
      return;
    }

    setIsLoading(true);

    try {
      if (isFavorite) {
        // Remove from favorites
        const response = await fetch(`/api/favorites?propertyId=${propertyId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          setIsFavorite(false);
          analytics.removeFromFavorites(propertyId);
          if (typeof window !== 'undefined' && window.showToast) {
            window.showToast('Property removed from favorites');
          }
        } else {
          const errorData = await response.json();
          console.error('Error removing favorite:', errorData);
          alert('Failed to remove from favorites: ' + (errorData.error || 'Unknown error'));
        }
      } else {
        // Add to favorites
        const response = await fetch('/api/favorites', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ propertyId }),
        });

        if (response.ok) {
          setIsFavorite(true);
          analytics.addToFavorites(propertyId);
          if (typeof window !== 'undefined' && window.showToast) {
            window.showToast('Property added to favorites');
          }
        } else {
          const errorData = await response.json();
          console.error('Error adding favorite:', errorData);
          alert('Failed to add to favorites: ' + (errorData.error || 'Unknown error'));
        }
      }
    } catch (error) {
      console.error('Error toggling favorite:', error);
      alert('Network error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-8 h-8';
      case 'lg':
        return 'w-12 h-12';
      default:
        return 'w-10 h-10';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  if (showText) {
    return (
      <Button
        variant={isFavorite ? 'default' : 'outline'}
        onClick={(e) => toggleFavorite(e)}
        disabled={isLoading}
        className={`${className} ${isFavorite ? 'bg-red-500 hover:bg-red-600 border-red-500' : 'border-gray-300 hover:border-red-500'}`}
      >
        <motion.div
          animate={{ scale: isFavorite ? 1.2 : 1 }}
          transition={{ duration: 0.2 }}
        >
          <Heart 
            className={`${getIconSize()} mr-2 ${isFavorite ? 'fill-white text-white' : 'text-gray-600'}`} 
          />
        </motion.div>
        {isFavorite ? 'Saved' : 'Save'}
      </Button>
    );
  }

  return (
    <motion.button
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      onClick={(e) => toggleFavorite(e)}
      disabled={isLoading}
      className={`${getSizeClasses()} rounded-full bg-white/80 backdrop-blur-sm border border-gray-200 hover:bg-white hover:shadow-lg transition-all duration-300 flex items-center justify-center ${className}`}
    >
      <motion.div
        animate={{ 
          scale: isFavorite ? 1.2 : 1,
          rotate: isFavorite ? 360 : 0 
        }}
        transition={{ duration: 0.3 }}
      >
        <Heart 
          className={`${getIconSize()} transition-colors duration-300 ${
            isFavorite 
              ? 'fill-red-500 text-red-500' 
              : 'text-gray-600 hover:text-red-500'
          }`} 
        />
      </motion.div>
    </motion.button>
  );
}