import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { db } from '@/lib/db/config';
import { savedSearches, properties, users } from '@/lib/db/schema';
import { eq, and, gte, lte, like, inArray, sql, desc } from 'drizzle-orm';
import { sendBulkEmails, emailTemplates } from '@/lib/email/config';

// POST /api/email-alerts/send - Send email alerts for new properties (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;
    
    // Only admin can trigger email alerts manually
    if (!session?.user?.id || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { frequency = 'daily', limit = 100 } = await request.json();

    console.log(`Starting email alert job for ${frequency} frequency...`);

    // Get all active saved searches for the specified frequency
    const activeSearches = await db
      .select({
        savedSearch: savedSearches,
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
        },
      })
      .from(savedSearches)
      .innerJoin(users, eq(savedSearches.userId, users.id))
      .where(and(
        eq(savedSearches.isActive, true),
        eq(savedSearches.emailAlerts, true),
        eq(savedSearches.frequency, frequency)
      ))
      .limit(limit);

    if (activeSearches.length === 0) {
      return NextResponse.json({
        message: `No active saved searches found for ${frequency} frequency`,
        processed: 0,
        emailsSent: 0,
      });
    }

    console.log(`Found ${activeSearches.length} active saved searches`);

    const emailsToSend: Array<{
      to: string;
      subject: string;
      html: string;
    }> = [];

    let totalMatches = 0;

    // Process each saved search
    for (const { savedSearch, user } of activeSearches) {
      try {
        // Calculate date threshold based on frequency
        const now = new Date();
        let dateThreshold = new Date();
        
        switch (frequency) {
          case 'daily':
            dateThreshold.setDate(now.getDate() - 1);
            break;
          case 'weekly':
            dateThreshold.setDate(now.getDate() - 7);
            break;
          case 'monthly':
            dateThreshold.setMonth(now.getMonth() - 1);
            break;
        }

        // Only send if we haven't sent recently
        const shouldSend = !savedSearch.lastSent || 
          new Date(savedSearch.lastSent) < dateThreshold;

        if (!shouldSend) {
          console.log(`Skipping search "${savedSearch.name}" - recently sent`);
          continue;
        }

        // Build query based on search parameters
        const params = savedSearch.searchParams as any;
        let query = db.select().from(properties);
        const conditions: any[] = [
          eq(properties.published, true),
          eq(properties.status, 'available'),
          gte(properties.createdAt, dateThreshold)
        ];

        // Apply search filters
        if (params.search) {
          conditions.push(
            sql`(${properties.title} ILIKE ${`%${params.search}%`} OR ${properties.description} ILIKE ${`%${params.search}%`})`
          );
        }

        if (params.location) {
          conditions.push(
            sql`(${properties.area} ILIKE ${`%${params.location}%`} OR ${properties.city} ILIKE ${`%${params.location}%`})`
          );
        }

        if (params.propertyType) {
          conditions.push(eq(properties.propertyType, params.propertyType));
        }

        if (params.listingType) {
          conditions.push(eq(properties.listingType, params.listingType));
        }

        if (params.minPrice !== undefined) {
          conditions.push(gte(properties.price, params.minPrice.toString()));
        }

        if (params.maxPrice !== undefined) {
          conditions.push(lte(properties.price, params.maxPrice.toString()));
        }

        if (params.bedrooms !== undefined) {
          conditions.push(eq(properties.bedrooms, params.bedrooms));
        }

        if (params.bathrooms !== undefined) {
          conditions.push(eq(properties.bathrooms, params.bathrooms));
        }

        if (params.minArea !== undefined) {
          conditions.push(gte(properties.totalArea, params.minArea));
        }

        if (params.maxArea !== undefined) {
          conditions.push(lte(properties.totalArea, params.maxArea));
        }

        if (params.furnished) {
          conditions.push(eq(properties.furnished, params.furnished));
        }

        // Execute query
        const matchingProperties = await query
          .where(and(...conditions))
          .orderBy(desc(properties.createdAt))
          .limit(20); // Limit to 20 properties per email

        if (matchingProperties.length > 0) {
          console.log(`Found ${matchingProperties.length} matching properties for "${savedSearch.name}"`);
          
          totalMatches += matchingProperties.length;

          // Generate unsubscribe URL
          const unsubscribeUrl = `${process.env.NEXT_PUBLIC_SITE_URL}/unsubscribe?token=${Buffer.from(`${user.id}:${savedSearch.id}`).toString('base64')}`;

          // Prepare email
          const emailHtml = emailTemplates.propertyAlert.getHtml(
            matchingProperties,
            savedSearch.name,
            unsubscribeUrl
          );

          emailsToSend.push({
            to: user.email!,
            subject: `New Properties: ${savedSearch.name} - ${matchingProperties.length} matches`,
            html: emailHtml,
          });

          // Update lastSent timestamp
          await db
            .update(savedSearches)
            .set({ lastSent: now })
            .where(eq(savedSearches.id, savedSearch.id));
        } else {
          console.log(`No new properties found for "${savedSearch.name}"`);
        }
      } catch (error) {
        console.error(`Error processing saved search "${savedSearch.name}":`, error);
      }
    }

    // Send emails in batches
    let emailResults = { sent: 0, failed: 0 };
    
    if (emailsToSend.length > 0) {
      console.log(`Sending ${emailsToSend.length} email alerts...`);
      emailResults = await sendBulkEmails(emailsToSend, 5, 2000); // 5 emails per batch, 2 second delay
    }

    return NextResponse.json({
      message: 'Email alert job completed',
      processed: activeSearches.length,
      totalMatches,
      emailsSent: emailResults.sent,
      emailsFailed: emailResults.failed,
      frequency,
    });

  } catch (error) {
    console.error('Error sending email alerts:', error);
    return NextResponse.json(
      { error: 'Failed to send email alerts' },
      { status: 500 }
    );
  }
}