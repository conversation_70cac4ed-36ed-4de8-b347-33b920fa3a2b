{"id": "2583957c-2c4d-455f-a84a-b6694901f546", "prevId": "********-0000-0000-0000-********0000", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"accounts_user_idx": {"name": "accounts_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "accounts_provider_idx": {"name": "accounts_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider_account_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_messages": {"name": "chat_messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "room_id": {"name": "room_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "uuid", "primaryKey": false, "notNull": true}, "sender_name": {"name": "sender_name", "type": "text", "primaryKey": false, "notNull": true}, "sender_role": {"name": "sender_role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'user'"}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'text'"}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_messages_room_idx": {"name": "chat_messages_room_idx", "columns": [{"expression": "room_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_messages_sender_idx": {"name": "chat_messages_sender_idx", "columns": [{"expression": "sender_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_messages_created_at_idx": {"name": "chat_messages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_messages_room_id_chat_rooms_id_fk": {"name": "chat_messages_room_id_chat_rooms_id_fk", "tableFrom": "chat_messages", "tableTo": "chat_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_messages_sender_id_users_id_fk": {"name": "chat_messages_sender_id_users_id_fk", "tableFrom": "chat_messages", "tableTo": "users", "columnsFrom": ["sender_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_messages_property_id_properties_id_fk": {"name": "chat_messages_property_id_properties_id_fk", "tableFrom": "chat_messages", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat_rooms": {"name": "chat_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_name": {"name": "user_name", "type": "text", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "text", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_message": {"name": "last_message", "type": "text", "primaryKey": false, "notNull": false}, "last_message_at": {"name": "last_message_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chat_rooms_user_idx": {"name": "chat_rooms_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_rooms_status_idx": {"name": "chat_rooms_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chat_rooms_property_idx": {"name": "chat_rooms_property_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chat_rooms_user_id_users_id_fk": {"name": "chat_rooms_user_id_users_id_fk", "tableFrom": "chat_rooms", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chat_rooms_property_id_properties_id_fk": {"name": "chat_rooms_property_id_properties_id_fk", "tableFrom": "chat_rooms", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.enquiries": {"name": "enquiries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "enquiry_type": {"name": "enquiry_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'general'"}, "budget": {"name": "budget", "type": "numeric(15, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'new'"}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'website'"}, "assigned_to": {"name": "assigned_to", "type": "uuid", "primaryKey": false, "notNull": false}, "follow_up_date": {"name": "follow_up_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"enquiries_property_idx": {"name": "enquiries_property_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "enquiries_status_idx": {"name": "enquiries_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "enquiries_email_idx": {"name": "enquiries_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"enquiries_property_id_properties_id_fk": {"name": "enquiries_property_id_properties_id_fk", "tableFrom": "enquiries", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "enquiries_user_id_users_id_fk": {"name": "enquiries_user_id_users_id_fk", "tableFrom": "enquiries", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "enquiries_assigned_to_users_id_fk": {"name": "enquiries_assigned_to_users_id_fk", "tableFrom": "enquiries", "tableTo": "users", "columnsFrom": ["assigned_to"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.favorites": {"name": "favorites", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"favorites_user_property_idx": {"name": "favorites_user_property_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"favorites_user_id_users_id_fk": {"name": "favorites_user_id_users_id_fk", "tableFrom": "favorites", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "favorites_property_id_properties_id_fk": {"name": "favorites_property_id_properties_id_fk", "tableFrom": "favorites", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.properties": {"name": "properties", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "price_per_sqft": {"name": "price_per_sqft", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "area": {"name": "area", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "pincode": {"name": "pincode", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "numeric(10, 8)", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "numeric(11, 8)", "primaryKey": false, "notNull": false}, "bedrooms": {"name": "bedrooms", "type": "integer", "primaryKey": false, "notNull": false}, "bathrooms": {"name": "bathrooms", "type": "integer", "primaryKey": false, "notNull": false}, "total_area": {"name": "total_area", "type": "integer", "primaryKey": false, "notNull": false}, "carpet_area": {"name": "carpet_area", "type": "integer", "primaryKey": false, "notNull": false}, "built_up_area": {"name": "built_up_area", "type": "integer", "primaryKey": false, "notNull": false}, "floors": {"name": "floors", "type": "integer", "primaryKey": false, "notNull": false}, "total_floors": {"name": "total_floors", "type": "integer", "primaryKey": false, "notNull": false}, "parking": {"name": "parking", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "balconies": {"name": "balconies", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "furnished": {"name": "furnished", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'unfurnished'"}, "age_of_property": {"name": "age_of_property", "type": "integer", "primaryKey": false, "notNull": false}, "facing": {"name": "facing", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "property_type": {"name": "property_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "sub_type": {"name": "sub_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "listing_type": {"name": "listing_type", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'available'"}, "amenities": {"name": "amenities", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "nearby_places": {"name": "nearby_places", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "videos": {"name": "videos", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "documents": {"name": "documents", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "virtual_tour_url": {"name": "virtual_tour_url", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": false}, "agent_id": {"name": "agent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "contact_name": {"name": "contact_name", "type": "text", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_whatsapp": {"name": "contact_whatsapp", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "featured": {"name": "featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "published": {"name": "published", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "views": {"name": "views", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "enquiries": {"name": "enquiries", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "meta_title": {"name": "meta_title", "type": "text", "primaryKey": false, "notNull": false}, "meta_description": {"name": "meta_description", "type": "text", "primaryKey": false, "notNull": false}, "meta_keywords": {"name": "meta_keywords", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sold_at": {"name": "sold_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"properties_slug_idx": {"name": "properties_slug_idx", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_city_idx": {"name": "properties_city_idx", "columns": [{"expression": "city", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_area_idx": {"name": "properties_area_idx", "columns": [{"expression": "area", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_type_idx": {"name": "properties_type_idx", "columns": [{"expression": "property_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_listing_type_idx": {"name": "properties_listing_type_idx", "columns": [{"expression": "listing_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_status_idx": {"name": "properties_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_price_idx": {"name": "properties_price_idx", "columns": [{"expression": "price", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_featured_idx": {"name": "properties_featured_idx", "columns": [{"expression": "featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_published_idx": {"name": "properties_published_idx", "columns": [{"expression": "published", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "properties_location_idx": {"name": "properties_location_idx", "columns": [{"expression": "latitude", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "longitude", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"properties_owner_id_users_id_fk": {"name": "properties_owner_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "properties_agent_id_users_id_fk": {"name": "properties_agent_id_users_id_fk", "tableFrom": "properties", "tableTo": "users", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"properties_slug_unique": {"name": "properties_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.property_images": {"name": "property_images", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "alt": {"name": "alt", "type": "text", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'photo'"}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"property_images_property_idx": {"name": "property_images_property_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_images_order_idx": {"name": "property_images_order_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"property_images_property_id_properties_id_fk": {"name": "property_images_property_id_properties_id_fk", "tableFrom": "property_images", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.property_views": {"name": "property_views", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "property_id": {"name": "property_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "referrer": {"name": "referrer", "type": "text", "primaryKey": false, "notNull": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"property_views_property_idx": {"name": "property_views_property_idx", "columns": [{"expression": "property_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_views_user_idx": {"name": "property_views_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "property_views_date_idx": {"name": "property_views_date_idx", "columns": [{"expression": "viewed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"property_views_property_id_properties_id_fk": {"name": "property_views_property_id_properties_id_fk", "tableFrom": "property_views", "tableTo": "properties", "columnsFrom": ["property_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "property_views_user_id_users_id_fk": {"name": "property_views_user_id_users_id_fk", "tableFrom": "property_views", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.saved_searches": {"name": "saved_searches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "search_params": {"name": "search_params", "type": "jsonb", "primaryKey": false, "notNull": true}, "email_alerts": {"name": "email_alerts", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "frequency": {"name": "frequency", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'daily'"}, "last_sent": {"name": "last_sent", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"saved_searches_user_idx": {"name": "saved_searches_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"saved_searches_user_id_users_id_fk": {"name": "saved_searches_user_id_users_id_fk", "tableFrom": "saved_searches", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"sessions_user_idx": {"name": "sessions_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'user'"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'credentials'"}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"verification_tokens_identifier_token_idx": {"name": "verification_tokens_identifier_token_idx", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}