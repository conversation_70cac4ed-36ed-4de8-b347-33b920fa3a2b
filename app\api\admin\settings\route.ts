import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import fs from 'fs/promises';
import path from 'path';

const SETTINGS_FILE = path.join(process.cwd(), 'data', 'settings.json');

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.join(process.cwd(), 'data');
  try {
    await fs.access(dataDir);
  } catch {
    await fs.mkdir(dataDir, { recursive: true });
  }
}

// GET - Get website settings
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    await ensureDataDirectory();

    try {
      const data = await fs.readFile(SETTINGS_FILE, 'utf-8');
      const settings = JSON.parse(data);
      return NextResponse.json(settings);
    } catch (error) {
      // Return default settings if file doesn't exist
      const defaultSettings = {
        siteName: 'Property Trendz',
        siteDescription: 'Find Your Dream Home in Delhi NCR',
        siteKeywords: 'real estate, properties, Delhi NCR, apartments, houses',
        contactPhone: '+91 98765 43210',
        contactEmail: '<EMAIL>',
        contactWhatsapp: '+91 98765 43210',
        address: 'Delhi NCR, India',
        socialMedia: {
          facebook: '',
          instagram: '',
          twitter: '',
          linkedin: '',
        },
        seo: {
          metaTitle: 'Property Trendz - Find Your Dream Home in Delhi NCR',
          metaDescription: 'Discover the best properties in Delhi NCR with Property Trendz. Houses, apartments, and commercial spaces for sale and rent.',
          ogImage: '',
          analyticsId: '',
        },
        aboutContent: `Property Trendz is a leading real estate company in Delhi NCR, 
        dedicated to helping you find your dream home. With years of experience in the industry, 
        we provide comprehensive services for buying, selling, and renting properties across 
        Delhi, Gurgaon, Noida, and surrounding areas.`,
        servicesContent: `We offer a wide range of real estate services including:
        • Property buying and selling
        • Rental services
        • Property management
        • Investment consultation
        • Legal assistance
        • Home loans assistance`,
        heroTitle: 'Find Your Dream Home',
        heroSubtitle: 'Discover the best properties in Delhi NCR',
        heroImage: '',
        updatedAt: new Date().toISOString(),
      };
      
      return NextResponse.json(defaultSettings);
    }
  } catch (error) {
    console.error('Settings GET error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Update website settings
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const settings = await request.json();
    settings.updatedAt = new Date().toISOString();
    settings.updatedBy = session.user.id;

    await ensureDataDirectory();
    await fs.writeFile(SETTINGS_FILE, JSON.stringify(settings, null, 2));

    return NextResponse.json({
      message: 'Settings updated successfully',
      settings,
    });
  } catch (error) {
    console.error('Settings POST error:', error);
    return NextResponse.json(
      { message: 'Failed to update settings' },
      { status: 500 }
    );
  }
}