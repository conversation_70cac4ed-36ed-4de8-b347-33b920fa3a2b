import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { savedSearches } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// POST /api/unsubscribe - Unsubscribe from email alerts
export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Unsubscribe token is required' },
        { status: 400 }
      );
    }

    // Decode token to get user ID and search ID
    let userId: string;
    let searchId: string;

    try {
      const decoded = Buffer.from(token, 'base64').toString('utf-8');
      const parts = decoded.split(':');
      
      if (parts.length !== 2) {
        throw new Error('Invalid token format');
      }

      [userId, searchId] = parts;
    } catch (error) {
      return NextResponse.json(
        { error: 'Invalid unsubscribe token' },
        { status: 400 }
      );
    }

    // Update the saved search to disable email alerts
    const updatedSearch = await db
      .update(savedSearches)
      .set({
        emailAlerts: false,
        updatedAt: new Date(),
      })
      .where(and(
        eq(savedSearches.id, searchId),
        eq(savedSearches.userId, userId)
      ))
      .returning();

    if (updatedSearch.length === 0) {
      return NextResponse.json(
        { error: 'Saved search not found or already unsubscribed' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Successfully unsubscribed from email alerts for this search',
      searchName: updatedSearch[0].name,
    });

  } catch (error) {
    console.error('Error unsubscribing from email alerts:', error);
    return NextResponse.json(
      { error: 'Failed to unsubscribe from email alerts' },
      { status: 500 }
    );
  }
}