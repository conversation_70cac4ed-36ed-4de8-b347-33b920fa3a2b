'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Save, 
  Upload, 
  X, 
  Plus, 
  MapPin, 
  Home, 
  DollarSign,
  Camera,
  FileText,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from 'react-hot-toast';

interface PropertyFormData {
  title: string;
  description: string;
  price: string;
  pricePerSqft: string;
  area: string;
  city: string;
  state: string;
  address: string;
  pincode: string;
  latitude: string;
  longitude: string;
  bedrooms: string;
  bathrooms: string;
  totalArea: string;
  carpetArea: string;
  builtUpArea: string;
  floors: string;
  totalFloors: string;
  parking: string;
  balconies: string;
  furnished: string;
  ageOfProperty: string;
  facing: string;
  propertyType: string;
  subType: string;
  listingType: string;
  status: string;
  amenities: string[];
  features: string[];
  images: string[];
  contactName: string;
  contactPhone: string;
  contactEmail: string;
  contactWhatsapp: string;
  featured: boolean;
  verified: boolean;
  published: boolean;
  metaTitle: string;
  metaDescription: string;
  metaKeywords: string;
}

const initialFormData: PropertyFormData = {
  title: '',
  description: '',
  price: '',
  pricePerSqft: '',
  area: '',
  city: '',
  state: 'Delhi NCR',
  address: '',
  pincode: '',
  latitude: '',
  longitude: '',
  bedrooms: '',
  bathrooms: '',
  totalArea: '',
  carpetArea: '',
  builtUpArea: '',
  floors: '',
  totalFloors: '',
  parking: '0',
  balconies: '0',
  furnished: 'unfurnished',
  ageOfProperty: '',
  facing: '',
  propertyType: 'apartment',
  subType: '',
  listingType: 'sale',
  status: 'available',
  amenities: [],
  features: [],
  images: [],
  contactName: '',
  contactPhone: '',
  contactEmail: '',
  contactWhatsapp: '',
  featured: false,
  verified: true,
  published: true,
  metaTitle: '',
  metaDescription: '',
  metaKeywords: '',
};

export function PropertyForm({ property }: { property?: any }) {
  const [formData, setFormData] = useState<PropertyFormData>(
    property ? { ...initialFormData, ...property } : initialFormData
  );
  const [loading, setLoading] = useState(false);
  const [activeSection, setActiveSection] = useState('basic');
  const [newAmenity, setNewAmenity] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const router = useRouter();

  const handleInputChange = (field: keyof PropertyFormData, value: string | boolean | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const addAmenity = () => {
    if (newAmenity.trim()) {
      setFormData(prev => ({
        ...prev,
        amenities: [...prev.amenities, newAmenity.trim()]
      }));
      setNewAmenity('');
    }
  };

  const removeAmenity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      amenities: prev.amenities.filter((_, i) => i !== index)
    }));
  };

  const addFeature = () => {
    if (newFeature.trim()) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const handleImageUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();
        return data.url;
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to upload image');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      toast.error('Failed to upload image');
    }
    return null;
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setLoading(true);

    try {
      const uploadPromises = files.map(file => handleImageUpload(file));
      const uploadedUrls = await Promise.all(uploadPromises);
      const validUrls = uploadedUrls.filter(url => url !== null);

      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...validUrls]
      }));

      if (validUrls.length > 0) {
        toast.success(`${validUrls.length} image(s) uploaded successfully`);
      }
    } catch (error) {
      toast.error('Failed to upload images');
    } finally {
      setLoading(false);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const slug = generateSlug(formData.title);
      const submitData = {
        ...formData,
        slug,
        price: parseFloat(formData.price) || 0,
        pricePerSqft: parseFloat(formData.pricePerSqft) || null,
        latitude: parseFloat(formData.latitude) || null,
        longitude: parseFloat(formData.longitude) || null,
        bedrooms: parseInt(formData.bedrooms) || null,
        bathrooms: parseInt(formData.bathrooms) || null,
        totalArea: parseInt(formData.totalArea) || null,
        carpetArea: parseInt(formData.carpetArea) || null,
        builtUpArea: parseInt(formData.builtUpArea) || null,
        floors: parseInt(formData.floors) || null,
        totalFloors: parseInt(formData.totalFloors) || null,
        parking: parseInt(formData.parking) || 0,
        balconies: parseInt(formData.balconies) || 0,
        ageOfProperty: parseInt(formData.ageOfProperty) || null,
      };

      const method = property ? 'PUT' : 'POST';
      const url = property ? `/api/admin/properties/${property.id}` : '/api/admin/properties';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(property ? 'Property updated successfully!' : 'Property created successfully!');
        router.push('/admin');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to save property');
      }
    } catch (error) {
      toast.error('An error occurred while saving the property');
    } finally {
      setLoading(false);
    }
  };

  const sections = [
    { id: 'basic', label: 'Basic Info', icon: Home },
    { id: 'location', label: 'Location', icon: MapPin },
    { id: 'details', label: 'Details', icon: Settings },
    { id: 'pricing', label: 'Pricing', icon: DollarSign },
    { id: 'images', label: 'Images', icon: Camera },
    { id: 'seo', label: 'SEO', icon: FileText },
  ];

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Section Navigation */}
      <div className="bg-white rounded-lg shadow-card p-6">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => (
            <button
              key={section.id}
              type="button"
              onClick={() => setActiveSection(section.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                activeSection === section.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <section.icon className="w-4 h-4 mr-2" />
              {section.label}
            </button>
          ))}
        </div>
      </div>

      {/* Basic Information */}
      {activeSection === 'basic' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Basic Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Property Title *
              </label>
              <Input
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., Luxury 3BHK Apartment in Gurgaon"
                required
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Detailed description of the property..."
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Property Type *
              </label>
              <select
                value={formData.propertyType}
                onChange={(e) => handleInputChange('propertyType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="apartment">Apartment</option>
                <option value="house">Independent House</option>
                <option value="villa">Villa</option>
                <option value="builderfloor">Builder Floor</option>
                <option value="plot">Plot</option>
                <option value="commercial">Commercial</option>
                <option value="office">Office Space</option>
                <option value="shop">Shop</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Sub Type
              </label>
              <Input
                value={formData.subType}
                onChange={(e) => handleInputChange('subType', e.target.value)}
                placeholder="e.g., 3BHK, Studio, Penthouse"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Listing Type *
              </label>
              <select
                value={formData.listingType}
                onChange={(e) => handleInputChange('listingType', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="sale">For Sale</option>
                <option value="rent">For Rent</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status *
              </label>
              <select
                value={formData.status}
                onChange={(e) => handleInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              >
                <option value="available">Available</option>
                <option value="under-negotiation">Under Negotiation</option>
                <option value="sold">Sold</option>
                <option value="rented">Rented</option>
              </select>
            </div>
          </div>

          <div className="mt-6 flex items-center space-x-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.featured}
                onChange={(e) => handleInputChange('featured', e.target.checked)}
                className="mr-2"
              />
              Featured Property
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.verified}
                onChange={(e) => handleInputChange('verified', e.target.checked)}
                className="mr-2"
              />
              Verified
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.published}
                onChange={(e) => handleInputChange('published', e.target.checked)}
                className="mr-2"
              />
              Published
            </label>
          </div>
        </motion.div>
      )}

      {/* Location Information */}
      {activeSection === 'location' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Location Details</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Area/Locality *
              </label>
              <Input
                value={formData.area}
                onChange={(e) => handleInputChange('area', e.target.value)}
                placeholder="e.g., Sector 15, Gurgaon"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                City *
              </label>
              <Input
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
                placeholder="e.g., Gurgaon"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                State *
              </label>
              <Input
                value={formData.state}
                onChange={(e) => handleInputChange('state', e.target.value)}
                placeholder="e.g., Haryana"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Pincode
              </label>
              <Input
                value={formData.pincode}
                onChange={(e) => handleInputChange('pincode', e.target.value)}
                placeholder="e.g., 122001"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Full Address *
              </label>
              <textarea
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Complete address of the property"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Latitude (Optional)
              </label>
              <Input
                value={formData.latitude}
                onChange={(e) => handleInputChange('latitude', e.target.value)}
                placeholder="28.4595"
                type="number"
                step="any"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Longitude (Optional)
              </label>
              <Input
                value={formData.longitude}
                onChange={(e) => handleInputChange('longitude', e.target.value)}
                placeholder="77.0266"
                type="number"
                step="any"
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Property Details */}
      {activeSection === 'details' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Property Details</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bedrooms
              </label>
              <Input
                value={formData.bedrooms}
                onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                placeholder="3"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bathrooms
              </label>
              <Input
                value={formData.bathrooms}
                onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                placeholder="2"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Balconies
              </label>
              <Input
                value={formData.balconies}
                onChange={(e) => handleInputChange('balconies', e.target.value)}
                placeholder="1"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Area (sq ft)
              </label>
              <Input
                value={formData.totalArea}
                onChange={(e) => handleInputChange('totalArea', e.target.value)}
                placeholder="1200"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Carpet Area (sq ft)
              </label>
              <Input
                value={formData.carpetArea}
                onChange={(e) => handleInputChange('carpetArea', e.target.value)}
                placeholder="1000"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Built-up Area (sq ft)
              </label>
              <Input
                value={formData.builtUpArea}
                onChange={(e) => handleInputChange('builtUpArea', e.target.value)}
                placeholder="1100"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Floor
              </label>
              <Input
                value={formData.floors}
                onChange={(e) => handleInputChange('floors', e.target.value)}
                placeholder="3"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total Floors
              </label>
              <Input
                value={formData.totalFloors}
                onChange={(e) => handleInputChange('totalFloors', e.target.value)}
                placeholder="15"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Parking
              </label>
              <Input
                value={formData.parking}
                onChange={(e) => handleInputChange('parking', e.target.value)}
                placeholder="1"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Furnished Status
              </label>
              <select
                value={formData.furnished}
                onChange={(e) => handleInputChange('furnished', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="unfurnished">Unfurnished</option>
                <option value="semi-furnished">Semi-Furnished</option>
                <option value="furnished">Fully Furnished</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Age of Property (years)
              </label>
              <Input
                value={formData.ageOfProperty}
                onChange={(e) => handleInputChange('ageOfProperty', e.target.value)}
                placeholder="2"
                type="number"
                min="0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Facing Direction
              </label>
              <select
                value={formData.facing}
                onChange={(e) => handleInputChange('facing', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Select Facing</option>
                <option value="north">North</option>
                <option value="south">South</option>
                <option value="east">East</option>
                <option value="west">West</option>
                <option value="north-east">North-East</option>
                <option value="north-west">North-West</option>
                <option value="south-east">South-East</option>
                <option value="south-west">South-West</option>
              </select>
            </div>
          </div>

          {/* Amenities */}
          <div className="mt-8">
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Amenities
            </label>
            <div className="flex flex-wrap gap-2 mb-4">
              {formData.amenities.map((amenity, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 text-primary-800"
                >
                  {amenity}
                  <button
                    type="button"
                    onClick={() => removeAmenity(index)}
                    className="ml-2 text-primary-600 hover:text-primary-800"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </span>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newAmenity}
                onChange={(e) => setNewAmenity(e.target.value)}
                placeholder="Add amenity (e.g., Swimming Pool, Gym)"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAmenity())}
              />
              <Button type="button" onClick={addAmenity} variant="outline">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Features */}
          <div className="mt-8">
            <label className="block text-sm font-medium text-gray-700 mb-4">
              Features
            </label>
            <div className="flex flex-wrap gap-2 mb-4">
              {formData.features.map((feature, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
                >
                  {feature}
                  <button
                    type="button"
                    onClick={() => removeFeature(index)}
                    className="ml-2 text-green-600 hover:text-green-800"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </span>
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                placeholder="Add feature (e.g., Modular Kitchen, CCTV)"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
              />
              <Button type="button" onClick={addFeature} variant="outline">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Pricing */}
      {activeSection === 'pricing' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Pricing Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price (₹) *
              </label>
              <Input
                value={formData.price}
                onChange={(e) => handleInputChange('price', e.target.value)}
                placeholder="5000000"
                type="number"
                min="0"
                required
              />
              <p className="text-sm text-gray-500 mt-1">
                {formData.listingType === 'rent' ? 'Monthly rent amount' : 'Total property price'}
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price per Sq Ft (₹)
              </label>
              <Input
                value={formData.pricePerSqft}
                onChange={(e) => handleInputChange('pricePerSqft', e.target.value)}
                placeholder="4000"
                type="number"
                min="0"
              />
              <p className="text-sm text-gray-500 mt-1">
                Will be calculated automatically if left blank
              </p>
            </div>
          </div>

          {/* Contact Information */}
          <div className="mt-8">
            <h4 className="text-md font-semibold text-gray-900 mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Name
                </label>
                <Input
                  value={formData.contactName}
                  onChange={(e) => handleInputChange('contactName', e.target.value)}
                  placeholder="Armaan Sharma"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Phone
                </label>
                <Input
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  placeholder="+91 98765 43210"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contact Email
                </label>
                <Input
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                  type="email"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  WhatsApp Number
                </label>
                <Input
                  value={formData.contactWhatsapp}
                  onChange={(e) => handleInputChange('contactWhatsapp', e.target.value)}
                  placeholder="+91 98765 43210"
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* Images */}
      {activeSection === 'images' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Property Images</h3>
          
          <div className="space-y-6">
            {/* Image Upload */}
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              <div className="text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <label htmlFor="images" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Upload property images
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, GIF up to 10MB each
                    </span>
                  </label>
                  <input
                    id="images"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>
                <Button
                  type="button"
                  variant="outline"
                  className="mt-4"
                  onClick={() => document.getElementById('images')?.click()}
                  disabled={loading}
                >
                  Choose Images
                </Button>
              </div>
            </div>

            {/* Image Preview */}
            {formData.images.length > 0 && (
              <div>
                <h4 className="text-md font-semibold text-gray-900 mb-4">
                  Uploaded Images ({formData.images.length})
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={image}
                        alt={`Property image ${index + 1}`}
                        className="w-full h-32 object-cover rounded-lg"
                      />
                      <button
                        type="button"
                        onClick={() => removeImage(index)}
                        className="absolute top-2 right-2 bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <X className="w-4 h-4" />
                      </button>
                      {index === 0 && (
                        <span className="absolute top-2 left-2 bg-primary-600 text-white text-xs px-2 py-1 rounded">
                          Main
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </motion.div>
      )}

      {/* SEO */}
      {activeSection === 'seo' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">SEO Settings</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meta Title
              </label>
              <Input
                value={formData.metaTitle}
                onChange={(e) => handleInputChange('metaTitle', e.target.value)}
                placeholder="Leave blank to auto-generate from property title"
              />
              <p className="text-sm text-gray-500 mt-1">
                {formData.metaTitle.length}/60 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meta Description
              </label>
              <textarea
                value={formData.metaDescription}
                onChange={(e) => handleInputChange('metaDescription', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Leave blank to auto-generate from property description"
              />
              <p className="text-sm text-gray-500 mt-1">
                {formData.metaDescription.length}/160 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meta Keywords
              </label>
              <Input
                value={formData.metaKeywords}
                onChange={(e) => handleInputChange('metaKeywords', e.target.value)}
                placeholder="property, real estate, apartment, gurgaon"
              />
              <p className="text-sm text-gray-500 mt-1">
                Comma-separated keywords for search engines
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Form Actions */}
      <div className="flex justify-between items-center bg-white rounded-lg shadow-card p-6">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.back()}
          disabled={loading}
        >
          Cancel
        </Button>
        
        <div className="flex space-x-4">
          <Button
            type="submit"
            disabled={loading}
            loading={loading}
          >
            <Save className="w-4 h-4 mr-2" />
            {property ? 'Update Property' : 'Create Property'}
          </Button>
        </div>
      </div>
    </form>
  );
}