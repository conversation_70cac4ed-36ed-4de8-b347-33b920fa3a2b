# Server Commands Guide

## Admin User Management

### Give Admin Access to a User
To give admin access to an existing user, use:
```bash
npm run set-admin <email>
```

**Example:**
```bash
npm run set-admin <EMAIL>
```

This command will:
- Find the user by email in the database
- Update their role to 'admin'
- Show confirmation with user details

## Development Commands

### Start Development Server
```bash
npm run dev
```
- Runs on http://localhost:3003
- Hot reload enabled
- Includes TypeScript checking

### Build for Production
```bash
npm run build
```
- Creates optimized production build
- Runs TypeScript checks
- Generates static files

### Start Production Server
```bash
npm start
```
- Serves the production build
- Use after running `npm run build`

## Database Commands

### Seed Database with Sample Data
```bash
npm run seed
```
- Adds 10+ realistic property listings
- Creates sample images and amenities
- Useful for testing and development

### Database Migration (if needed)
```bash
npx drizzle-kit push:pg
```
- Applies schema changes to database
- Use when updating database structure

## Production Deployment Commands

### VPS Deployment Preparation
```bash
npm run deploy:build
```
- Installs production dependencies only
- Creates optimized build

### PM2 Process Management (for VPS)
```bash
# Start application with PM2
pm2 start ecosystem.config.js

# View application status
pm2 status

# View logs
pm2 logs property-trendz

# Restart application
pm2 restart property-trendz

# Stop application
pm2 stop property-trendz

# Delete application from PM2
pm2 delete property-trendz
```

## Docker Commands (if using Docker)

### Build Docker Image
```bash
docker build -t property-trendz .
```

### Run Docker Container
```bash
docker run -p 3000:3000 --env-file .env.local property-trendz
```

### Docker Compose
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down
```

## Database Maintenance

### Test Database Connection
```bash
node lib/db/test-connection.mjs
```

### Check Admin Users
```bash
# This will show if any admin users exist
node lib/db/test-connection.mjs
```

## Utility Commands

### TypeScript Check
```bash
npm run type-check
```
- Runs TypeScript compiler check only
- No build output

### Lint Code
```bash
npm run lint
```
- Runs ESLint on codebase
- Shows code style issues

### Clear Next.js Cache
```bash
rm -rf .next
npm run dev
```
- Clears build cache
- Useful for resolving build issues

## Environment Setup

### Environment-Based Database Configuration

The application now uses **centralized database configuration** that automatically routes to the correct database based on your environment:

#### Development (.env.local)
```env
NODE_ENV="development"
DATABASE_URL="postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties"
NEXTAUTH_SECRET="your-secret-key"
NEXTAUTH_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_NAME="Property Trendz"
NEXT_PUBLIC_CONTACT_PHONE="+91 9810129777"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
```

#### Production (.env.production)
```env
NODE_ENV="production"
DATABASE_URL="postgresql://yash123414:<EMAIL>:5432/armaan_properties"
NEXTAUTH_URL="https://property-trendz.com"
NEXTAUTH_SECRET="ucntcomptmeimgr8strivl"
COOKIE_DOMAIN="property-trendz.com"
```

### Database Configuration Validation
```bash
# Validate current database configuration
npm run db:validate

# Test database connection
npm run db:test
```

## Troubleshooting Commands

### If Database Connection Fails

1. **Validate configuration first:**
   ```bash
   npm run db:validate
   ```

2. **For Development (Docker):**
   ```bash
   # Check if Docker containers are running
   docker ps | grep postgres

   # Start PostgreSQL container
   docker-compose up -d postgres

   # Check container logs
   docker logs armaan_properties_db
   ```

3. **For Production:**
   ```bash
   # Test network connectivity
   ping srv949995.hstar.cloud

   # Test database connection
   npm run db:test
   ```

4. **General troubleshooting:**
   ```bash
   # Test connection with detailed output
   node lib/db/test-connection.mjs

   # Check environment variables
   echo $NODE_ENV
   echo $DATABASE_URL
   ```

### If Build Fails
1. Clear cache and reinstall:
   ```bash
   rm -rf node_modules package-lock.json .next
   npm install
   npm run build
   ```

### If Port is Already in Use
```bash
# Kill process on port 3003
npx kill-port 3003

# Or find and kill manually
netstat -ano | findstr :3003
taskkill /PID <PID> /F
```

## Backup Commands

### Database Backup
```bash
pg_dump -h localhost -U username -d database_name > backup.sql
```

### Restore Database
```bash
psql -h localhost -U username -d database_name < backup.sql
```

## Monitoring

### Check Application Health
```bash
curl http://localhost:3003/api/health
```

### View Application Logs
```bash
# Development
npm run dev

# Production (with PM2)
pm2 logs property-trendz --lines 100
```

---

## Quick Start Checklist

1. **Setup Database**: Ensure PostgreSQL is running
2. **Install Dependencies**: `npm install`
3. **Setup Environment**: Copy `.env.local` with correct values
4. **Create Admin User**: Register normally, then run `npm run set-admin <email>`
5. **Seed Data**: `npm run seed` (optional)
6. **Start Development**: `npm run dev`
7. **Access Admin Panel**: Visit `/admin` after logging in as admin

---

**Note**: Replace `<email>` with the actual email address when running commands.