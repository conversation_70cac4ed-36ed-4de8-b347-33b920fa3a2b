// Production Environment Configuration Template
// This file contains production-ready environment variable patterns
// DO NOT use these exact values - generate your own secure credentials

const envConfig = {
  // Core Application
  NODE_ENV: 'production',

  // PostgreSQL Database - Use your production database URL
  DATABASE_URL: process.env.DATABASE_URL || 'postgresql://username:password@host:port/database',

  // NextAuth.js Configuration
  NEXTAUTH_URL: process.env.NEXTAUTH_URL || 'https://yourdomain.com',
  NEXTAUTH_SECRET: process.env.NEXTAUTH_SECRET || 'GENERATE_A_SECURE_32_CHAR_SECRET_KEY',
  COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || 'yourdomain.com',

  // OAuth Providers (Optional - configure only if using OAuth)
  GOOGLE_CLIENT_ID: process.env.GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.GOOGLE_CLIENT_SECRET || '',
  FACEBOOK_CLIENT_ID: process.env.FACEBOOK_CLIENT_ID || '',
  FACEBOOK_CLIENT_SECRET: process.env.FACEBOOK_CLIENT_SECRET || '',

  // Cloudinary (Optional - for advanced image management)
  NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || '',
  CLOUDINARY_API_KEY: process.env.CLOUDINARY_API_KEY || '',
  CLOUDINARY_API_SECRET: process.env.CLOUDINARY_API_SECRET || '',

  // Email Configuration (Required for contact forms)
  SMTP_HOST: process.env.SMTP_HOST || 'smtp.gmail.com',
  SMTP_PORT: process.env.SMTP_PORT || '587',
  SMTP_SECURE: process.env.SMTP_SECURE || 'false',
  SMTP_USER: process.env.SMTP_USER || '<EMAIL>',
  SMTP_PASSWORD: process.env.SMTP_PASSWORD || 'your-app-specific-password',

  // Site Configuration
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://yourdomain.com',
  NEXT_PUBLIC_SITE_NAME: process.env.NEXT_PUBLIC_SITE_NAME || 'Property Trendz',
  NEXT_PUBLIC_CONTACT_EMAIL: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
  NEXT_PUBLIC_CONTACT_PHONE: process.env.NEXT_PUBLIC_CONTACT_PHONE || '+1234567890',
  NEXT_PUBLIC_WHATSAPP_NUMBER: process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '1234567890',

  // Security (Required for production)
  CSRF_SECRET: process.env.CSRF_SECRET || 'GENERATE_A_SECURE_64_CHAR_CSRF_SECRET_KEY',
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY || 'GENERATE_A_SECURE_32_CHAR_ENCRYPTION_KEY',
  JWT_SECRET: process.env.JWT_SECRET || 'GENERATE_A_SECURE_JWT_SECRET_KEY',

  // File Upload Configuration
  UPLOAD_DIR: process.env.UPLOAD_DIR || './public/uploads',
  MAX_FILE_SIZE: process.env.MAX_FILE_SIZE || '10485760', // 10MB

  // Rate Limiting (Optional - Upstash Redis for production)
  UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL || '',
  UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN || '',

  // Analytics (Optional)
  NEXT_PUBLIC_GA_ID: process.env.NEXT_PUBLIC_GA_ID || '',
  NEXT_PUBLIC_GTM_ID: process.env.NEXT_PUBLIC_GTM_ID || '',

  // SEO Verification (Optional)
  NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION || '',
  NEXT_PUBLIC_BING_SITE_VERIFICATION: process.env.NEXT_PUBLIC_BING_SITE_VERIFICATION || '',
};

module.exports = envConfig; 
