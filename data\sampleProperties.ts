import { Property } from '@/types/property'

// PRODUCTION NOTE: This file contains sample data for development and testing purposes.
// In production, properties should be loaded from the database using the API endpoints.
// This sample data can be used for:
// 1. Initial database seeding (see scripts/seed-db.js)
// 2. Development and testing
// 3. Demo purposes

// For production use, replace this with database queries:
// - Use /api/properties endpoints to fetch real property data
// - Implement proper data management through the admin panel
// - Remove or comment out this sample data

// PRODUCTION: Sample data removed - all properties now loaded from database
export const sampleProperties: Partial<Property>[] = []

// Production helper functions for data management
export const getProductionProperties = async () => {
  // In production, fetch properties from the database via API
  try {
    const response = await fetch('/api/properties');
    if (!response.ok) {
      throw new Error('Failed to fetch properties');
    }
    return await response.json();
  } catch (error) {
    console.error('Error fetching properties:', error);
    return [];
  }
};

// Helper function to check if we're using sample data (for development)
export const isUsingSampleData = () => {
  return process.env.NODE_ENV === 'development' || process.env.USE_SAMPLE_DATA === 'true';
};

// PRODUCTION: Always return empty array - properties loaded from database via API
export const getProperties = () => {
  return []; // All properties loaded from database via /api/properties endpoint
};