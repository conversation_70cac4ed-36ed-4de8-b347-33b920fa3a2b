import { notFound } from 'next/navigation';
import { Metadata } from 'next';
import { PropertyDetail } from '@/components/property/PropertyDetail';

interface PropertyPageProps {
  params: { slug: string };
}

// Function to get property by slug
async function getProperty(slug: string) {
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/api/properties/${slug}`, {
      cache: 'no-store', // Always fetch fresh data
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data.property;
  } catch (error) {
    console.error('Error fetching property:', error);
    return null;
  }
}

export async function generateMetadata({ params }: PropertyPageProps): Promise<Metadata> {
  const property = await getProperty(params.slug);

  if (!property) {
    return {
      title: 'Property Not Found',
      description: 'The property you are looking for could not be found.',
    };
  }

  const title = property.metaTitle || property.title;
  const description = property.metaDescription || property.description.substring(0, 160);
  const images = property.images?.length > 0 ? [property.images[0]] : [];

  return {
    title: `${title} | Property Trendz`,
    description,
    keywords: property.metaKeywords || `${property.propertyType}, ${property.area}, ${property.city}, real estate`,
    openGraph: {
      title,
      description,
      type: 'website',
      images,
      siteName: 'Property Trendz',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images,
    },
  };
}

export default async function PropertyPage({ params }: PropertyPageProps) {
  const property = await getProperty(params.slug);

  if (!property) {
    notFound();
  }

  return <PropertyDetail property={property} />;
}