'use client';

import React, { useState } from 'react';
import {
  Phone,
  Mail,
  MapPin,
  Send,
  Clock,
  MessageSquare,
  ExternalLink,
  Building,
  Users,
  Calendar,
  DollarSign,
  Home,
  TrendingUp
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { motion } from 'framer-motion';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
    reasonForContact: '',
    budget: '',
    timeline: '',
    propertyType: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.message || 'Failed to submit form')
      }

      toast.success(data.message || 'Thank you! We will contact you soon.')
      setFormData({
        name: '',
        email: '',
        phone: '',
        message: '',
        reasonForContact: '',
        budget: '',
        timeline: '',
        propertyType: ''
      })
    } catch (error: any) {
      console.error('Contact form error:', error)
      toast.error(error.message || 'Something went wrong. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  // Contact information
  // TODO: Update this address to actual office location before production deployment
  const contactInfo = {
    phone: '+91 98765 43210',
    email: '<EMAIL>',
    whatsapp: '+919876543210',
    address: 'Green Park, New Delhi, Delhi 110016', // Default location - update to actual office address
    officeHours: {
      weekdays: 'Monday - Friday: 9:00 AM - 7:00 PM',
      saturday: 'Saturday: 10:00 AM - 5:00 PM',
      sunday: 'Sunday: Closed'
    }
  }

  // Animation variants
  const fadeInUp = {
    initial: { opacity: 0, y: 60 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6 }
  }

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 pt-16">
      {/* Hero Section */}
      <motion.section
        className="relative py-12 md:py-16 bg-gradient-to-r from-primary-600 to-primary-800 text-white overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 text-center">
          <motion.h1
            className="text-4xl md:text-5xl font-bold mb-4"
            {...fadeInUp}
          >
            Get In Touch
          </motion.h1>
          <motion.p
            className="text-lg md:text-xl text-primary-100 max-w-2xl mx-auto"
            {...fadeInUp}
            transition={{ delay: 0.2 }}
          >
            Ready to find your dream property? Our expert team is here to guide you through every step of your real estate journey.
          </motion.p>
        </div>
      </motion.section>

      <div className="container mx-auto px-4 py-12">
        {/* Quick Contact Methods */}
        <motion.section
          className="mb-12"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          <motion.h2
            className="text-2xl md:text-3xl font-bold text-center text-neutral-900 mb-8"
            variants={fadeInUp}
          >
            Connect With Us Instantly
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6 max-w-4xl mx-auto">
            {/* Phone */}
            <motion.a
              href={`tel:${contactInfo.phone}`}
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-neutral-200 hover:border-primary-300 text-center"
              variants={fadeInUp}
              whileHover={{ y: -5 }}
            >
              <div className="w-14 h-14 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-primary-200 transition-colors">
                <Phone className="w-7 h-7 text-primary-600" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-2">Call Us Now</h3>
              <p className="text-primary-600 font-medium">{contactInfo.phone}</p>
              <p className="text-neutral-500 text-sm mt-1">Instant support available</p>
            </motion.a>

            {/* WhatsApp */}
            <motion.a
              href={`https://wa.me/${contactInfo.whatsapp.replace(/[^0-9]/g, '')}?text=Hi! I'm interested in your property services.`}
              target="_blank"
              rel="noopener noreferrer"
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-neutral-200 hover:border-green-300 text-center"
              variants={fadeInUp}
              whileHover={{ y: -5 }}
            >
              <div className="w-14 h-14 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-green-200 transition-colors">
                <MessageSquare className="w-7 h-7 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-2">WhatsApp</h3>
              <p className="text-green-600 font-medium">{contactInfo.whatsapp}</p>
              <p className="text-neutral-500 text-sm mt-1">Quick chat support</p>
            </motion.a>

            {/* Email */}
            <motion.a
              href={`mailto:${contactInfo.email}?subject=Property Inquiry&body=Hi! I'm interested in your property services.`}
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-neutral-200 hover:border-blue-300 text-center"
              variants={fadeInUp}
              whileHover={{ y: -5 }}
            >
              <div className="w-14 h-14 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-blue-200 transition-colors">
                <Mail className="w-7 h-7 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-neutral-900 mb-2">Email Us</h3>
              <p className="text-blue-600 font-medium">{contactInfo.email}</p>
              <p className="text-neutral-500 text-sm mt-1">Detailed inquiries welcome</p>
            </motion.a>
          </div>
        </motion.section>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {/* Contact Form - Takes 2 columns */}
          <motion.div
            className="xl:col-span-2"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
          >
            <div className="bg-white rounded-2xl shadow-xl p-6 md:p-8 border border-neutral-200">
              <div className="mb-6">
                <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 mb-3">Send Us a Message</h2>
                <p className="text-neutral-600">Fill out the form below and we'll get back to you within 24 hours.</p>
              </div>

              <form onSubmit={handleSubmit} className="space-y-5">
                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-semibold text-neutral-700 mb-3">
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-semibold text-neutral-700 mb-3">
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-semibold text-neutral-700 mb-3">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                    placeholder="+91 98765 43210"
                  />
                </div>

                {/* Reason for Contact */}
                <div>
                  <label htmlFor="reasonForContact" className="block text-sm font-semibold text-neutral-700 mb-3">
                    Reason for Contact *
                  </label>
                  <select
                    id="reasonForContact"
                    name="reasonForContact"
                    value={formData.reasonForContact}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                  >
                    <option value="">Select your reason for contacting us</option>
                    <option value="buying">Buying a Property</option>
                    <option value="selling">Selling a Property</option>
                    <option value="investing">Investment Opportunities</option>
                    <option value="rental">Rental Inquiry</option>
                    <option value="valuation">Property Valuation</option>
                    <option value="consultation">Free Consultation</option>
                    <option value="general">General Inquiry</option>
                  </select>
                </div>

                {/* Property Details */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="propertyType" className="block text-sm font-semibold text-neutral-700 mb-3">
                      Property Type
                    </label>
                    <select
                      id="propertyType"
                      name="propertyType"
                      value={formData.propertyType}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                    >
                      <option value="">Select property type</option>
                      <option value="apartment">Apartment</option>
                      <option value="house">Independent House</option>
                      <option value="villa">Villa</option>
                      <option value="plot">Plot/Land</option>
                      <option value="commercial">Commercial Property</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="budget" className="block text-sm font-semibold text-neutral-700 mb-3">
                      Budget Range
                    </label>
                    <select
                      id="budget"
                      name="budget"
                      value={formData.budget}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                    >
                      <option value="">Select budget range</option>
                      <option value="under-25L">Under ₹25 Lakhs</option>
                      <option value="25L-50L">₹25 - 50 Lakhs</option>
                      <option value="50L-1Cr">₹50 Lakhs - 1 Crore</option>
                      <option value="1Cr-2Cr">₹1 - 2 Crores</option>
                      <option value="2Cr-5Cr">₹2 - 5 Crores</option>
                      <option value="above-5Cr">Above ₹5 Crores</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="timeline" className="block text-sm font-semibold text-neutral-700 mb-3">
                    Timeline
                  </label>
                  <select
                    id="timeline"
                    name="timeline"
                    value={formData.timeline}
                    onChange={handleChange}
                    className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white"
                  >
                    <option value="">When are you looking to proceed?</option>
                    <option value="immediately">Immediately</option>
                    <option value="1-3months">Within 1-3 months</option>
                    <option value="3-6months">Within 3-6 months</option>
                    <option value="6-12months">Within 6-12 months</option>
                    <option value="exploring">Just exploring options</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-semibold text-neutral-700 mb-3">
                    Additional Details *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    required
                    rows={5}
                    className="w-full px-4 py-4 border border-neutral-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200 bg-neutral-50 focus:bg-white resize-none"
                    placeholder="Tell us more about your requirements, preferred location, specific features you're looking for, or any questions you have..."
                  />
                </div>

                <motion.button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-primary-700 hover:to-primary-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-3 transition-all duration-300 shadow-lg hover:shadow-xl"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Sending Message...</span>
                    </>
                  ) : (
                    <>
                      <Send className="w-5 h-5" />
                      <span>Send Message</span>
                    </>
                  )}
                </motion.button>
              </form>
            </div>
          </motion.div>

          {/* Office Information & Map */}
          <motion.div
            className="xl:col-span-1 space-y-6"
            variants={fadeInUp}
            initial="initial"
            animate="animate"
            transition={{ delay: 0.2 }}
          >
            {/* Office Information */}
            <div className="bg-white rounded-2xl shadow-xl p-6 border border-neutral-200">
              <div className="flex items-center mb-5">
                <Building className="w-7 h-7 text-primary-600 mr-3" />
                <h3 className="text-xl font-bold text-neutral-900">Our Office</h3>
              </div>

              <div className="space-y-5">
                <div className="flex items-start space-x-4">
                  <MapPin className="w-6 h-6 text-primary-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900 mb-1">Address</h4>
                    <p className="text-neutral-600 leading-relaxed">{contactInfo.address}</p>
                    <a
                      href={`https://maps.google.com/?q=${encodeURIComponent(contactInfo.address)}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center text-primary-600 hover:text-primary-700 mt-2 text-sm font-medium"
                    >
                      Get Directions <ExternalLink className="w-4 h-4 ml-1" />
                    </a>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <Clock className="w-6 h-6 text-primary-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900 mb-2">Office Hours</h4>
                    <div className="space-y-1 text-neutral-600">
                      <p>{contactInfo.officeHours.weekdays}</p>
                      <p>{contactInfo.officeHours.saturday}</p>
                      <p className="text-red-600">{contactInfo.officeHours.sunday}</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <Users className="w-6 h-6 text-primary-600 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-neutral-900 mb-1">Our Team</h4>
                    <p className="text-neutral-600">Expert real estate professionals ready to help you find your perfect property.</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Map */}
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-neutral-200">
              <div className="p-6 border-b border-neutral-200">
                <h3 className="text-xl font-bold text-neutral-900 flex items-center">
                  <MapPin className="w-6 h-6 text-primary-600 mr-2" />
                  Find Us Here
                </h3>
              </div>
              <div className="relative h-64 bg-neutral-100">
                {/* TODO: Update this map embed to show actual office location before production deployment */}
                <iframe
                  src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3504.8267739788!2d77.20688!3d28.5562!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x390ce26c3f6b5c0f%3A0x7e5e5e5e5e5e5e5e!2sGreen%20Park%2C%20New%20Delhi%2C%20Delhi!5e0!3m2!1sen!2sin!4v1692345678901!5m2!1sen!2sin"
                  width="100%"
                  height="100%"
                  style={{ border: 0 }}
                  allowFullScreen
                  loading="lazy"
                  referrerPolicy="no-referrer-when-downgrade"
                  className="absolute inset-0"
                  title="Office Location - Green Park, New Delhi"
                ></iframe>
              </div>
              <div className="p-4 bg-neutral-50">
                <a
                  href={`https://maps.google.com/?q=${encodeURIComponent(contactInfo.address)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium text-sm"
                >
                  Open in Google Maps <ExternalLink className="w-4 h-4 ml-1" />
                </a>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl p-6 border border-primary-200">
              <h3 className="text-lg font-bold text-neutral-900 mb-4 text-center">Why Choose Us?</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Home className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-xl font-bold text-primary-700">500+</div>
                  <div className="text-xs text-neutral-600">Properties Sold</div>
                </div>
                <div className="text-center">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-xl font-bold text-primary-700">1000+</div>
                  <div className="text-xs text-neutral-600">Happy Clients</div>
                </div>
                <div className="text-center">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <TrendingUp className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-xl font-bold text-primary-700">15+</div>
                  <div className="text-xs text-neutral-600">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-2">
                    <DollarSign className="w-5 h-5 text-white" />
                  </div>
                  <div className="text-xl font-bold text-primary-700">₹500Cr+</div>
                  <div className="text-xs text-neutral-600">Worth Transacted</div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* FAQ Section */}
        <motion.section
          className="mt-16 max-w-4xl mx-auto"
          variants={fadeInUp}
          initial="initial"
          animate="animate"
          transition={{ delay: 0.4 }}
        >
          <div className="text-center mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-neutral-900 mb-3">Frequently Asked Questions</h2>
            <p className="text-neutral-600">Quick answers to common questions about our services</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-5 shadow-lg border border-neutral-200">
              <h3 className="font-semibold text-neutral-900 mb-2">How quickly can you help me find a property?</h3>
              <p className="text-neutral-600 text-sm">We typically have suitable options ready within 24-48 hours based on your requirements. Our extensive database and network help us match you quickly.</p>
            </div>

            <div className="bg-white rounded-xl p-5 shadow-lg border border-neutral-200">
              <h3 className="font-semibold text-neutral-900 mb-2">Do you charge any consultation fees?</h3>
              <p className="text-neutral-600 text-sm">Initial consultation is completely free. We only charge a commission upon successful transaction completion, ensuring our interests align with yours.</p>
            </div>

            <div className="bg-white rounded-xl p-5 shadow-lg border border-neutral-200">
              <h3 className="font-semibold text-neutral-900 mb-2">Can you help with property documentation?</h3>
              <p className="text-neutral-600 text-sm">Yes, we provide complete assistance with legal documentation, verification, and registration processes through our trusted legal partners.</p>
            </div>

            <div className="bg-white rounded-xl p-5 shadow-lg border border-neutral-200">
              <h3 className="font-semibold text-neutral-900 mb-2">Do you offer property management services?</h3>
              <p className="text-neutral-600 text-sm">We offer comprehensive property management including tenant finding, rent collection, maintenance coordination, and investment advisory services.</p>
            </div>
          </div>
        </motion.section>
      </div>
    </div>
  )
}
