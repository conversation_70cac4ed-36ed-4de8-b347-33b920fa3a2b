'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import { 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Maximize, 
  Car, 
  Calendar,
  Eye,
  Heart,
  Share2,
  Phone,
  MessageCircle,
  Mail,
  ChevronLeft,
  ChevronRight,
  X,
  Check,
  Building,
  Home,
  Trees,
  Shield,
  Zap,
  Wifi,
  Car as CarIcon
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FavoriteButton } from '@/components/ui/FavoriteButton';
import { CompareButton } from '@/components/ui/CompareButton';
import { toast } from 'react-hot-toast';
import Link from 'next/link';
import { PropertyStructuredData } from '@/components/seo/StructuredData';

interface PropertyDetailProps {
  property: any;
}

const amenityIcons: { [key: string]: any } = {
  'Swimming Pool': Trees,
  'Gym': Building,
  'Parking': CarIcon,
  'Security': Shield,
  'Power Backup': Zap,
  'Lift': Building,
  'Garden': Trees,
  'Clubhouse': Home,
  'Children Play Area': Trees,
  'CCTV': Shield,
  'Intercom': Phone,
  'Fire Safety': Shield,
  'WiFi': Wifi,
};

export function PropertyDetail({ property }: PropertyDetailProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageModal, setShowImageModal] = useState(false);
  const [showEnquiryForm, setShowEnquiryForm] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [enquiryForm, setEnquiryForm] = useState({
    name: '',
    email: '',
    phone: '',
    message: '',
  });
  const [submitting, setSubmitting] = useState(false);

  const formatPrice = (price: number, listingType: string) => {
    if (listingType === 'rent') {
      return `₹${price.toLocaleString('en-IN')}/month`;
    }
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`;
    }
    if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} L`;
    }
    return `₹${price.toLocaleString('en-IN')}`;
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => 
      prev === property.images.length - 1 ? 0 : prev + 1
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => 
      prev === 0 ? property.images.length - 1 : prev - 1
    );
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: property.title,
          text: property.description,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to clipboard
        await navigator.clipboard.writeText(window.location.href);
        toast.success('Link copied to clipboard!');
      }
    } else {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard!');
    }
  };

  const handleEnquiry = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const response = await fetch('/api/enquiries', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...enquiryForm,
          propertyId: property.id,
          enquiryType: 'property_interest',
        }),
      });

      if (response.ok) {
        toast.success('Enquiry sent successfully!');
        setShowEnquiryForm(false);
        setEnquiryForm({ name: '', email: '', phone: '', message: '' });
      } else {
        toast.error('Failed to send enquiry. Please try again.');
      }
    } catch (error) {
      toast.error('Something went wrong. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <>
      <PropertyStructuredData property={property} />
      <div className="min-h-screen bg-gray-50 pt-16">
      {/* Image Gallery */}
      <div className="relative">
        {property.images && property.images.length > 0 ? (
          <div className="relative h-96 md:h-[500px]">
            <Image
              src={property.images[currentImageIndex]}
              alt={property.title}
              fill
              className="object-cover"
              priority
            />
            
            {/* Image Navigation */}
            {property.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronLeft className="w-6 h-6" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors"
                >
                  <ChevronRight className="w-6 h-6" />
                </button>
              </>
            )}

            {/* Image Indicators */}
            {property.images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                {property.images.map((_: any, index: number) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-colors ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            )}

            {/* View All Photos Button */}
            <button
              onClick={() => setShowImageModal(true)}
              className="absolute bottom-4 right-4 bg-black/50 text-white px-4 py-2 rounded-lg hover:bg-black/70 transition-colors"
            >
              View All Photos ({property.images.length})
            </button>

            {/* Property Status */}
            <div className="absolute top-4 left-4 flex gap-2">
              {property.featured && (
                <span className="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                  Featured
                </span>
              )}
              <span className={`px-3 py-1 rounded-full text-sm font-semibold text-white ${
                property.status === 'available' ? 'bg-green-500' :
                property.status === 'sold' ? 'bg-red-500' :
                property.status === 'rented' ? 'bg-blue-500' :
                'bg-yellow-500'
              }`}>
                {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
              </span>
              <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-semibold capitalize">
                For {property.listingType}
              </span>
            </div>

            {/* Action Buttons */}
            <div className="absolute top-4 right-4 flex gap-2">
              <FavoriteButton propertyId={property.id} size="lg" />
              <CompareButton property={property} size="lg" />
              <button
                onClick={handleShare}
                className="p-3 bg-white/80 text-gray-700 rounded-full hover:bg-white transition-colors"
              >
                <Share2 className="w-6 h-6" />
              </button>
            </div>
          </div>
        ) : (
          <div className="h-96 md:h-[500px] bg-gray-200 flex items-center justify-center">
            <span className="text-gray-500">No images available</span>
          </div>
        )}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Property Header */}
            <div className="bg-white rounded-lg shadow-card p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900 mb-2">
                    {property.title}
                  </h1>
                  <div className="flex items-center text-gray-600">
                    <MapPin className="w-5 h-5 mr-2" />
                    <span>{property.address}</span>
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    {property.area}, {property.city}, {property.state} {property.pincode}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-3xl font-bold text-primary-600">
                    {formatPrice(property.price, property.listingType)}
                  </div>
                  {property.pricePerSqft && (
                    <div className="text-gray-500">
                      ₹{property.pricePerSqft.toLocaleString()}/sq ft
                    </div>
                  )}
                </div>
              </div>

              {/* Property Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
                {property.bedrooms && (
                  <div className="flex items-center">
                    <Bed className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="font-semibold">{property.bedrooms}</div>
                      <div className="text-sm text-gray-500">Bedrooms</div>
                    </div>
                  </div>
                )}
                {property.bathrooms && (
                  <div className="flex items-center">
                    <Bath className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="font-semibold">{property.bathrooms}</div>
                      <div className="text-sm text-gray-500">Bathrooms</div>
                    </div>
                  </div>
                )}
                {property.totalArea && (
                  <div className="flex items-center">
                    <Square className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="font-semibold">{property.totalArea}</div>
                      <div className="text-sm text-gray-500">Sq Ft</div>
                    </div>
                  </div>
                )}
                {property.parking > 0 && (
                  <div className="flex items-center">
                    <Car className="w-5 h-5 text-gray-400 mr-2" />
                    <div>
                      <div className="font-semibold">{property.parking}</div>
                      <div className="text-sm text-gray-500">Parking</div>
                    </div>
                  </div>
                )}
              </div>

              {/* View Count */}
              <div className="flex items-center text-gray-500 text-sm mt-4 pt-4 border-t">
                <Eye className="w-4 h-4 mr-1" />
                <span>{property.views || 0} views</span>
                <span className="mx-2">•</span>
                <Calendar className="w-4 h-4 mr-1" />
                <span>Listed {new Date(property.createdAt).toLocaleDateString()}</span>
              </div>
            </div>

            {/* Description */}
            <div className="bg-white rounded-lg shadow-card p-6">
              <h2 className="text-xl font-semibold mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed whitespace-pre-line">
                {property.description}
              </p>
            </div>

            {/* Property Details */}
            <div className="bg-white rounded-lg shadow-card p-6">
              <h2 className="text-xl font-semibold mb-4">Property Details</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <span className="text-gray-500">Property Type:</span>
                  <span className="ml-2 font-medium capitalize">{property.propertyType}</span>
                </div>
                {property.subType && (
                  <div>
                    <span className="text-gray-500">Sub Type:</span>
                    <span className="ml-2 font-medium">{property.subType}</span>
                  </div>
                )}
                <div>
                  <span className="text-gray-500">Listing Type:</span>
                  <span className="ml-2 font-medium capitalize">For {property.listingType}</span>
                </div>
                {property.furnished && (
                  <div>
                    <span className="text-gray-500">Furnished:</span>
                    <span className="ml-2 font-medium capitalize">{property.furnished}</span>
                  </div>
                )}
                {property.floors && (
                  <div>
                    <span className="text-gray-500">Floor:</span>
                    <span className="ml-2 font-medium">
                      {property.floors}{property.totalFloors && ` of ${property.totalFloors}`}
                    </span>
                  </div>
                )}
                {property.facing && (
                  <div>
                    <span className="text-gray-500">Facing:</span>
                    <span className="ml-2 font-medium capitalize">{property.facing}</span>
                  </div>
                )}
                {property.ageOfProperty !== null && property.ageOfProperty !== undefined && (
                  <div>
                    <span className="text-gray-500">Age:</span>
                    <span className="ml-2 font-medium">
                      {property.ageOfProperty === 0 ? 'Under Construction' : `${property.ageOfProperty} years`}
                    </span>
                  </div>
                )}
                {property.balconies > 0 && (
                  <div>
                    <span className="text-gray-500">Balconies:</span>
                    <span className="ml-2 font-medium">{property.balconies}</span>
                  </div>
                )}
              </div>

              {/* Area Details */}
              {(property.carpetArea || property.builtUpArea) && (
                <div className="mt-6 pt-6 border-t">
                  <h3 className="font-semibold mb-3">Area Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {property.totalArea && (
                      <div>
                        <span className="text-gray-500">Total Area:</span>
                        <span className="ml-2 font-medium">{property.totalArea} sq ft</span>
                      </div>
                    )}
                    {property.carpetArea && (
                      <div>
                        <span className="text-gray-500">Carpet Area:</span>
                        <span className="ml-2 font-medium">{property.carpetArea} sq ft</span>
                      </div>
                    )}
                    {property.builtUpArea && (
                      <div>
                        <span className="text-gray-500">Built-up Area:</span>
                        <span className="ml-2 font-medium">{property.builtUpArea} sq ft</span>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Amenities */}
            {property.amenities && property.amenities.length > 0 && (
              <div className="bg-white rounded-lg shadow-card p-6">
                <h2 className="text-xl font-semibold mb-4">Amenities</h2>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {property.amenities.map((amenity: string, index: number) => {
                    const IconComponent = amenityIcons[amenity] || Check;
                    return (
                      <div key={index} className="flex items-center">
                        <IconComponent className="w-5 h-5 text-green-500 mr-2" />
                        <span className="text-gray-700">{amenity}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Features */}
            {property.features && property.features.length > 0 && (
              <div className="bg-white rounded-lg shadow-card p-6">
                <h2 className="text-xl font-semibold mb-4">Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {property.features.map((feature: string, index: number) => (
                    <div key={index} className="flex items-center">
                      <Check className="w-5 h-5 text-green-500 mr-2" />
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Contact Card */}
            <div className="bg-white rounded-lg shadow-card p-6 sticky top-24">
              <h3 className="text-lg font-semibold mb-4">Contact Agent</h3>
              
              <div className="space-y-4">
                <div>
                  <div className="font-medium">{property.contactName || 'Armaan Sharma'}</div>
                  <div className="text-gray-500 text-sm">Property Agent</div>
                </div>

                <div className="space-y-3">
                  <a
                    href={`tel:${property.contactPhone}`}
                    className="w-full bg-primary-600 text-white py-3 px-4 rounded-lg hover:bg-primary-700 transition-colors flex items-center justify-center"
                  >
                    <Phone className="w-5 h-5 mr-2" />
                    Call Now
                  </a>

                  <a
                    href={`https://wa.me/${property.contactWhatsapp || property.contactPhone?.replace(/\D/g, '')}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center"
                  >
                    <MessageCircle className="w-5 h-5 mr-2" />
                    WhatsApp
                  </a>

                  <button
                    onClick={() => setShowEnquiryForm(true)}
                    className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors flex items-center justify-center"
                  >
                    <Mail className="w-5 h-5 mr-2" />
                    Send Enquiry
                  </button>
                </div>

                <div className="text-xs text-gray-500 mt-4">
                  <div>Phone: {property.contactPhone}</div>
                  {property.contactEmail && (
                    <div>Email: {property.contactEmail}</div>
                  )}
                </div>
              </div>
            </div>

            {/* Property ID */}
            <div className="bg-white rounded-lg shadow-card p-6">
              <h3 className="text-lg font-semibold mb-2">Property ID</h3>
              <div className="text-gray-600 font-mono">{property.id}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Enquiry Modal */}
      {showEnquiryForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Send Enquiry</h3>
              <button
                onClick={() => setShowEnquiryForm(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            <form onSubmit={handleEnquiry} className="space-y-4">
              <Input
                placeholder="Your Name"
                value={enquiryForm.name}
                onChange={(e) => setEnquiryForm({ ...enquiryForm, name: e.target.value })}
                required
              />
              <Input
                type="email"
                placeholder="Your Email"
                value={enquiryForm.email}
                onChange={(e) => setEnquiryForm({ ...enquiryForm, email: e.target.value })}
                required
              />
              <Input
                type="tel"
                placeholder="Your Phone"
                value={enquiryForm.phone}
                onChange={(e) => setEnquiryForm({ ...enquiryForm, phone: e.target.value })}
                required
              />
              <textarea
                placeholder="Your Message"
                value={enquiryForm.message}
                onChange={(e) => setEnquiryForm({ ...enquiryForm, message: e.target.value })}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
              <Button
                type="submit"
                className="w-full"
                disabled={submitting}
                loading={submitting}
              >
                Send Enquiry
              </Button>
            </form>
          </div>
        </div>
      )}

      {/* Image Modal */}
      {showImageModal && property.images && property.images.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center p-4 z-50">
          <div className="relative max-w-4xl w-full">
            <button
              onClick={() => setShowImageModal(false)}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
            >
              <X className="w-8 h-8" />
            </button>
            
            <div className="relative aspect-video">
              <Image
                src={property.images[currentImageIndex]}
                alt={`${property.title} - Image ${currentImageIndex + 1}`}
                fill
                className="object-contain"
              />
            </div>

            {property.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                >
                  <ChevronLeft className="w-8 h-8" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:text-gray-300"
                >
                  <ChevronRight className="w-8 h-8" />
                </button>
              </>
            )}

            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white">
              {currentImageIndex + 1} / {property.images.length}
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  );
}