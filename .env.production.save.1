# Production Environment Configuration (validated against code usage)

# --- Core App ---
NODE_ENV=production
NEXT_PUBLIC_SITE_URL="https://property-trendz.com"
NEXT_PUBLIC_SITE_NAME="Property Trendz"

# Optional: Google Analytics (used in app/layout.tsx)
# NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"

# --- Database (PostgreSQL via Drizzle/Postgres.js) ---
# Used in lib/db/config.ts and drizzle.config.ts

DATABASE_URL="postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties"

# --- Authentication (NextAuth) ---
# Used in lib/auth/auth.config.ts; cookies respect COOKIE_DOMAIN in production
NEXTAUTH_URL="https://property-trendz.com"
NEXTAUTH_SECRET="ucntcomptmeimgr8strivl"
COOKIE_DOMAIN="property-trendz.com"

# --- Security (CSRF) ---
# Required in production per lib/security/csrf.ts
CSRF_SECRET="replace-with-64+char-random-secret"

# --- Rate Limiting (Upstash Redis) [optional] ---
# Used in lib/rate-limit.ts. If unset, in-memory limiter is used.
# UPSTASH_REDIS_REST_URL="https://your-redis-url.upstash.io"
# UPSTASH_REDIS_REST_TOKEN="your-redis-token"

# --- Email (SMTP) ---
# Used in lib/email/config.ts for transactional emails
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"  # true if using port 465
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# --- Public Contact Info (shown in emails/templates) ---
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_CONTACT_PHONE="+91 9810129777"

# --- WhatsApp ---
# Used in lib/whatsapp/config.ts (digits only, country code + number)
NEXT_PUBLIC_WHATSAPP_NUMBER="919810129777"

# --- File Uploads ---
# Used in lib/upload/config.ts; ensure the directory exists and is served
UPLOAD_DIR="./public/uploads"

# --- Cloudinary ---
# No direct Cloudinary API usage found in the codebase. Keep only if you plan to add it.
# NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
# CLOUDINARY_API_KEY="your-api-key"
# CLOUDINARY_API_SECRET="your-api-secret"

