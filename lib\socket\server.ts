import { Server as NetServer } from 'http';
import { NextApiRequest, NextApiResponse } from 'next';
import { Server as SocketIOServer } from 'socket.io';
import { db } from '@/lib/db/config';
import { chatMessages, chatRooms } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';

export type NextApiResponseServerIO = NextApiResponse & {
  socket: {
    server: NetServer & {
      io: SocketIOServer;
    };
  };
};

interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  senderName: string;
  senderRole: 'user' | 'admin' | 'agent';
  message: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  propertyId?: string;
  isRead: boolean;
}

interface ChatRoom {
  id: string;
  propertyId?: string;
  userId: string;
  userName: string;
  userEmail: string;
  status: 'active' | 'closed' | 'pending';
  lastMessage?: string;
  lastMessageAt?: Date;
  unreadCount: number;
  createdAt: Date;
}

interface UserTyping {
  userId: string;
  userName: string;
  roomId: string;
}

export class ChatService {
  private io: SocketIOServer;
  private typingUsers: Map<string, UserTyping> = new Map();

  constructor(io: SocketIOServer) {
    this.io = io;
    this.setupSocketHandlers();
  }

  private setupSocketHandlers() {
    this.io.on('connection', (socket) => {
      console.log('User connected:', socket.id);

      // Join room
      socket.on('join-room', async (data: { roomId: string; userId: string; userName: string }) => {
        try {
          socket.join(data.roomId);
          
          // Load recent messages
          const messages = await this.getRecentMessages(data.roomId);
          socket.emit('previous-messages', messages);
          
          // Mark messages as read for this user
          await this.markMessagesAsRead(data.roomId, data.userId);
          
          // Notify room that user joined
          socket.to(data.roomId).emit('user-joined', {
            userId: data.userId,
            userName: data.userName
          });
          
          console.log(`User ${data.userName} joined room ${data.roomId}`);
        } catch (error) {
          console.error('Error joining room:', error);
          socket.emit('error', { message: 'Failed to join room' });
        }
      });

      // Send message
      socket.on('send-message', async (data: {
        roomId: string;
        senderId: string;
        senderName: string;
        senderRole: 'user' | 'admin' | 'agent';
        message: string;
        type: 'text' | 'image' | 'file';
        propertyId?: string;
      }) => {
        try {
          // Save message to database
          const messageId = await this.saveMessage(data);
          
          const chatMessage: ChatMessage = {
            id: messageId,
            roomId: data.roomId,
            senderId: data.senderId,
            senderName: data.senderName,
            senderRole: data.senderRole,
            message: data.message,
            timestamp: new Date(),
            type: data.type,
            propertyId: data.propertyId,
            isRead: false
          };

          // Broadcast message to room
          this.io.to(data.roomId).emit('new-message', chatMessage);
          
          // Update room last message
          await this.updateRoomLastMessage(data.roomId, data.message);
          
          // Stop typing indicator
          this.handleStopTyping(socket, data.roomId, data.senderId);
          
          console.log(`Message sent in room ${data.roomId} by ${data.senderName}`);
        } catch (error) {
          console.error('Error sending message:', error);
          socket.emit('error', { message: 'Failed to send message' });
        }
      });

      // Typing indicators
      socket.on('typing-start', (data: { roomId: string; userId: string; userName: string }) => {
        this.typingUsers.set(socket.id, data);
        socket.to(data.roomId).emit('user-typing', {
          userId: data.userId,
          userName: data.userName,
          isTyping: true
        });
      });

      socket.on('typing-stop', (data: { roomId: string; userId: string }) => {
        this.handleStopTyping(socket, data.roomId, data.userId);
      });

      // Leave room
      socket.on('leave-room', (roomId: string) => {
        socket.leave(roomId);
        this.handleStopTyping(socket, roomId, '');
      });

      // Admin functions
      socket.on('admin-join', (data: { adminId: string; adminName: string }) => {
        socket.join('admin-room');
        console.log(`Admin ${data.adminName} connected`);
      });

      socket.on('get-active-rooms', async () => {
        try {
          const rooms = await this.getActiveRooms();
          socket.emit('active-rooms', rooms);
        } catch (error) {
          console.error('Error getting active rooms:', error);
          socket.emit('error', { message: 'Failed to get active rooms' });
        }
      });

      // Disconnect
      socket.on('disconnect', () => {
        const typingUser = this.typingUsers.get(socket.id);
        if (typingUser) {
          socket.to(typingUser.roomId).emit('user-typing', {
            userId: typingUser.userId,
            userName: typingUser.userName,
            isTyping: false
          });
          this.typingUsers.delete(socket.id);
        }
        console.log('User disconnected:', socket.id);
      });
    });
  }

  private handleStopTyping(socket: any, roomId: string, userId: string) {
    const typingUser = this.typingUsers.get(socket.id);
    if (typingUser && typingUser.roomId === roomId) {
      socket.to(roomId).emit('user-typing', {
        userId: userId,
        userName: typingUser.userName,
        isTyping: false
      });
      this.typingUsers.delete(socket.id);
    }
  }

  private async saveMessage(data: {
    roomId: string;
    senderId: string;
    senderName: string;
    senderRole: 'user' | 'admin' | 'agent';
    message: string;
    type: 'text' | 'image' | 'file';
    propertyId?: string;
  }): Promise<string> {
    const result = await db.insert(chatMessages).values({
      roomId: data.roomId,
      senderId: data.senderId,
      senderName: data.senderName,
      senderRole: data.senderRole,
      message: data.message,
      type: data.type,
      propertyId: data.propertyId,
      isRead: false,
      createdAt: new Date(),
    }).returning({ id: chatMessages.id });

    return result[0].id;
  }

  private async getRecentMessages(roomId: string, limit: number = 50): Promise<ChatMessage[]> {
    const messages = await db
      .select()
      .from(chatMessages)
      .where(eq(chatMessages.roomId, roomId))
      .orderBy(desc(chatMessages.createdAt))
      .limit(limit);

    return messages.reverse().map(msg => ({
      id: msg.id,
      roomId: msg.roomId,
      senderId: msg.senderId,
      senderName: msg.senderName,
      senderRole: msg.senderRole as 'user' | 'admin' | 'agent',
      message: msg.message,
      timestamp: msg.createdAt,
      type: msg.type as 'text' | 'image' | 'file',
      propertyId: msg.propertyId || undefined,
      isRead: msg.isRead
    }));
  }

  private async markMessagesAsRead(roomId: string, userId: string): Promise<void> {
    await db
      .update(chatMessages)
      .set({ isRead: true })
      .where(
        and(
          eq(chatMessages.roomId, roomId),
          eq(chatMessages.senderId, userId)
        )
      );
  }

  private async updateRoomLastMessage(roomId: string, message: string): Promise<void> {
    await db
      .update(chatRooms)
      .set({
        lastMessage: message,
        lastMessageAt: new Date(),
      })
      .where(eq(chatRooms.id, roomId));
  }

  private async getActiveRooms(): Promise<ChatRoom[]> {
    const rooms = await db
      .select()
      .from(chatRooms)
      .where(eq(chatRooms.status, 'active'))
      .orderBy(desc(chatRooms.lastMessageAt));

    return rooms.map(room => ({
      id: room.id,
      propertyId: room.propertyId || undefined,
      userId: room.userId,
      userName: room.userName,
      userEmail: room.userEmail,
      status: room.status as 'active' | 'closed' | 'pending',
      lastMessage: room.lastMessage || undefined,
      lastMessageAt: room.lastMessageAt || undefined,
      unreadCount: 0, // Will be calculated separately
      createdAt: room.createdAt,
    }));
  }

  // Public methods for API endpoints
  public async createChatRoom(data: {
    userId: string;
    userName: string;
    userEmail: string;
    propertyId?: string;
  }): Promise<string> {
    const result = await db.insert(chatRooms).values({
      userId: data.userId,
      userName: data.userName,
      userEmail: data.userEmail,
      propertyId: data.propertyId,
      status: 'active',
      createdAt: new Date(),
    }).returning({ id: chatRooms.id });

    return result[0].id;
  }

  public async getRoomMessages(roomId: string): Promise<ChatMessage[]> {
    return this.getRecentMessages(roomId);
  }
}