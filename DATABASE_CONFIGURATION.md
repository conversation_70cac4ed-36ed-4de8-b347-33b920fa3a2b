# Database Configuration Guide

This document explains the centralized database configuration system implemented to resolve environment-based database routing issues.

## 🎯 Problem Solved

**Before:** Various parts of the codebase inconsistently used either production or development database URLs regardless of environment.

**After:** Centralized database URL management that automatically routes to the correct database based on environment.

## 🏗️ Architecture

### Centralized Configuration (`lib/db/config.ts`)

The new system provides:
- **Single source of truth** for database configuration
- **Environment-based routing** (development → Docker, production → server)
- **Automatic detection** of environment and database type
- **Type-safe configuration** with TypeScript support
- **Connection optimization** based on environment

### Environment Detection Logic

```typescript
// Development: Docker containerized PostgreSQL
development: 'postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties'

// Production: PostgreSQL on production server  
production: 'postgresql://yash123414:<EMAIL>:5432/armaan_properties'
```

The system automatically detects environment based on:
1. `NODE_ENV` environment variable
2. Presence of production-specific environment variables
3. Database URL patterns (localhost = Docker, remote = production)

## 📁 File Structure

```
lib/db/
├── config.ts          # Centralized database configuration
├── utils.ts           # Database utility functions
├── test-connection.mjs # Connection testing script
└── schema.ts          # Database schema (unchanged)

scripts/
└── validate-db-config.mjs # Configuration validation script
```

## 🔧 Configuration Files

### Development (`.env.local`)
```env
NODE_ENV="development"
DATABASE_URL="postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties"
# ... other development variables
```

### Production (`.env.production`)
```env
NODE_ENV="production"
DATABASE_URL="postgresql://yash123414:<EMAIL>:5432/armaan_properties"
# ... other production variables
```

## 🚀 Usage

### In Application Code

```typescript
// Import the centralized database instance
import { db } from '@/lib/db/config';

// Use normally - automatically routes to correct database
const properties = await db.select().from(properties);
```

### Database Information

```typescript
import { databaseConfig } from '@/lib/db/config';

console.log(databaseConfig.environment); // 'development' | 'production'
console.log(databaseConfig.isDocker);    // true for development
console.log(databaseConfig.host);        // Database host
```

### Utility Functions

```typescript
import { testDatabaseConnection, getDatabaseInfo } from '@/lib/db/utils';

// Test connection
const result = await testDatabaseConnection();
if (result.success) {
  console.log('Database connected');
}

// Get configuration info
const info = getDatabaseInfo();
console.log(`Connected to ${info.environment} database`);
```

## 🛠️ Commands

### Validation and Testing
```bash
# Validate database configuration
npm run db:validate

# Test database connection
npm run db:test

# Run database migrations
npm run db:push

# Open database studio
npm run db:studio
```

### Development Workflow
```bash
# 1. Start Docker containers
docker-compose up -d

# 2. Validate configuration
npm run db:validate

# 3. Test connection
npm run db:test

# 4. Run migrations
npm run db:push

# 5. Start development server
npm run dev
```

### Production Deployment
```bash
# 1. Set NODE_ENV
export NODE_ENV=production

# 2. Validate production config
npm run db:validate

# 3. Test production database
npm run db:test

# 4. Run migrations
npm run db:push

# 5. Start production server
npm run start
```

## 🔍 Troubleshooting

### Configuration Issues

**Problem:** Database connection fails
**Solution:** 
```bash
npm run db:validate  # Check configuration
npm run db:test      # Test connection
```

**Problem:** Wrong database being used
**Solution:** Check environment variables and file loading:
```bash
echo $NODE_ENV
echo $DATABASE_URL
npm run db:validate
```

### Environment-Specific Issues

**Development (Docker):**
- Ensure Docker containers are running: `docker ps`
- Start containers: `docker-compose up -d`
- Check logs: `docker logs armaan_properties_db`

**Production:**
- Verify network connectivity: `ping srv949995.hstar.cloud`
- Check firewall rules and database server status
- Verify credentials and database exists

## 📊 Health Monitoring

The system includes enhanced health checking:

### API Health Check
```bash
curl https://property-trendz.com/api/health
```

Response includes database information:
```json
{
  "status": "healthy",
  "database": {
    "environment": "production",
    "host": "srv949995.hstar.cloud",
    "port": 5432,
    "database": "armaan_properties",
    "isDocker": false,
    "status": "connected"
  }
}
```

## 🔒 Security Considerations

- Database credentials are environment-specific
- Production uses secure connection settings
- Connection pooling optimized per environment
- SSL enabled for production connections
- Passwords hidden in logging and debugging output

## 🔄 Migration from Old System

All existing code automatically benefits from the new system:
- No changes required to existing API routes
- All database imports use centralized configuration
- Drizzle configuration updated to use centralized system
- Test scripts updated for environment detection

## 📈 Benefits

1. **Consistency:** All database connections use the same configuration logic
2. **Environment Safety:** Impossible to accidentally use wrong database
3. **Debugging:** Clear visibility into which database is being used
4. **Maintenance:** Single place to update database configuration
5. **Type Safety:** Full TypeScript support with proper types
6. **Performance:** Environment-optimized connection settings
7. **Monitoring:** Built-in health checks and validation tools
