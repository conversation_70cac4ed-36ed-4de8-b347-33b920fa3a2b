import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { properties, propertyImages } from '@/lib/db/schema';
import { desc, asc, eq, like, and, or, gte, lte, sql, inArray } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Pagination
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const offset = (page - 1) * limit;

    // Search filters
    const search = searchParams.get('search') || '';
    const location = searchParams.get('location') || '';
    const propertyType = searchParams.get('propertyType') || '';
    const listingType = searchParams.get('listingType') || '';
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const bedrooms = searchParams.get('bedrooms');
    const bathrooms = searchParams.get('bathrooms');
    const minArea = searchParams.get('minArea');
    const maxArea = searchParams.get('maxArea');
    const furnished = searchParams.get('furnished') || '';
    const ageOfProperty = searchParams.get('ageOfProperty') || '';
    const amenities = searchParams.get('amenities')?.split(',').filter(a => a.trim()) || [];
    const status = searchParams.get('status') || '';
    const featured = searchParams.get('featured');
    const ids = searchParams.get('ids')?.split(',').filter(id => id.trim()) || [];
    
    // Sorting
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build where conditions
    const whereConditions = [];

    // If specific IDs are requested, use those instead of other filters
    if (ids.length > 0) {
      whereConditions.push(inArray(properties.id, ids));
      // Still only show published properties
      whereConditions.push(eq(properties.published, true));
    } else {
      // Only show published properties for public API
      whereConditions.push(eq(properties.published, true));

      // Status filter - include for-sale and for-rent by default, exclude sold/unavailable
      if (status) {
        whereConditions.push(eq(properties.status, status));
      } else {
        // Default: show available properties (for-sale and for-rent)
        whereConditions.push(
          or(
            eq(properties.status, 'for-sale'),
            eq(properties.status, 'for-rent')
          )
        );
      }
    }

    // Apply other filters only if not querying by specific IDs
    if (ids.length === 0) {
      // Search in title, description, area, city
      if (search) {
        whereConditions.push(
          or(
            like(properties.title, `%${search}%`),
            like(properties.description, `%${search}%`),
            like(properties.area, `%${search}%`),
            like(properties.city, `%${search}%`)
          )
        );
      }

      // Location filter (area or city)
      if (location) {
        whereConditions.push(
          or(
            like(properties.area, `%${location}%`),
            like(properties.city, `%${location}%`),
            like(properties.address, `%${location}%`)
          )
        );
      }

      // Property type filter
      if (propertyType) {
        whereConditions.push(eq(properties.propertyType, propertyType));
      }

      // Listing type filter
      if (listingType) {
        whereConditions.push(eq(properties.listingType, listingType));
      }

      // Price range filter
      if (minPrice) {
        whereConditions.push(gte(properties.price, minPrice));
      }
      if (maxPrice) {
        whereConditions.push(lte(properties.price, maxPrice));
      }

      // Bedrooms filter
      if (bedrooms) {
        if (bedrooms === '5') {
          whereConditions.push(gte(properties.bedrooms, 5));
        } else {
          whereConditions.push(eq(properties.bedrooms, parseInt(bedrooms)));
        }
      }

      // Bathrooms filter
      if (bathrooms) {
        if (bathrooms === '4') {
          whereConditions.push(gte(properties.bathrooms, 4));
        } else {
          whereConditions.push(eq(properties.bathrooms, parseInt(bathrooms)));
        }
      }

      // Area range filter
      if (minArea) {
        whereConditions.push(gte(properties.totalArea, parseInt(minArea)));
      }
      if (maxArea) {
        whereConditions.push(lte(properties.totalArea, parseInt(maxArea)));
      }

      // Furnished status filter
      if (furnished) {
        whereConditions.push(eq(properties.furnished, furnished));
      }

      // Property age filter
      if (ageOfProperty) {
        if (ageOfProperty === '0-1') {
          whereConditions.push(lte(properties.ageOfProperty, 1));
        } else if (ageOfProperty === '1-3') {
          whereConditions.push(and(
            gte(properties.ageOfProperty, 1),
            lte(properties.ageOfProperty, 3)
          ));
        } else if (ageOfProperty === '3-5') {
          whereConditions.push(and(
            gte(properties.ageOfProperty, 3),
            lte(properties.ageOfProperty, 5)
          ));
        } else if (ageOfProperty === '5-10') {
          whereConditions.push(and(
            gte(properties.ageOfProperty, 5),
            lte(properties.ageOfProperty, 10)
          ));
        } else if (ageOfProperty === '10+') {
          whereConditions.push(gte(properties.ageOfProperty, 10));
        }
      }

      // Featured filter
      if (featured === 'true') {
        whereConditions.push(eq(properties.featured, true));
      }

      // Amenities filter (check if all selected amenities are present)
      if (amenities.length > 0) {
        for (const amenity of amenities) {
          whereConditions.push(
            sql`JSON_SEARCH(${properties.amenities}, 'one', ${amenity}) IS NOT NULL`
          );
        }
      }
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Determine sort order
    let orderBy;
    
    // Safe field mapping for sorting
    const validSortFields = {
      'createdAt': properties.createdAt,
      'price': properties.price,
      'title': properties.title,
      'area': properties.area,
      'city': properties.city,
      'bedrooms': properties.bedrooms,
      'bathrooms': properties.bathrooms,
      'totalArea': properties.totalArea,
    } as const;
    
    const sortField = validSortFields[sortBy as keyof typeof validSortFields] || properties.createdAt;
    
    if (sortOrder === 'asc') {
      orderBy = asc(sortField);
    } else {
      orderBy = desc(sortField);
    }

    // Get properties with filters and pagination
    const baseQuery = db
      .select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        slug: properties.slug,
        price: properties.price,
        pricePerSqft: properties.pricePerSqft,
        area: properties.area,
        city: properties.city,
        state: properties.state,
        address: properties.address,
        pincode: properties.pincode,
        latitude: properties.latitude,
        longitude: properties.longitude,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        totalArea: properties.totalArea,
        carpetArea: properties.carpetArea,
        builtUpArea: properties.builtUpArea,
        floors: properties.floors,
        totalFloors: properties.totalFloors,
        parking: properties.parking,
        balconies: properties.balconies,
        furnished: properties.furnished,
        ageOfProperty: properties.ageOfProperty,
        facing: properties.facing,
        propertyType: properties.propertyType,
        subType: properties.subType,
        listingType: properties.listingType,
        status: properties.status,
        amenities: properties.amenities,
        features: properties.features,
        images: properties.images,
        contactName: properties.contactName,
        contactPhone: properties.contactPhone,
        contactEmail: properties.contactEmail,
        contactWhatsapp: properties.contactWhatsapp,
        featured: properties.featured,
        verified: properties.verified,
        views: properties.views,
        enquiries: properties.enquiries,
        metaTitle: properties.metaTitle,
        metaDescription: properties.metaDescription,
        createdAt: properties.createdAt,
        updatedAt: properties.updatedAt,
        publishedAt: properties.publishedAt,
      })
      .from(properties)
      .where(whereClause)
      .orderBy(orderBy);

    // Apply pagination only if not querying by specific IDs
    const propertiesList = ids.length === 0 
      ? await baseQuery.limit(limit).offset(offset)
      : await baseQuery;

    // Get images for each property
    const propertyIds = propertiesList.map(p => p.id);
    let imagesList: any[] = [];
    
    if (propertyIds.length > 0) {
      imagesList = await db
        .select({
          propertyId: propertyImages.propertyId,
          url: propertyImages.url,
          alt: propertyImages.alt,
          caption: propertyImages.caption,
          order: propertyImages.order,
          type: propertyImages.type,
          isFeatured: propertyImages.isFeatured,
        })
        .from(propertyImages)
        .where(inArray(propertyImages.propertyId, propertyIds))
        .orderBy(asc(propertyImages.order));
    }

    // Get total count for pagination
    const totalCountResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(properties)
      .where(whereClause);

    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Create a map of property images
    const imagesMap = new Map<string, any[]>();
    imagesList.forEach(image => {
      if (!imagesMap.has(image.propertyId)) {
        imagesMap.set(image.propertyId, []);
      }
      imagesMap.get(image.propertyId)!.push({
        url: image.url,
        alt: image.alt,
        caption: image.caption,
        order: image.order,
        type: image.type,
        isFeatured: image.isFeatured,
      });
    });

    // Format the response
    const formattedProperties = propertiesList.map(property => ({
      ...property,
      price: property.price ? parseFloat(property.price) : 0,
      pricePerSqft: property.pricePerSqft ? parseFloat(property.pricePerSqft) : null,
      latitude: property.latitude ? parseFloat(property.latitude) : null,
      longitude: property.longitude ? parseFloat(property.longitude) : null,
      images: imagesMap.get(property.id) || [], // Use images from propertyImages table
    }));

    return NextResponse.json({
      properties: formattedProperties,
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
        limit,
      },
      filters: {
        search,
        location,
        propertyType,
        listingType,
        minPrice,
        maxPrice,
        bedrooms,
        bathrooms,
        minArea,
        maxArea,
        furnished,
        ageOfProperty,
        amenities,
        sortBy,
        sortOrder,
      },
    });
  } catch (error) {
    console.error('Properties API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}