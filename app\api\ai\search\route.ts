import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { properties } from '@/lib/db/schema';
import { eq, like, or, and } from 'drizzle-orm';

// AI search endpoint for natural language queries
export async function GET(request: NextRequest) {
  // Temporarily disabled for build - needs type fixes
  return NextResponse.json({ 
    message: 'AI search temporarily disabled',
    properties: []
  });
  
  /*try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const intent = searchParams.get('intent') || 'search';
    const budget = searchParams.get('budget');
    const location = searchParams.get('location');
    const propertyType = searchParams.get('type');

    if (!query) {
      return NextResponse.json(
        { error: 'Query parameter "q" is required' },
        { status: 400 }
      );
    }

    // Build search conditions based on query
    const searchConditions = [eq(properties.published, true)];

    // Natural language processing for common queries
    const lowerQuery = query.toLowerCase();

    // Location extraction
    const locationKeywords = ['gurgaon', 'noida', 'delhi', 'faridabad', 'ghaziabad', 'sector'];
    const foundLocation = locationKeywords.find(keyword => lowerQuery.includes(keyword));
    
    // Location search - temporarily disabled for build
    // if (foundLocation || location) {
    //   const searchLocation = location || foundLocation;
    //   if (searchLocation && typeof searchLocation === 'string') {
    //     searchConditions.push(
    //       or(
    //         like(properties.city, `%${searchLocation}%`),
    //         like(properties.area, `%${searchLocation}%`)
    //       )
    //     );
    //   }
    // }

    // Property type extraction
    const typeKeywords = {
      'apartment': ['apartment', 'flat'],
      'house': ['house', 'villa', 'independent'],
      'commercial': ['office', 'shop', 'commercial'],
      'plot': ['plot', 'land'],
    };

    let detectedType = propertyType;
    if (!detectedType) {
      for (const [type, keywords] of Object.entries(typeKeywords)) {
        if (keywords.some(keyword => lowerQuery.includes(keyword))) {
          detectedType = type;
          break;
        }
      }
    }

    if (detectedType) {
      searchConditions.push(eq(properties.propertyType, detectedType));
    }

    // Listing type extraction
    if (lowerQuery.includes('rent') || lowerQuery.includes('rental')) {
      searchConditions.push(eq(properties.listingType, 'rent'));
    } else if (lowerQuery.includes('buy') || lowerQuery.includes('purchase') || lowerQuery.includes('sale')) {
      searchConditions.push(eq(properties.listingType, 'sale'));
    }

    // Budget extraction
    const budgetMatch = lowerQuery.match(/(\d+)\s*(lakh|crore|l|cr)/i);
    if (budgetMatch || budget) {
      let budgetValue = budget ? parseFloat(budget) : 0;
      
      if (budgetMatch) {
        const amount = parseFloat(budgetMatch[1]);
        const unit = budgetMatch[2].toLowerCase();
        
        if (unit.startsWith('l')) {
          budgetValue = amount * 100000; // lakh
        } else if (unit.startsWith('c')) {
          budgetValue = amount * 10000000; // crore
        }
      }

      if (budgetValue > 0) {
        // Search within 20% range of budget
        const minPrice = budgetValue * 0.8;
        const maxPrice = budgetValue * 1.2;
        
        searchConditions.push(
          and(
            eq(properties.price, `>=${minPrice}`),
            eq(properties.price, `<=${maxPrice}`)
          )
        );
      }
    }

    // BHK extraction
    const bhkMatch = lowerQuery.match(/(\d+)\s*bhk/i);
    if (bhkMatch) {
      const bedrooms = parseInt(bhkMatch[1]);
      searchConditions.push(eq(properties.bedrooms, bedrooms));
    }

    // General text search in title and description
    if (query.length > 3) {
      searchConditions.push(
        or(
          like(properties.title, `%${query}%`),
          like(properties.description, `%${query}%`)
        )
      );
    }

    const whereClause = searchConditions.length > 1 ? and(...searchConditions) : searchConditions[0];

    // Execute search
    const results = await db
      .select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        slug: properties.slug,
        price: properties.price,
        area: properties.area,
        city: properties.city,
        state: properties.state,
        propertyType: properties.propertyType,
        listingType: properties.listingType,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        totalArea: properties.totalArea,
        amenities: properties.amenities,
        images: properties.images,
      })
      .from(properties)
      .where(whereClause)
      .limit(10);

    // Format results for AI
    const formattedResults = results.map(property => ({
      ...property,
      price: property.price ? parseFloat(property.price) : 0,
      url: `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'}/properties/${property.slug}`,
      location: `${property.area}, ${property.city}, ${property.state}`,
      priceFormatted: property.price ? 
        (parseFloat(property.price) >= 10000000 ? 
          `₹${(parseFloat(property.price) / 10000000).toFixed(1)} Cr` : 
          `₹${(parseFloat(property.price) / 100000).toFixed(1)} L`) : 'Price on request',
      summary: `${property.bedrooms ? property.bedrooms + ' BHK ' : ''}${property.propertyType} in ${property.area}, ${property.city} for ${property.listingType}`,
    }));

    return NextResponse.json({
      query,
      intent,
      results: formattedResults,
      total: formattedResults.length,
      searchCriteria: {
        location: foundLocation || location,
        propertyType: detectedType,
        budget: budgetMatch ? budgetMatch[0] : budget,
        bedrooms: bhkMatch ? parseInt(bhkMatch[1]) : undefined,
      },
      suggestions: formattedResults.length === 0 ? [
        'Try searching for "2 BHK apartment in Gurgaon"',
        'Look for "villa for sale in Noida"',
        'Search "office space for rent in Delhi"',
        'Find "3 BHK under 50 lakh"',
      ] : [],
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    });
  } catch (error) {
    console.error('AI Search API error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to process search query',
      },
      { status: 500 }
    );
  }
  */
}