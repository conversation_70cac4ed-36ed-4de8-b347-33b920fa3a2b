'use client';

import { useEffect } from 'react';

interface PropertyStructuredDataProps {
  property: any;
}

export function PropertyStructuredData({ property }: PropertyStructuredDataProps) {
  useEffect(() => {
    const propertyImages = property.images?.map((img: any) => 
      typeof img === 'string' ? img : img.url
    ).filter(Boolean) || [];

    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'RealEstateListing',
      '@id': `${window.location.origin}/properties/${property.slug}`,
      name: property.title,
      description: property.description,
      url: `${window.location.origin}/properties/${property.slug}`,
      image: propertyImages.length > 0 ? propertyImages : undefined,
      address: {
        '@type': 'PostalAddress',
        streetAddress: property.address,
        addressLocality: property.area,
        addressRegion: property.state,
        postalCode: property.pincode,
        addressCountry: 'IN',
      },
      geo: property.latitude && property.longitude ? {
        '@type': 'GeoCoordinates',
        latitude: parseFloat(property.latitude),
        longitude: parseFloat(property.longitude),
      } : undefined,
      floorSize: property.totalArea ? {
        '@type': 'QuantitativeValue',
        value: parseFloat(property.totalArea),
        unitText: 'SQF',
        unitCode: 'SQF'
      } : undefined,
      numberOfRooms: property.bedrooms ? parseInt(property.bedrooms) : undefined,
      numberOfBathrooms: property.bathrooms ? parseInt(property.bathrooms) : undefined,
      numberOfBedrooms: property.bedrooms ? parseInt(property.bedrooms) : undefined,
      accommodationCategory: property.propertyType,
      accommodationFloorPlan: {
        '@type': 'FloorPlan',
        numberOfRooms: property.bedrooms ? parseInt(property.bedrooms) : undefined,
        numberOfBathrooms: property.bathrooms ? parseInt(property.bathrooms) : undefined,
        floorSize: property.totalArea ? {
          '@type': 'QuantitativeValue',
          value: parseFloat(property.totalArea),
          unitText: 'SQF'
        } : undefined,
      },
      offers: {
        '@type': 'Offer',
        '@id': `${window.location.origin}/properties/${property.slug}#offer`,
        price: property.price ? parseFloat(property.price) : undefined,
        priceCurrency: 'INR',
        priceSpecification: {
          '@type': 'PriceSpecification',
          price: property.price ? parseFloat(property.price) : undefined,
          priceCurrency: 'INR',
          unitText: property.listingType === 'rent' ? 'monthly' : undefined,
        },
        availability: property.status === 'available' ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
        validFrom: property.publishedAt || property.createdAt,
        validThrough: property.listingType === 'rent' ? 
          new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString() : undefined,
        seller: {
          '@type': 'RealEstateAgent',
          name: property.contactName || 'Property Trendz',
          telephone: property.contactPhone,
          email: property.contactEmail,
          url: window.location.origin,
        },
      },
      category: property.propertyType,
      amenityFeature: property.amenities?.map((amenity: string) => ({
        '@type': 'LocationFeatureSpecification',
        name: amenity,
        value: true
      })),
      additionalProperty: [
        ...(property.furnished ? [{
          '@type': 'PropertyValue',
          name: 'Furnished',
          value: property.furnished
        }] : []),
        ...(property.parking ? [{
          '@type': 'PropertyValue',
          name: 'Parking',
          value: property.parking
        }] : []),
        ...(property.floors ? [{
          '@type': 'PropertyValue',
          name: 'Floor',
          value: property.floors
        }] : []),
        ...(property.totalFloors ? [{
          '@type': 'PropertyValue',
          name: 'Total Floors',
          value: property.totalFloors
        }] : []),
        ...(property.ageOfProperty ? [{
          '@type': 'PropertyValue',
          name: 'Age of Property',
          value: property.ageOfProperty
        }] : []),
        ...(property.facing ? [{
          '@type': 'PropertyValue',
          name: 'Facing',
          value: property.facing
        }] : []),
      ],
      datePosted: property.publishedAt || property.createdAt,
      dateModified: property.updatedAt,
      potentialAction: {
        '@type': 'ViewAction',
        target: `${window.location.origin}/properties/${property.slug}`,
        name: 'View Property Details'
      }
    };

    // Remove undefined values
    const cleanedData = JSON.parse(JSON.stringify(structuredData));

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(cleanedData);
    script.id = 'property-structured-data';
    
    // Remove existing script if present
    const existingScript = document.getElementById('property-structured-data');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById('property-structured-data');
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, [property]);

  return null;
}

interface OrganizationStructuredDataProps {
  settings?: any;
}

export function OrganizationStructuredData({ settings }: OrganizationStructuredDataProps) {
  useEffect(() => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': ['RealEstateAgent', 'Organization'],
      '@id': `${window.location.origin}#organization`,
      name: settings?.siteName || 'Property Trendz',
      alternateName: 'Property Trendz Properties',
      description: settings?.siteDescription || 'Your trusted partner for finding the perfect property in Delhi NCR region with over 35+ years of experience.',
      url: window.location.origin,
      logo: {
        '@type': 'ImageObject',
        url: settings?.seo?.ogImage || `${window.location.origin}/logo.png`,
        width: 512,
        height: 512
      },
      image: settings?.seo?.ogImage || `${window.location.origin}/logo.png`,
      foundingDate: '1989',
      founder: {
        '@type': 'Person',
        name: 'Property Trendz Team'
      },
      contactPoint: [
        {
          '@type': 'ContactPoint',
          telephone: '+91 9810129777',
          email: '<EMAIL>',
          contactType: 'Customer Service',
          areaServed: 'Delhi NCR',
          availableLanguage: ['English', 'Hindi'],
          hoursAvailable: {
            '@type': 'OpeningHoursSpecification',
            dayOfWeek: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            opens: '09:00',
            closes: '21:00'
          }
        },
        {
          '@type': 'ContactPoint',
          telephone: '+91 9810129777',
          contactType: 'Sales',
          areaServed: 'Delhi NCR'
        }
      ],
      address: {
        '@type': 'PostalAddress',
        addressRegion: 'Delhi NCR',
        addressCountry: 'IN',
        addressLocality: 'Delhi'
      },
      sameAs: [
        'https://www.facebook.com/propertrendz',
        'https://www.instagram.com/propertrendz',
        'https://www.twitter.com/propertrendz',
        'https://www.linkedin.com/company/propertrendz',
        settings?.socialMedia?.facebook,
        settings?.socialMedia?.instagram,
        settings?.socialMedia?.twitter,
        settings?.socialMedia?.linkedin,
      ].filter(Boolean),
      areaServed: [
        {
          '@type': 'City',
          name: 'Delhi'
        },
        {
          '@type': 'City', 
          name: 'Gurgaon'
        },
        {
          '@type': 'City',
          name: 'Noida'
        },
        {
          '@type': 'City',
          name: 'Faridabad'
        },
        {
          '@type': 'City',
          name: 'Ghaziabad'
        }
      ],
      serviceType: ['Real Estate Sales', 'Property Rental', 'Property Management', 'Real Estate Consultation'],
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.8',
        reviewCount: '500',
        bestRating: '5',
        worstRating: '1'
      },
      knowsAbout: ['Real Estate', 'Property Investment', 'Delhi NCR Properties', 'Residential Properties', 'Commercial Properties'],
      hasOfferCatalog: {
        '@type': 'OfferCatalog',
        name: 'Property Listings',
        itemListElement: [
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Residential Property Sales'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service', 
              name: 'Commercial Property Sales'
            }
          },
          {
            '@type': 'Offer',
            itemOffered: {
              '@type': 'Service',
              name: 'Property Rental Services'
            }
          }
        ]
      }
    };

    // Remove undefined values
    const cleanedData = JSON.parse(JSON.stringify(structuredData));

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(cleanedData);
    script.id = 'organization-structured-data';
    
    // Remove existing script if present
    const existingScript = document.getElementById('organization-structured-data');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById('organization-structured-data');
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, [settings]);

  return null;
}

export function WebsiteStructuredData() {
  useEffect(() => {
    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      '@id': `${window.location.origin}#website`,
      name: 'Property Trendz',
      alternateName: 'Property Trendz Properties',
      url: window.location.origin,
      description: 'Find your dream home in Delhi NCR with Property Trendz. Browse through thousands of verified properties for sale and rent.',
      publisher: {
        '@id': `${window.location.origin}#organization`
      },
      potentialAction: [
        {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${window.location.origin}/properties?search={search_term_string}`,
          },
          'query-input': 'required name=search_term_string',
        },
        {
          '@type': 'SearchAction',
          target: {
            '@type': 'EntryPoint',
            urlTemplate: `${window.location.origin}/properties?location={location_string}`,
          },
          'query-input': 'required name=location_string',
        }
      ],
      mainEntity: {
        '@type': 'ItemList',
        name: 'Property Listings',
        description: 'Latest property listings in Delhi NCR',
        url: `${window.location.origin}/properties`
      },
      copyrightYear: new Date().getFullYear(),
      copyrightHolder: {
        '@id': `${window.location.origin}#organization`
      },
      inLanguage: ['en-IN', 'hi-IN'],
      audience: {
        '@type': 'Audience',
        audienceType: 'Property Buyers and Renters',
        geographicArea: {
          '@type': 'Place',
          name: 'Delhi NCR'
        }
      }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    script.id = 'website-structured-data';
    
    // Remove existing script if present
    const existingScript = document.getElementById('website-structured-data');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById('website-structured-data');
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, []);

  return null;
}

interface BreadcrumbStructuredDataProps {
  items: Array<{
    name: string;
    url: string;
  }>;
}

export function BreadcrumbStructuredData({ items }: BreadcrumbStructuredDataProps) {
  useEffect(() => {
    if (!items || items.length === 0) return;

    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: items.map((item, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        name: item.name,
        item: item.url.startsWith('http') ? item.url : `${window.location.origin}${item.url}`
      }))
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    script.id = 'breadcrumb-structured-data';
    
    // Remove existing script if present
    const existingScript = document.getElementById('breadcrumb-structured-data');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById('breadcrumb-structured-data');
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, [items]);

  return null;
}

interface FAQStructuredDataProps {
  faqs: Array<{
    question: string;
    answer: string;
  }>;
}

export function FAQStructuredData({ faqs }: FAQStructuredDataProps) {
  useEffect(() => {
    if (!faqs || faqs.length === 0) return;

    const structuredData = {
      '@context': 'https://schema.org',
      '@type': 'FAQPage',
      mainEntity: faqs.map(faq => ({
        '@type': 'Question',
        name: faq.question,
        acceptedAnswer: {
          '@type': 'Answer',
          text: faq.answer
        }
      }))
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    script.id = 'faq-structured-data';
    
    // Remove existing script if present
    const existingScript = document.getElementById('faq-structured-data');
    if (existingScript) {
      document.head.removeChild(existingScript);
    }
    
    document.head.appendChild(script);

    return () => {
      const scriptToRemove = document.getElementById('faq-structured-data');
      if (scriptToRemove) {
        document.head.removeChild(scriptToRemove);
      }
    };
  }, [faqs]);

  return null;
}