import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { properties, users, enquiries, propertyViews } from '@/lib/db/schema';
import { count, desc } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get total counts
    const [
      totalPropertiesResult,
      totalUsersResult,
      totalEnquiriesResult,
      totalViewsResult,
    ] = await Promise.all([
      db.select({ count: count() }).from(properties),
      db.select({ count: count() }).from(users),
      db.select({ count: count() }).from(enquiries),
      db.select({ count: count() }).from(propertyViews),
    ]);

    // Get recent properties
    const recentProperties = await db
      .select({
        id: properties.id,
        title: properties.title,
        area: properties.area,
        city: properties.city,
        price: properties.price,
        status: properties.status,
        propertyType: properties.propertyType,
        images: properties.images,
        views: properties.views,
        createdAt: properties.createdAt,
      })
      .from(properties)
      .orderBy(desc(properties.createdAt))
      .limit(10);

    // Get recent enquiries
    const recentEnquiries = await db
      .select({
        id: enquiries.id,
        name: enquiries.name,
        email: enquiries.email,
        phone: enquiries.phone,
        message: enquiries.message,
        status: enquiries.status,
        createdAt: enquiries.createdAt,
        propertyId: enquiries.propertyId,
      })
      .from(enquiries)
      .orderBy(desc(enquiries.createdAt))
      .limit(10);

    // Format the response
    const dashboardData = {
      totalProperties: totalPropertiesResult[0]?.count || 0,
      totalUsers: totalUsersResult[0]?.count || 0,
      totalEnquiries: totalEnquiriesResult[0]?.count || 0,
      totalViews: totalViewsResult[0]?.count || 0,
      recentProperties: recentProperties.map(property => ({
        ...property,
        price: property.price ? parseFloat(property.price) : 0,
        location: `${property.area}, ${property.city}`,
      })),
      recentEnquiries: recentEnquiries.map(enquiry => ({
        ...enquiry,
        property: recentProperties.find(p => p.id === enquiry.propertyId),
      })),
    };

    return NextResponse.json(dashboardData);
  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}