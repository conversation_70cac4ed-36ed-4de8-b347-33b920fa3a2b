'use client';

import { useEffect, useState } from 'react';

interface CSRFTokenProps {
  name?: string;
}

/**
 * Hidden input component that automatically includes CSRF token in forms
 */
export function CSRFToken({ name = '_csrf' }: CSRFTokenProps) {
  const [token, setToken] = useState<string>('');

  useEffect(() => {
    // Get CSRF token from cookie
    const getCSRFToken = () => {
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [cookieName, cookieValue] = cookie.trim().split('=');
        if (cookieName === 'csrf-token') {
          return decodeURIComponent(cookieValue);
        }
      }
      return '';
    };

    // Set token or fetch from API if not available
    const csrfToken = getCSRFToken();
    if (csrfToken) {
      setToken(csrfToken);
    } else {
      // Fetch token from API
      fetch('/api/csrf-token')
        .then(res => res.json())
        .then(data => {
          if (data.token) {
            setToken(data.token);
          }
        })
        .catch(error => {
          console.error('Failed to fetch CSRF token:', error);
        });
    }
  }, []);

  if (!token) {
    return null;
  }

  return (
    <input
      type="hidden"
      name={name}
      value={token}
      data-testid="csrf-token"
    />
  );
}

/**
 * Hook to get CSRF token for API requests
 */
export function useCSRFToken(): string {
  const [token, setToken] = useState<string>('');

  useEffect(() => {
    const getCSRFToken = () => {
      const cookies = document.cookie.split(';');
      for (let cookie of cookies) {
        const [cookieName, cookieValue] = cookie.trim().split('=');
        if (cookieName === 'csrf-token') {
          return decodeURIComponent(cookieValue);
        }
      }
      return '';
    };

    const csrfToken = getCSRFToken();
    if (csrfToken) {
      setToken(csrfToken);
    } else {
      // Fetch token from API
      fetch('/api/csrf-token')
        .then(res => res.json())
        .then(data => {
          if (data.token) {
            setToken(data.token);
          }
        })
        .catch(error => {
          console.error('Failed to fetch CSRF token:', error);
        });
    }
  }, []);

  return token;
}

/**
 * Enhanced fetch function that automatically includes CSRF token
 */
export async function csrfFetch(url: string, options: RequestInit = {}): Promise<Response> {
  // Get CSRF token from cookie
  const getCSRFToken = () => {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [cookieName, cookieValue] = cookie.trim().split('=');
      if (cookieName === 'csrf-token') {
        return decodeURIComponent(cookieValue);
      }
    }
    return '';
  };

  const token = getCSRFToken();
  
  // Add CSRF token to headers for non-GET requests
  const method = options.method?.toUpperCase() || 'GET';
  if (!['GET', 'HEAD', 'OPTIONS'].includes(method) && token) {
    options.headers = {
      ...options.headers,
      'x-csrf-token': token,
    };
  }

  return fetch(url, options);
}