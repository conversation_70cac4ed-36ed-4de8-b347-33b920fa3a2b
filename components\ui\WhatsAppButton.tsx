'use client';

import { MessageCircle, Phone, Calendar, FileText, DollarSign, MapPin } from 'lucide-react';
import { useState } from 'react';
import { WhatsAppService, useWhatsApp } from '@/lib/whatsapp/config';
import { Button } from './Button';

interface WhatsAppButtonProps {
  variant?: 'primary' | 'secondary' | 'floating';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  message?: string;
  customerName?: string;
  showIcon?: boolean;
  children?: React.ReactNode;
}

export function WhatsAppButton({
  variant = 'primary',
  size = 'md',
  className = '',
  message = WhatsAppService.templates.generalInquiry,
  customerName,
  showIcon = true,
  children
}: WhatsAppButtonProps) {
  const { sendBusinessInquiry } = useWhatsApp();

  const handleClick = () => {
    sendBusinessInquiry(message, customerName);
  };

  const baseClasses = 'inline-flex items-center justify-center gap-2 font-medium transition-all duration-200';
  
  const variantClasses = {
    primary: 'bg-green-500 hover:bg-green-600 text-white shadow-lg hover:shadow-xl',
    secondary: 'bg-white hover:bg-gray-50 text-green-600 border-2 border-green-500',
    floating: 'bg-green-500 hover:bg-green-600 text-white shadow-2xl hover:shadow-3xl rounded-full animate-pulse hover:animate-none'
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm rounded-md',
    md: 'px-4 py-2.5 text-base rounded-lg',
    lg: 'px-6 py-3 text-lg rounded-xl'
  };

  const iconSizes = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <button
      onClick={handleClick}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {showIcon && <MessageCircle className={iconSizes[size]} />}
      {children || 'WhatsApp'}
    </button>
  );
}

interface PropertyWhatsAppButtonProps {
  property: {
    title: string;
    slug: string;
    price: number;
  };
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  message?: string;
  variant?: 'inquiry' | 'viewing' | 'callback';
  className?: string;
}

export function PropertyWhatsAppButton({
  property,
  customerName,
  customerPhone,
  customerEmail,
  message,
  variant = 'inquiry',
  className = ''
}: PropertyWhatsAppButtonProps) {
  const whatsapp = useWhatsApp();

  const formatPrice = (price: number): string => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(0)}L`;
    } else if (price >= 1000) {
      return `₹${(price / 1000).toFixed(0)}K`;
    }
    return `₹${price}`;
  };

  const propertyInquiry = {
    propertyTitle: property.title,
    propertyUrl: `${process.env.NEXT_PUBLIC_SITE_URL}/properties/${property.slug}`,
    propertyPrice: formatPrice(property.price),
    customerName,
    customerPhone,
    customerEmail,
    message
  };

  const handleClick = () => {
    switch (variant) {
      case 'viewing':
        whatsapp.requestViewing(propertyInquiry);
        break;
      case 'callback':
        if (customerName && customerPhone) {
          whatsapp.requestCallback(customerName, customerPhone);
        } else {
          whatsapp.sendPropertyInquiry(propertyInquiry);
        }
        break;
      default:
        whatsapp.sendPropertyInquiry(propertyInquiry);
    }
  };

  const buttonContent = {
    inquiry: { icon: MessageCircle, text: 'Inquire on WhatsApp' },
    viewing: { icon: Calendar, text: 'Schedule Viewing' },
    callback: { icon: Phone, text: 'Request Callback' }
  };

  const { icon: Icon, text } = buttonContent[variant];

  return (
    <Button
      onClick={handleClick}
      className={`bg-green-500 hover:bg-green-600 text-white flex items-center gap-2 ${className}`}
    >
      <Icon className="w-4 h-4" />
      {text}
    </Button>
  );
}

interface QuickActionWhatsAppProps {
  className?: string;
}

export function QuickActionWhatsApp({ className = '' }: QuickActionWhatsAppProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const whatsapp = useWhatsApp();

  const quickActions = [
    {
      icon: MessageCircle,
      label: 'General Inquiry',
      message: WhatsAppService.templates.generalInquiry,
      color: 'bg-blue-500 hover:bg-blue-600'
    },
    {
      icon: DollarSign,
      label: 'Price Information',
      message: WhatsAppService.templates.priceInquiry,
      color: 'bg-green-500 hover:bg-green-600'
    },
    {
      icon: Calendar,
      label: 'Schedule Viewing',
      message: WhatsAppService.templates.viewingRequest,
      color: 'bg-purple-500 hover:bg-purple-600'
    },
    {
      icon: Phone,
      label: 'Request Callback',
      message: WhatsAppService.templates.callbackRequest,
      color: 'bg-orange-500 hover:bg-orange-600'
    },
    {
      icon: FileText,
      label: 'Documentation',
      message: WhatsAppService.templates.documentation,
      color: 'bg-indigo-500 hover:bg-indigo-600'
    },
    {
      icon: MapPin,
      label: 'Area Information',
      message: WhatsAppService.templates.areaInfo,
      color: 'bg-red-500 hover:bg-red-600'
    }
  ];

  return (
    <div className={`relative ${className}`}>
      {/* Expanded Actions */}
      {isExpanded && (
        <div className="absolute bottom-16 right-0 flex flex-col gap-2 animate-in slide-in-from-bottom-2">
          {quickActions.map((action, index) => {
            const Icon = action.icon;
            return (
              <button
                key={index}
                onClick={() => {
                  whatsapp.sendCustomMessage(action.message);
                  setIsExpanded(false);
                }}
                className={`flex items-center gap-3 px-4 py-3 rounded-lg text-white shadow-lg transition-all duration-200 hover:scale-105 ${action.color}`}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <Icon className="w-5 h-5" />
                <span className="text-sm font-medium whitespace-nowrap">{action.label}</span>
              </button>
            );
          })}
        </div>
      )}

      {/* Main WhatsApp Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-2xl transition-all duration-200 hover:scale-110 ${
          isExpanded ? 'rotate-45' : ''
        }`}
      >
        <MessageCircle className="w-6 h-6" />
      </button>
    </div>
  );
}

// Floating WhatsApp widget for bottom corner
export function FloatingWhatsApp({ className = '' }: { className?: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const whatsapp = useWhatsApp();

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      {/* Chat bubble */}
      {isOpen && (
        <div className="absolute bottom-16 right-0 bg-white rounded-lg shadow-2xl border p-4 w-80 animate-in slide-in-from-bottom-2">
          <div className="flex items-center gap-3 mb-3">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
              <MessageCircle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Property Trendz</h3>
              <p className="text-sm text-gray-600">How can we help you?</p>
            </div>
          </div>
          
          <div className="space-y-2">
            <button
              onClick={() => {
                whatsapp.sendCustomMessage(WhatsAppService.templates.generalInquiry);
                setIsOpen(false);
              }}
              className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium text-gray-900">General Inquiry</div>
              <div className="text-sm text-gray-600">Ask about our services</div>
            </button>
            
            <button
              onClick={() => {
                whatsapp.sendCustomMessage(WhatsAppService.templates.viewingRequest);
                setIsOpen(false);
              }}
              className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium text-gray-900">Schedule Viewing</div>
              <div className="text-sm text-gray-600">Book a property visit</div>
            </button>
            
            <button
              onClick={() => {
                whatsapp.sendCustomMessage(WhatsAppService.templates.callbackRequest);
                setIsOpen(false);
              }}
              className="w-full text-left p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="font-medium text-gray-900">Request Callback</div>
              <div className="text-sm text-gray-600">We'll call you back</div>
            </button>
          </div>
        </div>
      )}

      {/* Floating button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-2xl flex items-center justify-center transition-all duration-200 hover:scale-110 animate-bounce"
      >
        <MessageCircle className="w-6 h-6" />
      </button>
    </div>
  );
}