import React, { useEffect, useRef, useState, useMemo } from 'react';

/**
 * Debounce hook for performance optimization
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Throttle hook for performance optimization
 */
export function useThrottle<T>(value: T, delay: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef(Date.now());

  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= delay) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, delay - (Date.now() - lastRan.current));

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return throttledValue;
}

/**
 * Intersection Observer hook for lazy loading
 */
export function useIntersectionObserver(
  elementRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
): boolean {
  const [isIntersecting, setIsIntersecting] = useState(false);

  useEffect(() => {
    if (!elementRef.current) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(elementRef.current);

    return () => observer.disconnect();
  }, [elementRef, options]);

  return isIntersecting;
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static metrics = new Map<string, number>();

  static startTimer(name: string): void {
    this.metrics.set(name, performance.now());
  }

  static endTimer(name: string): number {
    const startTime = this.metrics.get(name);
    if (!startTime) {
      console.warn(`Timer "${name}" was not started`);
      return 0;
    }

    const duration = performance.now() - startTime;
    this.metrics.delete(name);
    
    // Log slow operations
    if (duration > 100) {
      console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  static measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    this.startTimer(name);
    return fn().finally(() => {
      this.endTimer(name);
    });
  }

  static measure<T>(name: string, fn: () => T): T {
    this.startTimer(name);
    try {
      return fn();
    } finally {
      this.endTimer(name);
    }
  }
}

/**
 * Image optimization utilities
 */
export function generateOptimizedImageUrl(
  src: string,
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'auto';
  } = {}
): string {
  if (!src) return '';

  // If it's already a Cloudinary URL, add transformations
  if (src.includes('cloudinary.com')) {
    const { width, height, quality = 80, format = 'auto' } = options;
    const transformations = [];

    if (width || height) {
      transformations.push(`c_fill`);
      if (width) transformations.push(`w_${width}`);
      if (height) transformations.push(`h_${height}`);
    }

    transformations.push(`q_${quality}`);
    transformations.push(`f_${format}`);

    const transformation = transformations.join(',');
    return src.replace('/upload/', `/upload/${transformation}/`);
  }

  // For other URLs, return as-is (Next.js Image component will handle optimization)
  return src;
}

/**
 * Virtual scrolling hook for large lists
 */
export function useVirtualScroll(
  items: any[],
  itemHeight: number,
  containerHeight: number
): {
  visibleItems: any[];
  startIndex: number;
  endIndex: number;
  totalHeight: number;
  offsetY: number;
} {
  const [scrollTop, setScrollTop] = useState(0);

  const visibleCount = Math.ceil(containerHeight / itemHeight);
  const startIndex = Math.floor(scrollTop / itemHeight);
  const endIndex = Math.min(startIndex + visibleCount, items.length - 1);
  const visibleItems = items.slice(startIndex, endIndex + 1);
  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  return {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
  };
}

/**
 * Memory usage monitoring
 */
export function useMemoryMonitor(): {
  usedMemory: number;
  totalMemory: number;
  memoryUsage: number;
} {
  const [memoryInfo, setMemoryInfo] = useState({
    usedMemory: 0,
    totalMemory: 0,
    memoryUsage: 0,
  });

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMemory = memory.usedJSHeapSize;
        const totalMemory = memory.totalJSHeapSize;
        const memoryUsage = (usedMemory / totalMemory) * 100;

        setMemoryInfo({
          usedMemory: Math.round(usedMemory / 1024 / 1024), // MB
          totalMemory: Math.round(totalMemory / 1024 / 1024), // MB
          memoryUsage: Math.round(memoryUsage),
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
}

/**
 * Component lazy loading helper
 */
export function createLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback: React.ComponentType = () => null
) {
  const LazyComponent = React.lazy(importFn);
  
  return function LazyWrapper(props: React.ComponentProps<T>) {
    const FallbackComponent = fallback;
    return (
      <React.Suspense fallback={<FallbackComponent />}>
        <LazyComponent {...props} />
      </React.Suspense>
    );
  };
}

/**
 * Optimized search function with caching
 */
export function createOptimizedSearch<T>(
  items: T[],
  searchFields: (keyof T)[],
  options: {
    caseSensitive?: boolean;
    matchFullWords?: boolean;
    cacheSize?: number;
  } = {}
) {
  const {
    caseSensitive = false,
    matchFullWords = false,
    cacheSize = 100,
  } = options;

  const cache = new Map<string, T[]>();

  return function search(query: string): T[] {
    if (!query.trim()) return items;

    // Check cache first
    const cacheKey = caseSensitive ? query : query.toLowerCase();
    if (cache.has(cacheKey)) {
      return cache.get(cacheKey)!;
    }

    const searchTerm = caseSensitive ? query : query.toLowerCase();
    
    const results = items.filter(item => {
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value !== 'string') return false;
        
        const fieldValue = caseSensitive ? value : value.toLowerCase();
        
        if (matchFullWords) {
          const words = fieldValue.split(/\s+/);
          return words.some(word => word === searchTerm);
        } else {
          return fieldValue.includes(searchTerm);
        }
      });
    });

    // Cache result (with size limit)
    if (cache.size >= cacheSize) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }
    cache.set(cacheKey, results);

    return results;
  };
}