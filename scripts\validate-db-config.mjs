#!/usr/bin/env node

/**
 * Database Configuration Validation Script
 * Validates that the database configuration is correct for the current environment
 */

import { createRequire } from 'module';
import path from 'path';
import { fileURLToPath } from 'url';

const require = createRequire(import.meta.url);
const dotenv = require('dotenv');

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
const nodeEnv = process.env.NODE_ENV || 'development';
console.log(`🔍 Validating database configuration for: ${nodeEnv}`);

if (nodeEnv === 'production') {
  dotenv.config({ path: path.join(process.cwd(), '.env.production') });
  console.log('📁 Loaded .env.production');
} else {
  dotenv.config({ path: path.join(process.cwd(), '.env.local') });
  console.log('📁 Loaded .env.local');
}

async function validateConfiguration() {
  try {
    // Import the database utilities
    const { validateDatabaseConfig, testDatabaseConnection, getDatabaseInfo } = await import('../lib/db/utils.js');
    
    console.log('\n🔧 Database Configuration Validation\n');
    
    // Get database info
    const dbInfo = getDatabaseInfo();
    console.log('Environment:', dbInfo.environment);
    console.log('Host:', dbInfo.host);
    console.log('Port:', dbInfo.port);
    console.log('Database:', dbInfo.database);
    console.log('Username:', dbInfo.username);
    console.log('Docker:', dbInfo.isDocker ? 'Yes' : 'No');
    console.log('URL:', dbInfo.url);
    
    // Validate configuration
    const validation = validateDatabaseConfig();
    
    console.log('\n📋 Configuration Validation:');
    if (validation.isValid) {
      console.log('✅ Configuration is valid');
    } else {
      console.log('❌ Configuration has errors:');
      validation.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (validation.warnings.length > 0) {
      console.log('\n⚠️  Warnings:');
      validation.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    // Test database connection
    console.log('\n🔌 Testing Database Connection...');
    const connectionTest = await testDatabaseConnection();
    
    if (connectionTest.success) {
      console.log('✅ Database connection successful');
      
      // Additional environment-specific checks
      if (dbInfo.environment === 'development' && !dbInfo.isDocker) {
        console.log('\n⚠️  Development Environment Notice:');
        console.log('You are in development but not using Docker database.');
        console.log('Expected: localhost:5432 (Docker)');
        console.log(`Actual: ${dbInfo.host}:${dbInfo.port}`);
      }
      
      if (dbInfo.environment === 'production' && dbInfo.isDocker) {
        console.log('\n⚠️  Production Environment Notice:');
        console.log('You are in production but using Docker database URL.');
        console.log('Expected: Production server database');
        console.log(`Actual: ${dbInfo.host}:${dbInfo.port}`);
      }
      
    } else {
      console.log('❌ Database connection failed:', connectionTest.error);
      
      // Provide troubleshooting suggestions
      console.log('\n🔧 Troubleshooting Suggestions:');
      
      if (dbInfo.isDocker) {
        console.log('- Ensure Docker PostgreSQL container is running');
        console.log('- Check: docker ps | grep postgres');
        console.log('- Start container: docker-compose up -d postgres');
      } else {
        console.log('- Verify production database server is accessible');
        console.log('- Check network connectivity and firewall rules');
        console.log('- Verify database credentials');
      }
      
      console.log('- Verify DATABASE_URL format');
      console.log('- Check environment file is loaded correctly');
    }
    
    console.log('\n📊 Environment Summary:');
    console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
    console.log(`Database Environment: ${dbInfo.environment}`);
    console.log(`Expected for ${nodeEnv}:`, nodeEnv === 'production' ? 'Production server' : 'Docker container');
    console.log(`Actual: ${dbInfo.isDocker ? 'Docker container' : 'Production server'}`);
    console.log(`Match: ${(nodeEnv === 'production' && !dbInfo.isDocker) || (nodeEnv !== 'production' && dbInfo.isDocker) ? '✅' : '❌'}`);
    
  } catch (error) {
    console.error('\n❌ Validation failed:', error.message);
    console.error('\nThis might indicate:');
    console.error('- Missing environment file');
    console.error('- Invalid database configuration');
    console.error('- Module import issues');
    process.exit(1);
  }
}

// Run validation
validateConfiguration().catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
