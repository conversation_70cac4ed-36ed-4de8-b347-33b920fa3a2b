import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { enquiries, properties } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET - Get single enquiry
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const enquiry = await db
      .select({
        id: enquiries.id,
        propertyId: enquiries.propertyId,
        userId: enquiries.userId,
        name: enquiries.name,
        email: enquiries.email,
        phone: enquiries.phone,
        message: enquiries.message,
        enquiryType: enquiries.enquiryType,
        budget: enquiries.budget,
        status: enquiries.status,
        source: enquiries.source,
        assignedTo: enquiries.assignedTo,
        followUpDate: enquiries.followUpDate,
        notes: enquiries.notes,
        createdAt: enquiries.createdAt,
        updatedAt: enquiries.updatedAt,
        // Property details
        propertyTitle: properties.title,
        propertyArea: properties.area,
        propertyCity: properties.city,
        propertyPrice: properties.price,
        propertyType: properties.propertyType,
      })
      .from(enquiries)
      .leftJoin(properties, eq(enquiries.propertyId, properties.id))
      .where(eq(enquiries.id, params.id))
      .limit(1);

    if (!enquiry[0]) {
      return NextResponse.json(
        { message: 'Enquiry not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      enquiry: enquiry[0],
    });
  } catch (error) {
    console.error('Error fetching enquiry:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update enquiry
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const enquiryData = await request.json();
    const { 
      status, 
      assignedTo, 
      followUpDate, 
      notes, 
      enquiryType,
      budget 
    } = enquiryData;

    // Check if enquiry exists
    const existingEnquiry = await db
      .select()
      .from(enquiries)
      .where(eq(enquiries.id, params.id))
      .limit(1);

    if (!existingEnquiry[0]) {
      return NextResponse.json(
        { message: 'Enquiry not found' },
        { status: 404 }
      );
    }

    // Update enquiry
    const updatedEnquiry = await db
      .update(enquiries)
      .set({
        status: status || existingEnquiry[0].status,
        assignedTo: assignedTo !== undefined ? assignedTo : existingEnquiry[0].assignedTo,
        followUpDate: followUpDate ? new Date(followUpDate) : existingEnquiry[0].followUpDate,
        notes: notes !== undefined ? notes : existingEnquiry[0].notes,
        enquiryType: enquiryType || existingEnquiry[0].enquiryType,
        budget: budget !== undefined ? (budget ? String(budget) : null) : existingEnquiry[0].budget,
        updatedAt: new Date(),
      })
      .where(eq(enquiries.id, params.id))
      .returning();

    return NextResponse.json({
      message: 'Enquiry updated successfully',
      enquiry: updatedEnquiry[0],
    });
  } catch (error) {
    console.error('Error updating enquiry:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete enquiry
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if enquiry exists
    const existingEnquiry = await db
      .select()
      .from(enquiries)
      .where(eq(enquiries.id, params.id))
      .limit(1);

    if (!existingEnquiry[0]) {
      return NextResponse.json(
        { message: 'Enquiry not found' },
        { status: 404 }
      );
    }

    await db
      .delete(enquiries)
      .where(eq(enquiries.id, params.id));

    return NextResponse.json({
      message: 'Enquiry deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting enquiry:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
