import { db } from '@/lib/db/config';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

/**
 * <PERSON>ript to normalize existing email addresses in the database to lowercase
 * This ensures consistency with the authentication system
 */
async function normalizeEmails() {
  try {
    console.log('🔄 Starting email normalization...');

    // Get all users
    const allUsers = await db.select().from(users);
    console.log(`📊 Found ${allUsers.length} users to process`);

    let updatedCount = 0;

    for (const user of allUsers) {
      const normalizedEmail = user.email.toLowerCase().trim();
      
      // Only update if the email is different
      if (user.email !== normalizedEmail) {
        console.log(`📧 Normalizing: ${user.email} → ${normalizedEmail}`);
        
        await db
          .update(users)
          .set({ 
            email: normalizedEmail,
            updatedAt: new Date()
          })
          .where(eq(users.id, user.id));
        
        updatedCount++;
      }
    }

    console.log(`✅ Email normalization complete!`);
    console.log(`📈 Updated ${updatedCount} out of ${allUsers.length} users`);
    
    if (updatedCount === 0) {
      console.log('🎉 All emails were already normalized!');
    }

  } catch (error) {
    console.error('❌ Error normalizing emails:', error);
    throw error;
  }
}

// Run the script if called directly
if (require.main === module) {
  normalizeEmails()
    .then(() => {
      console.log('🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}

export { normalizeEmails };
