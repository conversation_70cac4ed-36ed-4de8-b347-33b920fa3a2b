/**
 * Database utility functions and helpers
 * Provides additional database management functionality
 */

import { db, databaseConfig, postgresClient } from './config';

/**
 * Test database connection
 */
export async function testDatabaseConnection(): Promise<{
  success: boolean;
  error?: string;
  config: typeof databaseConfig;
}> {
  try {
    await db.execute('SELECT 1');
    return {
      success: true,
      config: databaseConfig,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      config: databaseConfig,
    };
  }
}

/**
 * Get database connection info for debugging
 */
export function getDatabaseInfo() {
  return {
    environment: databaseConfig.environment,
    host: databaseConfig.host,
    port: databaseConfig.port,
    database: databaseConfig.database,
    username: databaseConfig.username,
    isDocker: databaseConfig.isDocker,
    url: databaseConfig.url.replace(/:[^:@]*@/, ':***@'), // Hide password
  };
}

/**
 * Check if database is available
 */
export async function isDatabaseAvailable(): Promise<boolean> {
  try {
    await db.execute('SELECT 1');
    return true;
  } catch {
    return false;
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats() {
  try {
    const [tablesResult] = await db.execute(`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as inserts,
        n_tup_upd as updates,
        n_tup_del as deletes
      FROM pg_stat_user_tables 
      ORDER BY schemaname, tablename
    `);

    const [sizeResult] = await db.execute(`
      SELECT 
        pg_size_pretty(pg_database_size(current_database())) as database_size
    `);

    return {
      tables: tablesResult,
      databaseSize: sizeResult[0]?.database_size || 'Unknown',
      environment: databaseConfig.environment,
    };
  } catch (error) {
    throw new Error(`Failed to get database stats: ${error}`);
  }
}

/**
 * Close database connection (for cleanup)
 */
export async function closeDatabaseConnection(): Promise<void> {
  try {
    await postgresClient.end();
  } catch (error) {
    console.warn('Error closing database connection:', error);
  }
}

/**
 * Validate database configuration
 */
export function validateDatabaseConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check required configuration
  if (!databaseConfig.url) {
    errors.push('Database URL is not configured');
  }

  if (!databaseConfig.host) {
    errors.push('Database host is not configured');
  }

  if (!databaseConfig.database) {
    errors.push('Database name is not configured');
  }

  if (!databaseConfig.username) {
    errors.push('Database username is not configured');
  }

  // Environment-specific warnings
  if (databaseConfig.environment === 'production' && databaseConfig.isDocker) {
    warnings.push('Production environment detected but using Docker database URL');
  }

  if (databaseConfig.environment === 'development' && !databaseConfig.isDocker) {
    warnings.push('Development environment detected but not using Docker database URL');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Export the main database instance and config for convenience
export { db, databaseConfig } from './config';
