import { NextRequest, NextResponse } from 'next/server';
import { Server as NetServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import { ChatService } from '@/lib/socket/server';

let io: SocketIOServer;
let chatService: ChatService;

export async function GET(req: NextRequest) {
  if (!io) {
    console.log('Initializing Socket.io server...');
    
    // Create HTTP server instance
    const httpServer = new NetServer();
    
    // Initialize Socket.io
    io = new SocketIOServer(httpServer, {
      path: '/api/socket',
      cors: {
        origin: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3004',
        methods: ['GET', 'POST'],
        credentials: true,
      },
      transports: ['websocket', 'polling'],
    });
    
    // Initialize chat service
    chatService = new ChatService(io);
    
    console.log('Socket.io server initialized');
  }

  return NextResponse.json({ 
    success: true,
    message: 'Socket.io server is running',
    path: '/api/socket'
  });
}

export async function POST(req: NextRequest) {
  const body = await req.json();
  const { action, data } = body;

  if (!chatService) {
    return NextResponse.json({ error: 'Chat service not initialized' }, { status: 500 });
  }

  try {
    switch (action) {
      case 'create-room':
        const roomId = await chatService.createChatRoom(data);
        return NextResponse.json({ roomId });
      
      case 'get-messages':
        const messages = await chatService.getRoomMessages(data.roomId);
        return NextResponse.json({ messages });
      
      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Socket API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}