'use client';

import { useEffect, useRef, useState } from 'react';
import { MapP<PERSON>, Filter, Eye } from 'lucide-react';
import Link from 'next/link';

interface Property {
  id: string;
  slug: string;
  title: string;
  price: number;
  latitude: number;
  longitude: number;
  propertyType: string;
  bedrooms?: number;
  bathrooms?: number;
  images?: string[];
}

interface SearchMapProps {
  properties: Property[];
  className?: string;
  height?: string;
  onPropertyClick?: (property: Property) => void;
  showPropertyCount?: boolean;
  fitBounds?: boolean;
  zoom?: number;
  center?: { lat: number; lng: number };
}

export function SearchMap({
  properties,
  className = '',
  height = '500px',
  onPropertyClick,
  showPropertyCount = true,
  fitBounds = true,
  zoom = 11,
  center = { lat: 28.6139, lng: 77.2090 } // Delhi center
}: SearchMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<any>(null);
  const [markers, setMarkers] = useState<any[]>([]);
  const [selectedProperty, setSelectedProperty] = useState<Property | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load Google Maps script
  useEffect(() => {
    if (window.google) {
      initializeMap();
      return;
    }

    const script = document.createElement('script');
    script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`;
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      initializeMap();
    };
    
    script.onerror = () => {
      setError('Failed to load Google Maps');
      setIsLoading(false);
    };

    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
      if (existingScript && existingScript.parentNode) {
        existingScript.parentNode.removeChild(existingScript);
      }
    };
  }, []);

  // Update markers when properties change
  useEffect(() => {
    if (map && window.google) {
      updateMarkers();
    }
  }, [properties, map]);

  const initializeMap = () => {
    if (!mapRef.current || !window.google) return;

    try {
      const mapOptions = {
        center: center,
        zoom: zoom,
        mapTypeId: window.google.maps.MapTypeId.ROADMAP,
        zoomControl: true,
        streetViewControl: false,
        fullscreenControl: true,
        mapTypeControl: true,
        gestureHandling: 'cooperative',
        styles: [
          {
            featureType: 'poi.business',
            stylers: [{ visibility: 'off' }]
          },
          {
            featureType: 'transit.station',
            stylers: [{ visibility: 'off' }]
          }
        ]
      };

      const newMap = new window.google.maps.Map(mapRef.current, mapOptions);
      setMap(newMap);
      setIsLoading(false);
    } catch (err) {
      setError('Failed to initialize map');
      setIsLoading(false);
    }
  };

  const updateMarkers = () => {
    if (!map || !window.google) return;

    // Clear existing markers
    markers.forEach(marker => marker.setMap(null));
    setMarkers([]);

    if (properties.length === 0) return;

    const bounds = new window.google.maps.LatLngBounds();
    const newMarkers: any[] = [];

    properties.forEach((property) => {
      if (!property.latitude || !property.longitude) return;

      const position = { lat: property.latitude, lng: property.longitude };
      
      // Create price marker
      const priceText = formatPrice(property.price);
      const marker = new window.google.maps.Marker({
        position: position,
        map: map,
        title: property.title,
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(createMarkerSVG(priceText)),
          scaledSize: new window.google.maps.Size(100, 40),
          anchor: new window.google.maps.Point(50, 40)
        }
      });

      // Create info window
      const infoWindow = new window.google.maps.InfoWindow({
        content: createInfoWindowContent(property)
      });

      // Add click listener
      marker.addListener('click', () => {
        // Close any open info windows
        markers.forEach(m => m.infoWindow?.close());
        
        infoWindow.open(map, marker);
        setSelectedProperty(property);
        
        if (onPropertyClick) {
          onPropertyClick(property);
        }
      });

      marker.infoWindow = infoWindow;
      newMarkers.push(marker);
      bounds.extend(position);
    });

    setMarkers(newMarkers);

    // Fit bounds if enabled and we have properties
    if (fitBounds && properties.length > 0) {
      if (properties.length === 1) {
        map.setCenter(bounds.getCenter());
        map.setZoom(15);
      } else {
        map.fitBounds(bounds);
        // Add some padding
        const listener = window.google.maps.event.addListener(map, 'bounds_changed', () => {
          if (map.getZoom() > 16) {
            map.setZoom(16);
          }
          window.google.maps.event.removeListener(listener);
        });
      }
    }
  };

  const formatPrice = (price: number): string => {
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)}Cr`;
    } else if (price >= 100000) {
      return `₹${(price / 100000).toFixed(0)}L`;
    } else if (price >= 1000) {
      return `₹${(price / 1000).toFixed(0)}K`;
    }
    return `₹${price}`;
  };

  const createMarkerSVG = (priceText: string): string => {
    return `
      <svg width="100" height="40" viewBox="0 0 100 40" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
            <dropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000" flood-opacity="0.3"/>
          </filter>
        </defs>
        <rect x="5" y="5" width="90" height="25" rx="12" fill="#3B82F6" filter="url(#shadow)"/>
        <polygon points="50,30 45,40 55,40" fill="#3B82F6"/>
        <text x="50" y="20" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">${priceText}</text>
      </svg>
    `;
  };

  const createInfoWindowContent = (property: Property): string => {
    const image = property.images?.[0] || '/placeholder-property.svg';
    const bedrooms = property.bedrooms ? `${property.bedrooms} BHK` : '';
    const bathrooms = property.bathrooms ? `${property.bathrooms} Bath` : '';

    return `
      <div class="max-w-xs">
        <div class="relative h-32 mb-3 rounded-lg overflow-hidden">
          <img src="${image}" alt="${property.title}" class="w-full h-full object-cover" onerror="this.src='/placeholder-property.svg'"/>
        </div>
        <h3 class="font-semibold text-gray-900 mb-1 line-clamp-2">${property.title}</h3>
        <p class="text-lg font-bold text-blue-600 mb-2">${formatPrice(property.price)}</p>
        <div class="flex items-center gap-3 text-sm text-gray-600 mb-3">
          ${bedrooms ? `<span>${bedrooms}</span>` : ''}
          ${bathrooms ? `<span>${bathrooms}</span>` : ''}
          <span class="capitalize">${property.propertyType}</span>
        </div>
        <div class="flex gap-2">
          <a href="/properties/${property.slug}" class="flex-1 bg-blue-500 text-white text-center py-2 px-3 rounded text-sm hover:bg-blue-600 transition-colors">
            View Details
          </a>
        </div>
      </div>
    `;
  };

  if (error) {
    return (
      <div 
        className={`bg-gray-100 flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="text-center text-gray-500">
          <MapPin className="w-8 h-8 mx-auto mb-2" />
          <p className="text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Map Container */}
      <div
        ref={mapRef}
        className="w-full rounded-lg overflow-hidden"
        style={{ height }}
      />

      {/* Loading Overlay */}
      {isLoading && (
        <div 
          className="absolute inset-0 bg-gray-100 flex items-center justify-center rounded-lg"
          style={{ height }}
        >
          <div className="text-center text-gray-500">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm">Loading map...</p>
          </div>
        </div>
      )}

      {/* Property Count */}
      {showPropertyCount && !isLoading && (
        <div className="absolute top-4 left-4 bg-white shadow-lg rounded-lg px-3 py-2">
          <p className="text-sm font-medium text-gray-900">
            {properties.length} {properties.length === 1 ? 'Property' : 'Properties'}
          </p>
        </div>
      )}

      {/* Map Controls */}
      <div className="absolute top-4 right-4 flex flex-col gap-2">
        <button
          onClick={() => updateMarkers()}
          className="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50 transition-colors"
          title="Refresh Map"
        >
          <Filter className="w-5 h-5 text-gray-700" />
        </button>
      </div>
    </div>
  );
}

// Fallback component when no properties or map fails
export function SearchMapFallback({ 
  className = '', 
  height = '500px',
  message = 'No properties to display on map'
}: { 
  className?: string; 
  height?: string;
  message?: string;
}) {
  return (
    <div 
      className={`bg-gray-100 flex items-center justify-center rounded-lg ${className}`}
      style={{ height }}
    >
      <div className="text-center text-gray-500">
        <MapPin className="w-8 h-8 mx-auto mb-2" />
        <p className="text-sm">{message}</p>
      </div>
    </div>
  );
}