import { z } from 'zod';

// Common validation schemas
export const emailSchema = z
  .string()
  .min(1, 'Email is required')
  .email('Invalid email format')
  .max(255, 'Email too long');

export const phoneSchema = z
  .string()
  .min(1, 'Phone number is required')
  .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format')
  .max(20, 'Phone number too long');

export const nameSchema = z
  .string()
  .min(1, 'Name is required')
  .max(100, 'Name too long')
  .regex(/^[a-zA-Z\s\.\-']+$/, 'Name contains invalid characters');

export const passwordSchema = z
  .string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password too long')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number');

export const slugSchema = z
  .string()
  .min(1, 'Slug is required')
  .max(255, 'Slug too long')
  .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format');

// Property validation schemas
export const propertySchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(5000, 'Description too long'),
  slug: slugSchema,
  price: z.number().positive('Price must be positive').max(10000000000, 'Price too high'),
  pricePerSqft: z.number().positive().optional(),
  
  // Location
  area: z.string().min(1, 'Area is required').max(100, 'Area name too long'),
  city: z.string().min(1, 'City is required').max(50, 'City name too long'),
  state: z.string().min(1, 'State is required').max(50, 'State name too long'),
  address: z.string().min(1, 'Address is required').max(500, 'Address too long'),
  pincode: z.string().regex(/^[0-9]{6}$/, 'Invalid pincode').optional(),
  latitude: z.number().min(-90).max(90).optional(),
  longitude: z.number().min(-180).max(180).optional(),
  
  // Specifications
  bedrooms: z.number().int().min(0).max(20).optional(),
  bathrooms: z.number().int().min(0).max(20).optional(),
  totalArea: z.number().positive('Total area must be positive').max(100000).optional(),
  carpetArea: z.number().positive().max(100000).optional(),
  builtUpArea: z.number().positive().max(100000).optional(),
  floors: z.number().int().min(0).max(200).optional(),
  totalFloors: z.number().int().min(0).max(200).optional(),
  parking: z.number().int().min(0).max(20).default(0),
  balconies: z.number().int().min(0).max(20).default(0),
  
  // Property details
  propertyType: z.enum(['apartment', 'house', 'villa', 'plot', 'commercial', 'office', 'shop', 'builderfloor']),
  subType: z.string().max(50).optional(),
  listingType: z.enum(['sale', 'rent']),
  status: z.enum(['available', 'sold', 'rented', 'under-negotiation']).default('available'),
  furnished: z.enum(['furnished', 'semi-furnished', 'unfurnished']).default('unfurnished'),
  ageOfProperty: z.number().int().min(0).max(100).optional(),
  facing: z.enum(['north', 'south', 'east', 'west', 'north-east', 'north-west', 'south-east', 'south-west']).optional(),
  
  // Arrays
  amenities: z.array(z.string().max(50)).default([]),
  features: z.array(z.string().max(100)).default([]),
  images: z.array(z.string().url()).default([]),
  videos: z.array(z.string().url()).default([]),
  
  // Contact
  contactName: z.string().max(100).optional(),
  contactPhone: phoneSchema.optional(),
  contactEmail: emailSchema.optional(),
  contactWhatsapp: phoneSchema.optional(),
  
  // Flags
  featured: z.boolean().default(false),
  verified: z.boolean().default(false),
  published: z.boolean().default(true),
  
  // SEO
  metaTitle: z.string().max(70).optional(),
  metaDescription: z.string().max(160).optional(),
  metaKeywords: z.string().max(255).optional(),
});

// User validation schemas
export const userRegistrationSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: passwordSchema,
  phone: phoneSchema.optional(),
});

export const userLoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
});

export const userUpdateSchema = z.object({
  name: nameSchema.optional(),
  phone: phoneSchema.optional(),
  image: z.string().url().optional(),
});

// Enquiry validation schemas
export const enquirySchema = z.object({
  propertyId: z.string().uuid('Invalid property ID'),
  name: nameSchema,
  email: emailSchema,
  phone: phoneSchema,
  message: z.string().min(1, 'Message is required').max(1000, 'Message too long'),
  enquiryType: z.enum(['general', 'viewing', 'price', 'loan', 'property_interest']).default('general'),
  budget: z.number().positive().optional(),
});

// Search validation schemas
export const searchFiltersSchema = z.object({
  search: z.string().max(100).optional(),
  location: z.string().max(100).optional(),
  propertyType: z.string().max(50).optional(),
  listingType: z.enum(['sale', 'rent']).optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  bedrooms: z.number().int().min(0).max(20).optional(),
  bathrooms: z.number().int().min(0).max(20).optional(),
  minArea: z.number().min(0).optional(),
  maxArea: z.number().min(0).optional(),
  furnished: z.enum(['furnished', 'semi-furnished', 'unfurnished']).optional(),
  ageOfProperty: z.string().optional(),
  amenities: z.array(z.string()).optional(),
  sortBy: z.enum(['createdAt', 'price', 'totalArea', 'views']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(12),
});

// Admin validation schemas
export const adminSettingsSchema = z.object({
  siteName: z.string().min(1).max(100),
  siteDescription: z.string().max(500),
  contactEmail: emailSchema,
  contactPhone: phoneSchema,
  contactWhatsapp: phoneSchema.optional(),
  address: z.string().min(1).max(500),
  socialLinks: z.object({
    facebook: z.string().url().optional(),
    twitter: z.string().url().optional(),
    instagram: z.string().url().optional(),
    linkedin: z.string().url().optional(),
    youtube: z.string().url().optional(),
  }).optional(),
  seoSettings: z.object({
    metaTitle: z.string().max(70).optional(),
    metaDescription: z.string().max(160).optional(),
    metaKeywords: z.string().max(255).optional(),
    googleAnalyticsId: z.string().optional(),
    googleTagManagerId: z.string().optional(),
  }).optional(),
});

// File upload validation
export const fileUploadSchema = z.object({
  file: z.object({
    name: z.string().min(1, 'File name is required'),
    size: z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
    type: z.string().refine(
      (type) => ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(type),
      'Only JPEG, PNG, and WebP images are allowed'
    ),
  }),
  category: z.enum(['property', 'user', 'document']).default('property'),
});

// Comment/Review validation
export const commentSchema = z.object({
  propertyId: z.string().uuid(),
  content: z.string().min(1, 'Comment is required').max(1000, 'Comment too long'),
  rating: z.number().int().min(1).max(5).optional(),
});

// Type exports
export type PropertyInput = z.infer<typeof propertySchema>;
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
export type UserLogin = z.infer<typeof userLoginSchema>;
export type UserUpdate = z.infer<typeof userUpdateSchema>;
export type EnquiryInput = z.infer<typeof enquirySchema>;
export type SearchFilters = z.infer<typeof searchFiltersSchema>;
export type AdminSettings = z.infer<typeof adminSettingsSchema>;
export type FileUpload = z.infer<typeof fileUploadSchema>;
export type CommentInput = z.infer<typeof commentSchema>;