'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Home, 
  Users, 
  Building, 
  MessageSquare, 
  TrendingUp, 
  Eye,
  DollarSign,
  Calendar,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Database,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from 'react-hot-toast';
import { useRouter } from 'next/navigation';
import { UsersManagementTab } from '@/components/admin/UsersManagementTab';
import { EnquiryManagementTab } from '@/components/admin/EnquiryManagementTab';

interface DashboardStats {
  totalProperties: number;
  totalUsers: number;
  totalEnquiries: number;
  totalViews: number;
  recentProperties: any[];
  recentEnquiries: any[];
}

export function AdminDashboard() {
  const router = useRouter();
  const [stats, setStats] = useState<DashboardStats>({
    totalProperties: 0,
    totalUsers: 0,
    totalEnquiries: 0,
    totalViews: 0,
    recentProperties: [],
    recentEnquiries: [],
  });
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [seeding, setSeeding] = useState(false);
  const [seedingResult, setSeedingResult] = useState<any>(null);
  const [deletingProperty, setDeletingProperty] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const seedProperties = async () => {
    try {
      setSeeding(true);
      setSeedingResult(null);

      const response = await fetch('/api/admin/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ force: false }),
      });

      const result = await response.json();

      if (response.ok) {
        setSeedingResult({
          success: true,
          ...result.data,
        });
        // Refresh dashboard data after seeding
        await fetchDashboardData();
      } else {
        setSeedingResult({
          success: false,
          error: result.error || 'Seeding failed',
        });
      }
    } catch (error) {
      console.error('Seeding error:', error);
      setSeedingResult({
        success: false,
        error: 'Network error occurred',
      });
    } finally {
      setSeeding(false);
    }
  };

  const fetchDashboardData = async () => {
    try {
      const response = await fetch('/api/admin/dashboard');
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleEditProperty = (propertyId: string) => {
    router.push(`/admin/properties/edit/${propertyId}`);
  };

  const handleDeleteProperty = async (propertyId: string) => {
    if (!confirm('Are you sure you want to delete this property? This action cannot be undone.')) {
      return;
    }

    setDeletingProperty(propertyId);
    try {
      const response = await fetch(`/api/admin/properties/${propertyId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        toast.success('Property deleted successfully');
        // Refresh the dashboard data
        fetchDashboardData();
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to delete property');
      }
    } catch (error) {
      console.error('Delete property error:', error);
      toast.error('An error occurred while deleting the property');
    } finally {
      setDeletingProperty(null);
    }
  };

  const StatCard = ({ icon: Icon, title, value, trend, color }: any) => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-6 rounded-lg shadow-card hover:shadow-card-hover transition-all duration-300"
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {trend && (
            <p className={`text-sm ${trend > 0 ? 'text-green-600' : 'text-red-600'}`}>
              {trend > 0 ? '+' : ''}{trend}% from last month
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          <Icon className="w-6 h-6 text-white" />
        </div>
      </div>
    </motion.div>
  );

  const tabs = [
    { id: 'overview', label: 'Overview', icon: Home },
    { id: 'properties', label: 'Properties', icon: Building },
    { id: 'users', label: 'Users', icon: Users },
    { id: 'enquiries', label: 'Enquiries', icon: MessageSquare },
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg">
        <div className="p-6">
          <h1 className="text-xl font-bold text-gray-900">Admin Panel</h1>
          <p className="text-sm text-gray-600">Property Trendz</p>
        </div>
        
        <nav className="mt-6">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`w-full flex items-center px-6 py-3 text-left transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary-50 text-primary-600 border-r-2 border-primary-600'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-5 h-5 mr-3" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-1 p-8">
        {activeTab === 'overview' && (
          <div>
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
              <p className="text-gray-600">Welcome back! Here's what's happening with your properties.</p>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <StatCard
                icon={Building}
                title="Total Properties"
                value={stats.totalProperties}
                trend={12}
                color="bg-blue-500"
              />
              <StatCard
                icon={Users}
                title="Total Users"
                value={stats.totalUsers}
                trend={8}
                color="bg-green-500"
              />
              <StatCard
                icon={MessageSquare}
                title="Total Enquiries"
                value={stats.totalEnquiries}
                trend={-3}
                color="bg-purple-500"
              />
              <StatCard
                icon={Eye}
                title="Total Views"
                value={stats.totalViews}
                trend={15}
                color="bg-orange-500"
              />
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Recent Properties */}
              <div className="bg-white p-6 rounded-lg shadow-card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Properties</h3>
                  <Button size="sm" onClick={() => setActiveTab('properties')}>
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {stats.recentProperties.length > 0 ? (
                    stats.recentProperties.slice(0, 5).map((property: any) => (
                      <div key={property.id} className="flex items-center space-x-4">
                        <img
                          src={property.images?.[0] || '/placeholder-property.jpg'}
                          alt={property.title}
                          className="w-12 h-12 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{property.title}</p>
                          <p className="text-sm text-gray-600">{property.location}</p>
                        </div>
                        <span className="text-sm font-medium text-green-600">
                          ₹{property.price?.toLocaleString()}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No properties yet</p>
                  )}
                </div>
              </div>

              {/* Recent Enquiries */}
              <div className="bg-white p-6 rounded-lg shadow-card">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Recent Enquiries</h3>
                  <Button size="sm" onClick={() => setActiveTab('enquiries')}>
                    View All
                  </Button>
                </div>
                <div className="space-y-4">
                  {stats.recentEnquiries.length > 0 ? (
                    stats.recentEnquiries.slice(0, 5).map((enquiry: any) => (
                      <div key={enquiry.id} className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center">
                          <MessageSquare className="w-5 h-5 text-primary-600" />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{enquiry.name}</p>
                          <p className="text-sm text-gray-600">{enquiry.property?.title}</p>
                        </div>
                        <span className="text-xs text-gray-500">
                          {new Date(enquiry.createdAt).toLocaleDateString()}
                        </span>
                      </div>
                    ))
                  ) : (
                    <p className="text-gray-500 text-center py-4">No enquiries yet</p>
                  )}
                </div>
              </div>
            </div>

            {/* Database Seeding Section */}
            <div className="bg-white p-6 rounded-lg shadow-card">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                    <Database className="w-5 h-5 mr-2" />
                    Database Management
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Add sample properties to test the system
                  </p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-gray-900">Seed Properties</h4>
                    <p className="text-sm text-gray-600">
                      Add 10 dummy properties with realistic data for testing
                    </p>
                  </div>
                  <Button
                    onClick={seedProperties}
                    disabled={seeding}
                    className="flex items-center"
                  >
                    {seeding ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Seeding...
                      </>
                    ) : (
                      <>
                        <Plus className="w-4 h-4 mr-2" />
                        Seed Properties
                      </>
                    )}
                  </Button>
                </div>

                {/* Seeding Result */}
                {seedingResult && (
                  <div className={`mt-4 p-4 rounded-lg ${
                    seedingResult.success 
                      ? 'bg-green-50 border border-green-200' 
                      : 'bg-red-50 border border-red-200'
                  }`}>
                    {seedingResult.success ? (
                      <div className="text-green-800">
                        <p className="font-medium">✅ Seeding Successful!</p>
                        <p className="text-sm mt-1">
                          Created {seedingResult.propertiesCreated} properties with {seedingResult.imagesCreated} images
                        </p>
                      </div>
                    ) : (
                      <div className="text-red-800">
                        <p className="font-medium">❌ Seeding Failed</p>
                        <p className="text-sm mt-1">{seedingResult.error}</p>
                      </div>
                    )}
                  </div>
                )}

                <div className="mt-4 text-xs text-gray-500">
                  <p>• Properties will include: Apartments, Villas, Offices, Studios</p>
                  <p>• Locations: Delhi, Gurgaon, Noida, Faridabad</p>
                  <p>• Price range: ₹28L to ₹7.5Cr with realistic market prices</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'properties' && (
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">Property Management</h2>
              <Button onClick={() => window.location.href = '/admin/properties/new'}>
                <Plus className="w-4 h-4 mr-2" />
                Add Property
              </Button>
            </div>

            {/* Property Filters */}
            <div className="bg-white p-4 rounded-lg shadow-card mb-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search properties..."
                    className="w-full"
                  />
                </div>
                <Button variant="outline">
                  <Filter className="w-4 h-4 mr-2" />
                  Filters
                </Button>
                <Button variant="outline">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>

            {/* Properties Table */}
            <div className="bg-white rounded-lg shadow-card overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Property
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Views
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {stats.recentProperties.length > 0 ? (
                      stats.recentProperties.map((property: any) => (
                        <tr key={property.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <img
                                src={property.images?.[0] || '/placeholder-property.jpg'}
                                alt={property.title}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                              <div className="ml-4">
                                <div className="text-sm font-medium text-gray-900">
                                  {property.title}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {property.propertyType}
                                </div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {property.area}, {property.city}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            ₹{property.price?.toLocaleString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              property.status === 'available' 
                                ? 'bg-green-100 text-green-800'
                                : property.status === 'sold'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {property.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {property.views || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleEditProperty(property.id)}
                                title="Edit Property"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDeleteProperty(property.id)}
                                disabled={deletingProperty === property.id}
                                title="Delete Property"
                                className="hover:bg-red-50 hover:border-red-200"
                              >
                                {deletingProperty === property.id ? (
                                  <Loader2 className="w-4 h-4 text-red-600 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4 text-red-600" />
                                )}
                              </Button>
                              <Button size="sm" variant="outline" title="More Options">
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                          No properties found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Users Management Tab */}
        {activeTab === 'users' && (
          <UsersManagementTab />
        )}

        {/* Enquiry Management Tab */}
        {activeTab === 'enquiries' && (
          <EnquiryManagementTab />
        )}
      </div>
    </div>
  );
}