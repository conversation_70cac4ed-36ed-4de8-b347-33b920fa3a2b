'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { MessageCircle, Send, Users, Clock, User, Home } from 'lucide-react';
import { motion } from 'framer-motion';
import { useSocket } from '@/lib/socket/client';

interface ChatRoom {
  id: string;
  propertyId?: string;
  userId: string;
  userName: string;
  userEmail: string;
  status: 'active' | 'closed' | 'pending';
  lastMessage?: string;
  lastMessageAt?: Date;
  unreadCount: number;
  createdAt: Date;
}

interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  senderName: string;
  senderRole: 'user' | 'admin' | 'agent';
  message: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  propertyId?: string;
  isRead: boolean;
}

export function AdminChatPanel() {
  const { data: session } = useSession();
  const [selectedRoom, setSelectedRoom] = useState<string | null>(null);
  const [activeRooms, setActiveRooms] = useState<ChatRoom[]>([]);
  const [message, setMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const {
    isConnected,
    messages,
    currentRoom,
    typingUsers,
    joinRoom,
    leaveRoom,
    sendMessage,
    joinAsAdmin,
    getActiveRooms,
    socket,
  } = useSocket();

  // Initialize admin connection
  useEffect(() => {
    if (session?.user?.role === 'admin' && socket && isConnected) {
      joinAsAdmin(session.user.id as string, session.user.name || 'Admin');
      getActiveRooms();
    }
  }, [session, socket, isConnected, joinAsAdmin, getActiveRooms]);

  // Listen for active rooms
  useEffect(() => {
    if (socket) {
      socket.on('active-rooms', (rooms: ChatRoom[]) => {
        setActiveRooms(rooms);
      });

      return () => {
        socket.off('active-rooms');
      };
    }
  }, [socket]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Join a room
  const handleJoinRoom = (roomId: string) => {
    if (currentRoom === roomId) return;
    
    if (currentRoom) {
      leaveRoom();
    }
    
    setSelectedRoom(roomId);
    joinRoom(roomId, session?.user?.id as string, session?.user?.name || 'Admin');
  };

  // Send message
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !selectedRoom || !session?.user) return;

    sendMessage({
      roomId: selectedRoom,
      senderId: session.user.id as string,
      senderName: session.user.name || 'Admin',
      senderRole: 'admin',
      message: message.trim(),
      type: 'text',
    });

    setMessage('');
  };

  // Format time
  const formatTime = (timestamp: Date | string) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDate = (timestamp: Date | string) => {
    const date = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else {
      return date.toLocaleDateString();
    }
  };

  if (session?.user?.role !== 'admin') {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Access denied. Admin privileges required.</p>
      </div>
    );
  }

  return (
    <div className="flex h-96 bg-white rounded-lg shadow-lg border overflow-hidden">
      {/* Rooms Sidebar */}
      <div className="w-1/3 border-r bg-gray-50">
        <div className="p-4 border-b bg-white">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">Active Chats</h3>
            <div className="flex items-center gap-1 text-sm text-gray-500">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              {isConnected ? 'Online' : 'Offline'}
            </div>
          </div>
          <button
            onClick={getActiveRooms}
            className="text-xs text-blue-600 hover:text-blue-800 mt-1"
          >
            Refresh
          </button>
        </div>

        <div className="overflow-y-auto max-h-80">
          {activeRooms.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">
              <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              No active chats
            </div>
          ) : (
            activeRooms.map((room) => (
              <motion.div
                key={room.id}
                whileHover={{ backgroundColor: '#f3f4f6' }}
                className={`p-3 border-b cursor-pointer transition-colors ${
                  selectedRoom === room.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                }`}
                onClick={() => handleJoinRoom(room.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-gray-400" />
                      <p className="font-medium text-sm text-gray-900 truncate">
                        {room.userName}
                      </p>
                    </div>
                    {room.propertyId && (
                      <div className="flex items-center gap-1 mt-1">
                        <Home className="w-3 h-3 text-gray-400" />
                        <p className="text-xs text-gray-500">Property inquiry</p>
                      </div>
                    )}
                    {room.lastMessage && (
                      <p className="text-xs text-gray-600 truncate mt-1">
                        {room.lastMessage}
                      </p>
                    )}
                  </div>
                  <div className="text-right ml-2">
                    {room.lastMessageAt && (
                      <p className="text-xs text-gray-400">
                        {formatTime(room.lastMessageAt)}
                      </p>
                    )}
                    {room.unreadCount > 0 && (
                      <div className="bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center mt-1 ml-auto">
                        {room.unreadCount}
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            ))
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedRoom ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b bg-white">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-gray-900">
                    {activeRooms.find(r => r.id === selectedRoom)?.userName || 'User'}
                  </h4>
                  <p className="text-sm text-gray-500">
                    {activeRooms.find(r => r.id === selectedRoom)?.userEmail}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-xs text-gray-400">
                    Room: #{selectedRoom.slice(-6)}
                  </p>
                  {typingUsers.length > 0 && (
                    <p className="text-xs text-blue-600">User is typing...</p>
                  )}
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 p-4 overflow-y-auto bg-gray-50">
              <div className="space-y-3">
                {messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex ${
                      msg.senderRole === 'admin' ? 'justify-end' : 'justify-start'
                    }`}
                  >
                    <div
                      className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                        msg.senderRole === 'admin'
                          ? 'bg-blue-500 text-white'
                          : 'bg-white border shadow-sm'
                      }`}
                    >
                      {msg.senderRole !== 'admin' && (
                        <div className="text-xs text-gray-500 mb-1">
                          {msg.senderName}
                        </div>
                      )}
                      <div>{msg.message}</div>
                      <div
                        className={`text-xs mt-1 ${
                          msg.senderRole === 'admin'
                            ? 'text-blue-100'
                            : 'text-gray-400'
                        }`}
                      >
                        {formatTime(msg.timestamp)}
                      </div>
                    </div>
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>
            </div>

            {/* Message Input */}
            <div className="p-4 border-t bg-white">
              <form onSubmit={handleSendMessage} className="flex gap-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Type your response..."
                  className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  disabled={!isConnected}
                />
                <button
                  type="submit"
                  disabled={!message.trim() || !isConnected}
                  className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white px-4 py-2 rounded-lg transition-colors flex items-center gap-2"
                >
                  <Send className="w-4 h-4" />
                  Send
                </button>
              </form>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center text-gray-500">
              <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-300" />
              <p className="text-lg font-medium">Select a chat to start</p>
              <p className="text-sm">Choose from active conversations on the left</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}