import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import { db } from '@/lib/db/config';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/admin/normalize-emails - Normalize all email addresses in the database (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      );
    }

    console.log('🔄 Starting email normalization...');

    // Get all users
    const allUsers = await db.select().from(users);
    console.log(`📊 Found ${allUsers.length} users to process`);

    let updatedCount = 0;
    const updates: Array<{ original: string; normalized: string }> = [];

    for (const user of allUsers) {
      const normalizedEmail = user.email.toLowerCase().trim();
      
      // Only update if the email is different
      if (user.email !== normalizedEmail) {
        console.log(`📧 Normalizing: ${user.email} → ${normalizedEmail}`);
        
        await db
          .update(users)
          .set({ 
            email: normalizedEmail,
            updatedAt: new Date()
          })
          .where(eq(users.id, user.id));
        
        updates.push({
          original: user.email,
          normalized: normalizedEmail
        });
        
        updatedCount++;
      }
    }

    console.log(`✅ Email normalization complete!`);
    console.log(`📈 Updated ${updatedCount} out of ${allUsers.length} users`);

    return NextResponse.json({
      success: true,
      message: `Email normalization complete. Updated ${updatedCount} out of ${allUsers.length} users.`,
      totalUsers: allUsers.length,
      updatedCount,
      updates: updates.slice(0, 10), // Return first 10 updates for reference
      hasMore: updates.length > 10
    });

  } catch (error) {
    console.error('❌ Error normalizing emails:', error);
    return NextResponse.json(
      { error: 'Failed to normalize emails', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
