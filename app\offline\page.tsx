import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Home, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { OfflinePageClient } from './OfflinePageClient';

export const metadata: Metadata = {
  title: 'Offline - Property Trendz',
  description: 'You are currently offline. Please check your internet connection.',
  robots: 'noindex, nofollow',
};

export default function OfflinePage() {
  return <OfflinePageClient />;
}