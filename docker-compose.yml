version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: property_trendz_db
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-property_trendz}
      POSTGRES_USER: ${POSTGRES_USER:-property_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:?POSTGRES_PASSWORD is required}
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-property_user} -d ${POSTGRES_DB:-property_trendz}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - property_network
    security_opt:
      - no-new-privileges:true

  # Optional: Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: property_trendz_redis
    restart: unless-stopped
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:?REDIS_PASSWORD is required}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - property_network
    security_opt:
      - no-new-privileges:true

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  property_network:
    driver: bridge