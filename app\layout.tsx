import type { Metadata } from 'next'
import { Inter, Poppins } from 'next/font/google'
import './globals.css'
import { Toaster } from 'react-hot-toast'
import Navigation from '@/components/Navigation'
import Footer from '@/components/Footer'
import { SessionProvider } from '@/components/providers/SessionProvider'
import { GoogleAnalytics } from '@/components/analytics/GoogleAnalytics'
import { ServiceWorkerRegistration } from '@/components/pwa/ServiceWorkerRegistration'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
})

const poppins = Poppins({ 
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  variable: '--font-poppins',
})

export const metadata: Metadata = {
  metadataBase: new URL('https://delhi-ncr-properties.vercel.app'),
  title: 'Property Trendz - Find Your Dream Home in Delhi NCR',
  description: 'Discover the best properties in Delhi NCR with Property Trendz. Houses, apartments, and commercial spaces for sale and rent.',
  keywords: 'Property Trendz, Delhi NCR properties, real estate, houses for sale, apartments, property listing',
  authors: [{ name: 'Property Trendz' }],
  robots: 'index, follow',
  manifest: '/manifest.json',
  openGraph: {
    title: 'Property Trendz - Find Your Dream Home in Delhi NCR',
    description: 'Discover the best properties in Delhi NCR with Property Trendz. Houses, apartments, and commercial spaces for sale and rent.',
    type: 'website',
    locale: 'en_US',
  }
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const gaId = process.env.NEXT_PUBLIC_GA_ID;

  return (
    <html lang="en" className={`${inter.variable} ${poppins.variable}`}>
      <body className={`${inter.className} antialiased bg-neutral-50`}>
        <SessionProvider>
          <Navigation />
          <main className="min-h-screen">
            {children}
          </main>
          <Footer />
          <Toaster 
            position="top-right"
            toastOptions={{
              duration: 4000,
              style: {
                background: '#363636',
                color: '#fff',
              },
            }}
          />
        </SessionProvider>
        {gaId && <GoogleAnalytics gaId={gaId} />}
        <ServiceWorkerRegistration />
      </body>
    </html>
  )
} 