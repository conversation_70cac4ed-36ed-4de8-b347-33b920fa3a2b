import { getServerSession, authConfig } from '@/lib/auth/auth';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { ContentManagement } from '@/components/admin/ContentManagement';

export const metadata: Metadata = {
  title: 'Website Settings | Admin',
  description: 'Manage website content and settings',
};

export default async function SettingsPage() {
  const session = await getServerSession(authConfig) as any;

  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Website Settings</h1>
          <p className="mt-2 text-gray-600">
            Manage website content, contact information, and SEO settings.
          </p>
        </div>
        
        <ContentManagement />
      </div>
    </div>
  );
}