import { Metadata } from 'next';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { redirect } from 'next/navigation';
import { SavedSearchesPage } from '@/components/saved-searches/SavedSearchesPage';

export const metadata: Metadata = {
  title: 'Saved Searches | Property Trendz',
  description: 'Manage your saved property searches and email alerts.',
};

export default async function SavedSearches() {
  const session = await getServerSession(authConfig) as Session | null;

  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/saved-searches');
  }

  return <SavedSearchesPage />;
}