import { getServerSession, authConfig } from '@/lib/auth/auth';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { PropertyForm } from '@/components/admin/PropertyForm';
import { db } from '@/lib/db/config';
import { properties } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export const metadata: Metadata = {
  title: 'Edit Property | Admin',
  description: 'Edit property details',
};

interface EditPropertyPageProps {
  params: {
    id: string;
  };
}

export default async function EditPropertyPage({ params }: EditPropertyPageProps) {
  const session = await getServerSession(authConfig) as any;

  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }

  // Fetch the property data
  let property = null;
  try {
    const propertyData = await db
      .select()
      .from(properties)
      .where(eq(properties.id, params.id))
      .limit(1);

    if (propertyData[0]) {
      property = propertyData[0];
    } else {
      redirect('/admin?error=property-not-found');
    }
  } catch (error) {
    console.error('Error fetching property:', error);
    redirect('/admin?error=fetch-failed');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Edit Property</h1>
          <p className="mt-2 text-gray-600">
            Update the property details below.
          </p>
        </div>
        
        <PropertyForm property={property} />
      </div>
    </div>
  );
}
