import { NextResponse } from 'next/server';
import { testDatabaseConnection, getDatabaseInfo } from '@/lib/db/utils';

export async function GET() {
  try {
    // Test database connection using centralized configuration
    const dbTest = await testDatabaseConnection();
    const dbInfo = getDatabaseInfo();

    if (!dbTest.success) {
      return NextResponse.json(
        {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          error: 'Database connection failed',
          details: dbTest.error,
          database: {
            environment: dbInfo.environment,
            host: dbInfo.host,
            port: dbInfo.port,
            isDocker: dbInfo.isDocker,
          },
        },
        { status: 503 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version || '1.0.0',
      database: {
        environment: dbInfo.environment,
        host: dbInfo.host,
        port: dbInfo.port,
        database: dbInfo.database,
        isDocker: dbInfo.isDocker,
        status: 'connected',
      },
    });
  } catch (error) {
    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 503 }
    );
  }
}
