'use client'

import { motion, AnimatePresence } from 'framer-motion'
import {
  Search, MapPin, Home, Building2, Key, Phone, MessageCircle,
  Filter, Grid3X3, List, Heart, Eye, TrendingUp, Users,
  Star, ChevronLeft, ChevronRight, Play, Pause, SlidersHorizontal,
  Bed, Bath, Square, Calendar, ArrowRight, CheckCircle
} from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { useState, useEffect, useRef } from 'react'

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 }
  }
}

const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}



// Hero background images
const heroImages = [
  {
    url: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=1920&h=1080&fit=crop&q=80",
    title: "Luxury Villa in Gurgaon",
    location: "DLF Phase 2"
  },
  {
    url: "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=1920&h=1080&fit=crop&q=80",
    title: "Modern Apartment in Noida",
    location: "Sector 62"
  },
  {
    url: "https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=1920&h=1080&fit=crop&q=80",
    title: "Premium Penthouse",
    location: "Cyber City"
  },
  {
    url: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=1920&h=1080&fit=crop&q=80",
    title: "Designer Home",
    location: "South Delhi"
  }
]

// Featured properties data
const featuredProperties = [
  {
    id: 1,
    title: "Luxury 4 BHK Villa",
    location: "DLF Phase 2, Gurgaon",
    price: "₹3.5 Cr",
    originalPrice: "₹4.2 Cr",
    image: "https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=600&h=400&fit=crop&q=80",
    beds: 4,
    baths: 4,
    area: "3200 sq ft",
    type: "Villa",
    featured: true,
    new: false,
    views: 1250,
    likes: 89
  },
  {
    id: 2,
    title: "Modern 3 BHK Apartment",
    location: "Sector 62, Noida",
    price: "₹1.8 Cr",
    originalPrice: "₹2.1 Cr",
    image: "https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=600&h=400&fit=crop&q=80",
    beds: 3,
    baths: 3,
    area: "1850 sq ft",
    type: "Apartment",
    featured: false,
    new: true,
    views: 890,
    likes: 67
  },
  {
    id: 3,
    title: "Premium Builder Floor",
    location: "Greater Kailash, Delhi",
    price: "₹4.2 Cr",
    originalPrice: null,
    image: "https://images.unsplash.com/photo-1460317442991-0ec209397118?w=600&h=400&fit=crop&q=80",
    beds: 4,
    baths: 4,
    area: "2400 sq ft",
    type: "Builder Floor",
    featured: true,
    new: false,
    views: 2100,
    likes: 156
  },
  {
    id: 4,
    title: "Spacious 2 BHK Flat",
    location: "Dwarka, Delhi",
    price: "₹25,000/month",
    originalPrice: null,
    image: "https://images.unsplash.com/photo-1522708323590-d24dbb6b0267?w=600&h=400&fit=crop&q=80",
    beds: 2,
    baths: 2,
    area: "1200 sq ft",
    type: "Apartment",
    featured: false,
    new: false,
    views: 456,
    likes: 23,
    forRent: true
  },
  {
    id: 5,
    title: "Luxury Penthouse",
    location: "Cyber City, Gurgaon",
    price: "₹2.8 Cr",
    originalPrice: "₹3.2 Cr",
    image: "https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=600&h=400&fit=crop&q=80",
    beds: 3,
    baths: 3,
    area: "2100 sq ft",
    type: "Penthouse",
    featured: true,
    new: false,
    views: 1890,
    likes: 234
  },
  {
    id: 6,
    title: "Independent House",
    location: "Vasant Kunj, Delhi",
    price: "₹6.5 Cr",
    originalPrice: null,
    image: "https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=600&h=400&fit=crop&q=80",
    beds: 5,
    baths: 5,
    area: "4500 sq ft",
    type: "Independent House",
    featured: false,
    new: true,
    views: 3200,
    likes: 445
  }
]

// Additional animation variants
const slideInLeft = {
  initial: { opacity: 0, x: -30 },
  animate: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6 }
  }
}

const slideInRight = {
  initial: { opacity: 0, x: 30 },
  animate: {
    opacity: 1,
    x: 0,
    transition: { duration: 0.6 }
  }
}

export default function HomePage() {
  const [searchLocation, setSearchLocation] = useState('')
  const [propertyType, setPropertyType] = useState('')
  const [priceRange, setPriceRange] = useState('')
  const [bedrooms, setBedrooms] = useState('')
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false)
  const [likedProperties, setLikedProperties] = useState(new Set())
  const intervalRef = useRef(null)

  // Auto-rotate hero images
  useEffect(() => {
    if (isPlaying) {
      intervalRef.current = setInterval(() => {
        setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
      }, 5000)
    } else {
      clearInterval(intervalRef.current)
    }

    return () => clearInterval(intervalRef.current)
  }, [isPlaying])

  const toggleLike = (propertyId) => {
    setLikedProperties(prev => {
      const newSet = new Set(prev)
      if (newSet.has(propertyId)) {
        newSet.delete(propertyId)
      } else {
        newSet.add(propertyId)
      }
      return newSet
    })
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % heroImages.length)
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + heroImages.length) % heroImages.length)
  }

  return (
    <div className="min-h-screen overflow-hidden">

      {/* Hero Section with Dynamic Background */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Dynamic Background Images */}
        <div className="absolute inset-0">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentImageIndex}
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 1.5, ease: "easeInOut" }}
              className="absolute inset-0"
            >
              <Image
                src={heroImages[currentImageIndex].url}
                alt={heroImages[currentImageIndex].title}
                fill
                className="object-cover"
                priority
              />
            </motion.div>
          </AnimatePresence>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-black/20 to-black/60"></div>

          {/* Property Info Overlay */}
          <motion.div
            key={currentImageIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
            className="absolute bottom-8 left-8 text-white"
          >
            <div className="bg-black/30 backdrop-blur-md rounded-lg p-4">
              <h3 className="text-lg font-semibold">{heroImages[currentImageIndex].title}</h3>
              <p className="text-sm text-gray-200">{heroImages[currentImageIndex].location}</p>
            </div>
          </motion.div>
        </div>

        {/* Navigation Controls */}
        <button
          onClick={prevImage}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md hover:bg-white/30 text-white p-3 rounded-full transition-all duration-200 z-10"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>

        <button
          onClick={nextImage}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md hover:bg-white/30 text-white p-3 rounded-full transition-all duration-200 z-10"
        >
          <ChevronRight className="w-6 h-6" />
        </button>

        {/* Play/Pause Control */}
        <button
          onClick={() => setIsPlaying(!isPlaying)}
          className="absolute top-8 right-8 bg-white/20 backdrop-blur-md hover:bg-white/30 text-white p-3 rounded-full transition-all duration-200 z-10"
        >
          {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
        </button>

        {/* Floating Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="relative z-20 w-full max-w-4xl mx-auto px-4"
        >
          {/* Main Heading */}
          <div className="text-center mb-8">
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="text-4xl md:text-6xl font-display font-bold text-white leading-tight mb-4"
            >
              Find Your Perfect
              <span className="block text-transparent bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text">
                Dream Home
              </span>
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl text-white/90 max-w-2xl mx-auto"
            >
              Discover premium properties in Delhi NCR with our advanced search and expert guidance
            </motion.p>
          </div>

          {/* Advanced Search Card */}
          <div className="bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl p-6 md:p-8">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-neutral-800">Search Properties</h3>
              <button
                onClick={() => setShowAdvancedSearch(!showAdvancedSearch)}
                className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 transition-colors"
              >
                <SlidersHorizontal className="w-5 h-5" />
                <span className="text-sm font-medium">
                  {showAdvancedSearch ? 'Simple' : 'Advanced'}
                </span>
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {/* Location Search */}
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Location (e.g., Gurgaon)"
                  value={searchLocation}
                  onChange={(e) => setSearchLocation(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                />
              </div>

              {/* Property Type */}
              <div className="relative">
                <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                <select
                  value={propertyType}
                  onChange={(e) => setPropertyType(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none transition-all"
                >
                  <option value="">Property Type</option>
                  <option value="apartment">Apartment</option>
                  <option value="villa">Villa</option>
                  <option value="house">Independent House</option>
                  <option value="builder-floor">Builder Floor</option>
                  <option value="penthouse">Penthouse</option>
                  <option value="commercial">Commercial</option>
                </select>
              </div>

              {/* Price Range */}
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 text-sm font-medium">₹</span>
                <select
                  value={priceRange}
                  onChange={(e) => setPriceRange(e.target.value)}
                  className="w-full pl-8 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none transition-all"
                >
                  <option value="">Price Range</option>
                  <option value="0-50">Under ₹50L</option>
                  <option value="50-100">₹50L - ₹1Cr</option>
                  <option value="100-200">₹1Cr - ₹2Cr</option>
                  <option value="200-500">₹2Cr - ₹5Cr</option>
                  <option value="500+">Above ₹5Cr</option>
                </select>
              </div>

              {/* Bedrooms */}
              <div className="relative">
                <Bed className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
                <select
                  value={bedrooms}
                  onChange={(e) => setBedrooms(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent appearance-none transition-all"
                >
                  <option value="">Bedrooms</option>
                  <option value="1">1 BHK</option>
                  <option value="2">2 BHK</option>
                  <option value="3">3 BHK</option>
                  <option value="4">4 BHK</option>
                  <option value="5+">5+ BHK</option>
                </select>
              </div>
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showAdvancedSearch && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3 }}
                  className="border-t border-neutral-200 pt-6 mb-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">Furnishing</label>
                      <select className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Any</option>
                        <option value="furnished">Furnished</option>
                        <option value="semi-furnished">Semi-Furnished</option>
                        <option value="unfurnished">Unfurnished</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">Parking</label>
                      <select className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Any</option>
                        <option value="1">1 Car</option>
                        <option value="2">2 Cars</option>
                        <option value="3+">3+ Cars</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 mb-2">Age of Property</label>
                      <select className="w-full px-3 py-2 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                        <option value="">Any</option>
                        <option value="0-1">Under 1 Year</option>
                        <option value="1-5">1-5 Years</option>
                        <option value="5-10">5-10 Years</option>
                        <option value="10+">10+ Years</option>
                      </select>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Search Button */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={() => {
                  // Build search query from form inputs
                  const params = new URLSearchParams();
                  if (searchLocation) params.append('location', searchLocation);
                  if (propertyType) params.append('type', propertyType);
                  if (priceRange) params.append('price', priceRange);
                  if (bedrooms) params.append('bedrooms', bedrooms);

                  // Navigate to properties page with search params
                  window.location.href = `/properties?${params.toString()}`;
                }}
                className="flex-1 btn-primary flex items-center justify-center space-x-2 py-4"
              >
                <Search className="w-5 h-5" />
                <span className="font-semibold">Search Properties</span>
              </button>
              <Link
                href="/properties"
                className="btn-outline flex items-center justify-center space-x-2 py-4 px-6"
              >
                <span>Browse All</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Image Indicators */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentImageIndex(index)}
              className={`w-3 h-3 rounded-full transition-all duration-200 ${
                index === currentImageIndex
                  ? 'bg-white scale-125'
                  : 'bg-white/50 hover:bg-white/75'
              }`}
            />
          ))}
        </div>
      </section>

      {/* Featured Properties Section */}
      <section className="section-padding bg-neutral-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col md:flex-row md:items-center md:justify-between mb-12"
          >
            <div>
              <h2 className="text-3xl md:text-4xl font-display font-bold text-neutral-800 mb-4">
                Featured Properties
              </h2>
              <p className="text-lg text-neutral-600 max-w-2xl">
                Handpicked premium properties that offer exceptional value and prime locations
              </p>
            </div>

            {/* View Toggle */}
            <div className="flex items-center space-x-4 mt-6 md:mt-0">
              <div className="flex items-center bg-white rounded-lg p-1 shadow-sm">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-all ${
                    viewMode === 'grid'
                      ? 'bg-primary-600 text-white'
                      : 'text-neutral-600 hover:text-primary-600'
                  }`}
                >
                  <Grid3X3 className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-all ${
                    viewMode === 'list'
                      ? 'bg-primary-600 text-white'
                      : 'text-neutral-600 hover:text-primary-600'
                  }`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>

              <Link
                href="/properties"
                className="btn-outline flex items-center space-x-2"
              >
                <span>View All</span>
                <ArrowRight className="w-4 h-4" />
              </Link>
            </div>
          </motion.div>

          {/* Properties Grid/List */}
          <motion.div
            layout
            className={`grid gap-4 md:gap-6 ${
              viewMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4'
                : 'grid-cols-1'
            }`}
          >
            {featuredProperties.slice(0, 6).map((property, index) => (
              <motion.div
                key={property.id}
                layout
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all duration-300 overflow-hidden group ${
                  viewMode === 'list' ? 'flex' : ''
                }`}
              >
                <div className={`relative overflow-hidden ${
                  viewMode === 'list' ? 'w-80 h-64' : 'h-40 sm:h-44 md:h-48 lg:h-52'
                }`}>
                  <Image
                    src={property.image}
                    alt={property.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />

                  {/* Property Badges */}
                  <div className="absolute top-2 md:top-4 left-2 md:left-4 flex flex-col space-y-1 md:space-y-2">
                    {property.featured && (
                      <span className="bg-primary-600 text-white px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium">
                        Featured
                      </span>
                    )}
                    {property.new && (
                      <span className="bg-green-600 text-white px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium">
                        New
                      </span>
                    )}
                    {property.forRent && (
                      <span className="bg-orange-600 text-white px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium">
                        For Rent
                      </span>
                    )}
                  </div>

                  {/* Like Button */}
                  <button
                    onClick={() => toggleLike(property.id)}
                    className="absolute top-2 md:top-4 right-2 md:right-4 bg-white/90 hover:bg-white text-neutral-600 p-1.5 md:p-2 rounded-full transition-all duration-200 hover:scale-110"
                  >
                    <Heart
                      className={`w-4 md:w-5 h-4 md:h-5 transition-colors ${
                        likedProperties.has(property.id)
                          ? 'fill-red-500 text-red-500'
                          : 'hover:text-red-500'
                      }`}
                    />
                  </button>

                  {/* Property Stats */}
                  <div className="absolute bottom-2 md:bottom-4 right-2 md:right-4 flex items-center space-x-2 md:space-x-3 text-white text-xs md:text-sm">
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-1.5 md:px-2 py-1">
                      <Eye className="w-3 md:w-4 h-3 md:h-4" />
                      <span>{property.views}</span>
                    </div>
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-1.5 md:px-2 py-1">
                      <Heart className="w-3 md:w-4 h-3 md:h-4" />
                      <span>{property.likes}</span>
                    </div>
                  </div>
                </div>

                <div className={`p-3 md:p-6 ${viewMode === 'list' ? 'flex-1' : ''}`}>
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <span className="text-lg md:text-2xl font-bold text-primary-600">
                        {property.price}
                      </span>
                      {property.originalPrice && (
                        <span className="text-xs md:text-sm text-neutral-500 line-through ml-2">
                          {property.originalPrice}
                        </span>
                      )}
                    </div>
                    <span className="text-xs md:text-sm text-neutral-500 bg-neutral-100 px-2 py-1 rounded">
                      {property.type}
                    </span>
                  </div>

                  <h3 className="text-base md:text-lg font-semibold text-neutral-800 mb-2 group-hover:text-primary-600 transition-colors line-clamp-2">
                    {property.title}
                  </h3>

                  <p className="text-neutral-600 mb-4 flex items-center text-sm">
                    <MapPin className="w-3 md:w-4 h-3 md:h-4 mr-1 flex-shrink-0" />
                    <span className="truncate">{property.location}</span>
                  </p>

                  <div className="grid grid-cols-3 gap-2 md:gap-4 text-xs md:text-sm text-neutral-600 mb-4 md:mb-6">
                    <div className="flex items-center">
                      <Bed className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                      <span className="hidden sm:inline">{property.beds} Beds</span>
                      <span className="sm:hidden">{property.beds}</span>
                    </div>
                    <div className="flex items-center">
                      <Bath className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                      <span className="hidden sm:inline">{property.baths} Baths</span>
                      <span className="sm:hidden">{property.baths}</span>
                    </div>
                    <div className="flex items-center">
                      <Square className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                      <span className="truncate">{property.area}</span>
                    </div>
                  </div>

                  <div className="flex space-x-2 md:space-x-3">
                    <Link
                      href={`/properties/${property.id}`}
                      className="flex-1 btn-primary text-center text-xs md:text-sm py-2"
                    >
                      <span className="hidden sm:inline">View Details</span>
                      <span className="sm:hidden">View</span>
                    </Link>
                    <button
                      onClick={() => window.open('tel:+919810129777', '_self')}
                      className="btn-outline px-2 md:px-4 py-2 text-xs md:text-sm hover:bg-blue-50 hover:border-blue-300"
                      title="Call Now"
                    >
                      <Phone className="w-3 md:w-4 h-3 md:h-4" />
                    </button>
                    <button
                      onClick={() => window.open('https://wa.me/919810129777?text=Hi, I am interested in this property', '_blank')}
                      className="btn-outline px-2 md:px-4 py-2 text-xs md:text-sm hover:bg-green-50 hover:border-green-300"
                      title="WhatsApp"
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Trending Properties Carousel */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex items-center justify-between mb-12"
          >
            <div>
              <h2 className="text-3xl md:text-4xl font-display font-bold text-neutral-800 mb-4">
                Trending Properties
              </h2>
              <p className="text-lg text-neutral-600">
                Most viewed and liked properties this week
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-6 h-6 text-primary-600" />
              <span className="text-sm font-medium text-primary-600">Hot Picks</span>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProperties.slice(0, 4).map((property, index) => (
              <motion.div
                key={`trending-${property.id}`}
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all duration-300 overflow-hidden group"
              >
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={property.image}
                    alt={property.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                    <TrendingUp className="w-4 h-4" />
                    <span>Trending</span>
                  </div>
                  <div className="absolute bottom-4 left-4 right-4 flex items-center justify-between text-white text-sm">
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                      <Eye className="w-4 h-4" />
                      <span>{property.views}</span>
                    </div>
                    <div className="flex items-center space-x-1 bg-black/50 backdrop-blur-sm rounded-full px-2 py-1">
                      <Heart className="w-4 h-4" />
                      <span>{property.likes}</span>
                    </div>
                  </div>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-neutral-800 mb-1 group-hover:text-primary-600 transition-colors">
                    {property.title}
                  </h3>
                  <p className="text-sm text-neutral-600 mb-2 flex items-center">
                    <MapPin className="w-3 h-3 mr-1" />
                    {property.location}
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-primary-600">
                      {property.price}
                    </span>
                    <span className="text-xs text-neutral-500">
                      {property.area}
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Popular Searches & Quick Actions */}
      <section className="section-padding bg-neutral-50">
        <div className="container-custom">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Popular Searches */}
            <motion.div
              initial={{ opacity: 0, x: -30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-display font-bold text-neutral-800 mb-6">
                Popular Searches
              </h3>
              <div className="grid grid-cols-2 gap-4">
                {[
                  { term: "3 BHK in Gurgaon", count: "245 properties" },
                  { term: "Apartments in Noida", count: "189 properties" },
                  { term: "Villas in Delhi", count: "156 properties" },
                  { term: "Builder Floors", count: "298 properties" },
                  { term: "Ready to Move", count: "567 properties" },
                  { term: "Under ₹2 Cr", count: "423 properties" }
                ].map((search, index) => (
                  <motion.button
                    key={search.term}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    onClick={() => {
                      // Navigate to properties page with search term
                      const searchQuery = search.term.toLowerCase();
                      window.location.href = `/properties?search=${encodeURIComponent(searchQuery)}`;
                    }}
                    className="bg-white rounded-lg p-4 text-left hover:shadow-md transition-all duration-200 group cursor-pointer"
                  >
                    <div className="font-medium text-neutral-800 group-hover:text-primary-600 transition-colors">
                      {search.term}
                    </div>
                    <div className="text-sm text-neutral-500 mt-1">
                      {search.count}
                    </div>
                  </motion.button>
                ))}
              </div>
            </motion.div>

            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, x: 30 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl font-display font-bold text-neutral-800 mb-6">
                Quick Actions
              </h3>
              <div className="space-y-4">
                {[
                  {
                    icon: <Home className="w-6 h-6" />,
                    title: "Sell Your Property",
                    desc: "Get the best price for your property",
                    color: "bg-green-600"
                  },
                  {
                    icon: <Key className="w-6 h-6" />,
                    title: "Rent Your Property",
                    desc: "Find reliable tenants quickly",
                    color: "bg-blue-600"
                  },
                  {
                    icon: <Users className="w-6 h-6" />,
                    title: "Property Consultation",
                    desc: "Expert advice for your investment",
                    color: "bg-purple-600"
                  },
                  {
                    icon: <Star className="w-6 h-6" />,
                    title: "Property Valuation",
                    desc: "Know your property's market value",
                    color: "bg-orange-600"
                  }
                ].map((action, index) => (
                  <motion.div
                    key={action.title}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    onClick={() => {
                      // Navigate based on action type
                      switch(action.title) {
                        case "Sell Your Property":
                          window.location.href = "/sell-property";
                          break;
                        case "Rent Your Property":
                          window.location.href = "/rent-property";
                          break;
                        case "Property Consultation":
                          window.location.href = "/consultation";
                          break;
                        case "Property Valuation":
                          window.location.href = "/valuation";
                          break;
                        default:
                          window.location.href = "/contact";
                      }
                    }}
                    className="bg-white rounded-lg p-6 hover:shadow-md transition-all duration-200 group cursor-pointer"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`${action.color} text-white p-3 rounded-lg group-hover:scale-110 transition-transform`}>
                        {action.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-neutral-800 group-hover:text-primary-600 transition-colors">
                          {action.title}
                        </h4>
                        <p className="text-sm text-neutral-600 mt-1">
                          {action.desc}
                        </p>
                      </div>
                      <ArrowRight className="w-5 h-5 text-neutral-400 group-hover:text-primary-600 transition-colors" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="section-padding bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="container-custom relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-display font-bold text-white mb-4">
              Trusted by Thousands
            </h2>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Leading the real estate market in Delhi NCR with proven results
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            {[
              { number: "2500+", label: "Properties Sold", icon: <Home className="w-8 h-8" /> },
              { number: "5000+", label: "Happy Families", icon: <Users className="w-8 h-8" /> },
              { number: "35+", label: "Years Experience", icon: <Star className="w-8 h-8" /> },
              { number: "50+", label: "Areas Covered", icon: <MapPin className="w-8 h-8" /> }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 hover:bg-white/20 transition-all duration-300">
                  <div className="text-white/80 mb-4 flex justify-center group-hover:scale-110 transition-transform">
                    {stat.icon}
                  </div>
                  <div className="text-4xl md:text-5xl font-bold mb-2 text-white">
                    {stat.number}
                  </div>
                  <div className="text-blue-100 font-medium">
                    {stat.label}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-12"
          >
            <h2 className="text-3xl md:text-4xl font-display font-bold text-neutral-800 mb-4">
              What Our Clients Say
            </h2>
            <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
              Real stories from families who found their dream homes with us
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Rajesh Kumar",
                location: "Gurgaon",
                rating: 5,
                text: "Excellent service! Found my dream villa in DLF Phase 2. The team was professional and guided us through every step."
              },
              {
                name: "Priya Sharma",
                location: "Noida",
                rating: 5,
                text: "Very satisfied with the apartment we purchased. Great location, fair price, and smooth transaction process."
              },
              {
                name: "Amit Singh",
                location: "Delhi",
                rating: 5,
                text: "Professional team with deep market knowledge. They helped us find the perfect builder floor in South Delhi."
              }
            ].map((testimonial, index) => (
              <motion.div
                key={testimonial.name}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-neutral-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-primary-600 rounded-full flex items-center justify-center mr-4">
                    <span className="text-white font-semibold text-sm">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-neutral-800">{testimonial.name}</h4>
                    <p className="text-sm text-neutral-600">{testimonial.location}</p>
                  </div>
                </div>
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-neutral-700 italic">"{testimonial.text}"</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="section-padding bg-gradient-to-r from-neutral-900 to-neutral-800 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/20 to-secondary-600/20"></div>
        <div className="container-custom relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center"
          >
            <h2 className="text-3xl md:text-5xl font-display font-bold text-white mb-6">
              Ready to Find Your
              <span className="block text-transparent bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text">
                Dream Home?
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
              Join thousands of satisfied customers who found their perfect property with Property Trendz.
              Let our experts guide you to your ideal home in Delhi NCR.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
              <Link
                href="/properties"
                className="btn-primary bg-white text-neutral-800 hover:bg-gray-100 px-8 py-4 text-lg font-semibold"
              >
                Browse Properties
              </Link>
              <Link
                href="/contact"
                className="btn-outline border-white text-white hover:bg-white hover:text-neutral-800 px-8 py-4 text-lg font-semibold"
              >
                Get Expert Consultation
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-gray-300">
              <div className="flex items-center space-x-2">
                <Phone className="w-5 h-5 text-primary-400" />
                <span>+91 98101 29777</span>
              </div>
              <div className="flex items-center space-x-2">
                <MessageCircle className="w-5 h-5 text-green-400" />
                <span>WhatsApp Support</span>
              </div>
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-blue-400" />
                <span>Free Consultation</span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

    </div>
  )
}