'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Heart, Grid, List, Search, Filter, Trash2, Share2, Eye } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FavoriteButton } from '@/components/ui/FavoriteButton';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface FavoriteProperty {
  favoriteId: string;
  createdAt: string;
  property: {
    id: string;
    title: string;
    slug: string;
    price: string;
    area: string;
    city: string;
    propertyType: string;
    listingType: string;
    bedrooms: number;
    bathrooms: number;
    totalArea: number;
    images: string[];
    status: string;
  };
}

export function FavoritesPage() {
  const [favorites, setFavorites] = useState<FavoriteProperty[]>([]);
  const [filteredFavorites, setFilteredFavorites] = useState<FavoriteProperty[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  useEffect(() => {
    fetchFavorites();
  }, []);

  useEffect(() => {
    filterAndSortFavorites();
  }, [favorites, searchTerm, filterType, sortBy]);

  const fetchFavorites = async () => {
    try {
      const response = await fetch('/api/favorites');
      if (response.ok) {
        const data = await response.json();
        setFavorites(data.favorites);
      }
    } catch (error) {
      console.error('Error fetching favorites:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortFavorites = () => {
    let filtered = [...favorites];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.property.area.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.property.city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.property.propertyType === filterType);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'price-low':
          return parseFloat(a.property.price) - parseFloat(b.property.price);
        case 'price-high':
          return parseFloat(b.property.price) - parseFloat(a.property.price);
        case 'title':
          return a.property.title.localeCompare(b.property.title);
        default:
          return 0;
      }
    });

    setFilteredFavorites(filtered);
  };

  const removeFavorite = async (propertyId: string) => {
    try {
      const response = await fetch(`/api/favorites?propertyId=${propertyId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setFavorites(prev => prev.filter(item => item.property.id !== propertyId));
      }
    } catch (error) {
      console.error('Error removing favorite:', error);
    }
  };

  const shareProperty = async (property: any) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: property.title,
          text: `Check out this property: ${property.title}`,
          url: `${window.location.origin}/properties/${property.slug}`,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback: Copy to clipboard
      navigator.clipboard.writeText(`${window.location.origin}/properties/${property.slug}`);
      alert('Property link copied to clipboard!');
    }
  };

  const PropertyCard = ({ item }: { item: FavoriteProperty }) => {
    const { property } = item;
    const imageUrl = property.images?.[0] || '/placeholder-property.jpg';

    if (viewMode === 'list') {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card hover:shadow-card-hover transition-all duration-300 overflow-hidden"
        >
          <div className="flex">
            <div className="relative w-48 h-32 flex-shrink-0">
              <Image
                src={imageUrl}
                alt={property.title}
                fill
                className="object-cover"
              />
              <div className="absolute top-2 right-2">
                <FavoriteButton propertyId={property.id} size="sm" />
              </div>
            </div>
            
            <div className="flex-1 p-4">
              <div className="flex justify-between items-start mb-2">
                <Link href={`/properties/${property.slug}`}>
                  <h3 className="text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors">
                    {property.title}
                  </h3>
                </Link>
                <span className="text-xl font-bold text-primary-600">
                  ₹{parseFloat(property.price).toLocaleString()}
                </span>
              </div>
              
              <p className="text-gray-600 mb-2">{property.area}, {property.city}</p>
              
              <div className="flex items-center gap-4 text-sm text-gray-500 mb-3">
                <span className="capitalize">{property.propertyType}</span>
                <span>•</span>
                <span>{property.bedrooms} BHK</span>
                <span>•</span>
                <span>{property.totalArea} sq ft</span>
                <span>•</span>
                <span className="capitalize">{property.listingType}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Link href={`/properties/${property.slug}`}>
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-1" />
                      View
                    </Button>
                  </Link>
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => shareProperty(property)}
                  >
                    <Share2 className="w-4 h-4 mr-1" />
                    Share
                  </Button>
                </div>
                
                <span className="text-xs text-gray-400">
                  Saved {new Date(item.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      );
    }

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-card hover:shadow-card-hover transition-all duration-300 overflow-hidden group"
      >
        <div className="relative h-40 sm:h-44 md:h-48">
          <Image
            src={imageUrl}
            alt={property.title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-2 md:top-3 right-2 md:right-3">
            <FavoriteButton propertyId={property.id} />
          </div>
          <div className="absolute bottom-2 md:bottom-3 left-2 md:left-3 right-2 md:right-3">
            <span className="inline-flex px-2 py-1 text-xs font-medium bg-white/90 backdrop-blur-sm rounded">
              {property.listingType === 'sale' ? 'For Sale' : 'For Rent'}
            </span>
          </div>
        </div>

        <div className="p-3 md:p-4">
          <Link href={`/properties/${property.slug}`}>
            <h3 className="text-base md:text-lg font-semibold text-gray-900 hover:text-primary-600 transition-colors mb-1 line-clamp-2">
              {property.title}
            </h3>
          </Link>

          <p className="text-gray-600 text-xs md:text-sm mb-2">{property.area}, {property.city}</p>

          <div className="flex items-center gap-2 md:gap-3 text-xs md:text-sm text-gray-500 mb-3">
            <span className="capitalize">{property.propertyType}</span>
            <span>•</span>
            <span>{property.bedrooms} BHK</span>
            <span>•</span>
            <span className="hidden sm:inline">{property.totalArea} sq ft</span>
            <span className="sm:hidden">{property.totalArea}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-xl font-bold text-primary-600">
              ₹{parseFloat(property.price).toLocaleString()}
            </span>
            <div className="flex gap-1">
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => shareProperty(property)}
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
          
          <div className="mt-3 pt-3 border-t border-gray-100">
            <span className="text-xs text-gray-400">
              Saved on {new Date(item.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
      </motion.div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Heart className="w-6 h-6 mr-2 text-red-500" />
                My Favorites
              </h1>
              <p className="text-gray-600 mt-1">
                {favorites.length} saved {favorites.length === 1 ? 'property' : 'properties'}
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="bg-white rounded-lg shadow-card p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search favorites..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="all">All Types</option>
              <option value="apartment">Apartment</option>
              <option value="house">House</option>
              <option value="villa">Villa</option>
              <option value="plot">Plot</option>
              <option value="commercial">Commercial</option>
            </select>
            
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="title">Title A-Z</option>
            </select>
            
            <Button variant="outline" className="flex items-center">
              <Filter className="w-4 h-4 mr-2" />
              More Filters
            </Button>
          </div>
        </div>

        {/* Results */}
        {filteredFavorites.length === 0 ? (
          <div className="bg-white rounded-lg shadow-card p-12 text-center">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              {searchTerm || filterType !== 'all' ? 'No matching favorites' : 'No favorites yet'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || filterType !== 'all' 
                ? 'Try adjusting your search or filters'
                : 'Start browsing properties and save your favorites here'
              }
            </p>
            <Link href="/properties">
              <Button>Browse Properties</Button>
            </Link>
          </div>
        ) : (
          <div className={`grid gap-4 md:gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {filteredFavorites.map((item) => (
              <PropertyCard key={item.favoriteId} item={item} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}