import { db } from '../lib/db/config';
import { users } from '../lib/db/schema';
import { eq } from 'drizzle-orm';

// Script to set a user as admin
async function setUserAsAdmin() {
  const email = process.argv[2];
  
  if (!email) {
    console.error('Usage: npm run set-admin <email>');
    console.error('Example: npm run set-admin <EMAIL>');
    process.exit(1);
  }

  try {
    console.log(`Setting admin role for user: ${email}`);
    
    // Find and update the user
    const result = await db
      .update(users)
      .set({ 
        role: 'admin',
        updatedAt: new Date()
      })
      .where(eq(users.email, email))
      .returning();

    if (result.length === 0) {
      console.error(`❌ User with email ${email} not found`);
      process.exit(1);
    }

    console.log(`✅ Successfully set ${email} as admin`);
    console.log(`User ID: ${result[0].id}`);
    console.log(`Name: ${result[0].name}`);
    console.log(`Role: ${result[0].role}`);
    
  } catch (error) {
    console.error('❌ Error setting admin role:', error);
    process.exit(1);
  }
  
  process.exit(0);
}

setUserAsAdmin();