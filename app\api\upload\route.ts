import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { 
  validateFile, 
  generateSecureFilename, 
  processAndSaveImage, 
  saveFile, 
  basicMalwareCheck,
  uploadConfig,
  type UploadResult
} from '@/lib/upload/config';
import { checkRateLimit } from '@/lib/rate-limit';

// POST /api/upload - Upload files with security checks
export async function POST(request: NextRequest): Promise<Response> {
  try {
    // Rate limiting
    const rateLimitResult = await checkRateLimit(request, 'upload');
    if (!rateLimitResult.success) {
      return NextResponse.json(
        { error: 'Too many upload requests. Please try again later.' },
        { status: 429 }
      );
    }

    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const contentType = request.headers.get('content-type');
    
    if (!contentType?.includes('multipart/form-data')) {
      return NextResponse.json(
        { error: 'Invalid content type. Use multipart/form-data' },
        { status: 400 }
      );
    }

    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const category = (formData.get('category') as string) || 'property';

    if (!['property', 'user', 'document'].includes(category)) {
      return NextResponse.json(
        { error: 'Invalid category' },
        { status: 400 }
      );
    }

    if (files.length === 0) {
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (files.length > uploadConfig.maxFilesPerUpload) {
      return NextResponse.json(
        { error: `Maximum ${uploadConfig.maxFilesPerUpload} files allowed per upload` },
        { status: 400 }
      );
    }

    const results: UploadResult[] = [];
    const errors: string[] = [];

    for (const file of files) {
      try {
        // Validate file
        const validation = validateFile(file);
        if (!validation.isValid) {
          errors.push(`${file.name}: ${validation.error}`);
          continue;
        }

        // Convert file to buffer
        const buffer = Buffer.from(await file.arrayBuffer());

        // Basic malware check
        const malwareCheck = basicMalwareCheck(buffer, file.name);
        if (!malwareCheck.isSafe) {
          errors.push(`${file.name}: Security threat detected - ${malwareCheck.reason}`);
          continue;
        }

        // Generate secure filename
        const secureFilename = generateSecureFilename(file.name, session.user.id);

        let result: UploadResult;

        // Process based on file type
        if (file.type.startsWith('image/')) {
          result = await processAndSaveImage(buffer, secureFilename, category as any);
        } else {
          result = await saveFile(buffer, secureFilename, file.type, file.name, category as any);
        }

        if (result.success) {
          results.push(result);
        } else {
          errors.push(`${file.name}: ${result.error}`);
        }
      } catch (error) {
        console.error(`Error processing file ${file.name}:`, error);
        errors.push(`${file.name}: Processing failed`);
      }
    }

    // Log upload activity
    console.log(`User ${session.user.id} uploaded ${results.length} files, ${errors.length} errors`);

    return NextResponse.json({
      success: true,
      message: `Successfully uploaded ${results.length} file(s)`,
      files: results,
      errors: errors.length > 0 ? errors : undefined,
      uploadedCount: results.length,
      errorCount: errors.length,
    });

  } catch (error) {
    console.error('Upload API error:', error);
    return NextResponse.json(
      { error: 'File upload failed' },
      { status: 500 }
    );
  }
}

// GET /api/upload - Get upload configuration (for frontend)
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    return NextResponse.json({
      maxFileSize: uploadConfig.maxFileSize,
      allowedMimeTypes: uploadConfig.allowedMimeTypes,
      allowedExtensions: uploadConfig.allowedExtensions,
      maxFilesPerUpload: uploadConfig.maxFilesPerUpload,
      categories: ['property', 'user', 'document'],
    });
  } catch (error) {
    console.error('Upload config API error:', error);
    return NextResponse.json(
      { error: 'Failed to get upload configuration' },
      { status: 500 }
    );
  }
}