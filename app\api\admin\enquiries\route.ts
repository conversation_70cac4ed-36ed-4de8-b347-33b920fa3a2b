import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { enquiries, properties } from '@/lib/db/schema';
import { desc, eq, like, and, or, count } from 'drizzle-orm';

// GET - List all enquiries with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const enquiryType = searchParams.get('enquiryType') || '';
    const source = searchParams.get('source') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          like(enquiries.name, `%${search}%`),
          like(enquiries.email, `%${search}%`),
          like(enquiries.phone, `%${search}%`),
          like(enquiries.message, `%${search}%`)
        )
      );
    }

    if (status) {
      conditions.push(eq(enquiries.status, status));
    }

    if (enquiryType) {
      conditions.push(eq(enquiries.enquiryType, enquiryType));
    }

    if (source) {
      conditions.push(eq(enquiries.source, source));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const totalResult = await db
      .select({ count: count() })
      .from(enquiries)
      .where(whereClause);

    const total = totalResult[0].count;

    // Get enquiries with property details
    const enquiryList = await db
      .select({
        id: enquiries.id,
        propertyId: enquiries.propertyId,
        userId: enquiries.userId,
        name: enquiries.name,
        email: enquiries.email,
        phone: enquiries.phone,
        message: enquiries.message,
        enquiryType: enquiries.enquiryType,
        budget: enquiries.budget,
        status: enquiries.status,
        source: enquiries.source,
        assignedTo: enquiries.assignedTo,
        followUpDate: enquiries.followUpDate,
        notes: enquiries.notes,
        createdAt: enquiries.createdAt,
        updatedAt: enquiries.updatedAt,
        // Property details
        propertyTitle: properties.title,
        propertyArea: properties.area,
        propertyCity: properties.city,
        propertyPrice: properties.price,
        propertyType: properties.propertyType,
      })
      .from(enquiries)
      .leftJoin(properties, eq(enquiries.propertyId, properties.id))
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(enquiries[sortBy as keyof typeof enquiries]) : enquiries[sortBy as keyof typeof enquiries])
      .limit(limit)
      .offset(offset);

    // Get status counts for dashboard
    const statusCounts = await db
      .select({
        status: enquiries.status,
        count: count(),
      })
      .from(enquiries)
      .groupBy(enquiries.status);

    return NextResponse.json({
      enquiries: enquiryList,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
      statusCounts: statusCounts.reduce((acc, item) => {
        acc[item.status] = item.count;
        return acc;
      }, {} as Record<string, number>),
    });
  } catch (error) {
    console.error('Error fetching enquiries:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST - Create new enquiry (admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const enquiryData = await request.json();
    const { 
      propertyId, 
      name, 
      email, 
      phone, 
      message, 
      enquiryType, 
      budget, 
      status, 
      source,
      assignedTo,
      followUpDate,
      notes 
    } = enquiryData;

    // Validate required fields
    if (!name || !email || !phone) {
      return NextResponse.json(
        { message: 'Name, email, and phone are required' },
        { status: 400 }
      );
    }

    // If propertyId is provided, verify property exists
    if (propertyId) {
      const property = await db
        .select()
        .from(properties)
        .where(eq(properties.id, propertyId))
        .limit(1);

      if (!property[0]) {
        return NextResponse.json(
          { message: 'Property not found' },
          { status: 404 }
        );
      }
    }

    // Create enquiry
    const newEnquiry = await db
      .insert(enquiries)
      .values({
        propertyId: propertyId || null,
        name,
        email,
        phone,
        message: message || '',
        enquiryType: enquiryType || 'general',
        budget: budget ? String(budget) : null,
        status: status || 'new',
        source: source || 'admin',
        assignedTo: assignedTo || null,
        followUpDate: followUpDate ? new Date(followUpDate) : null,
        notes: notes || null,
      })
      .returning();

    return NextResponse.json({
      message: 'Enquiry created successfully',
      enquiry: newEnquiry[0],
    });
  } catch (error) {
    console.error('Error creating enquiry:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
