export interface Property {
  _id: string
  title: string
  description: string
  price: number
  location: {
    area: string
    city: string
    address: string
    pincode?: string
    coordinates?: [number, number]
  }
  specifications: {
    bedrooms: number
    bathrooms: number
    area: number // in sq ft
    floors?: number
    parking: boolean
    furnished: 'Furnished' | 'Semi-Furnished' | 'Unfurnished'
    balconies?: number
    ageOfProperty?: number
  }
  images: string[]
  propertyType: 'apartment' | 'house' | 'builder-floor' | 'commercial' | 'plot'
  listingType: 'sale' | 'rent'
  status: 'available' | 'sold' | 'rented' | 'under-negotiation'
  amenities: string[]
  featured: boolean
  pricePerSqFt?: number
  contactDetails: {
    phone: string
    email?: string
    whatsapp?: string
  }
  createdAt: Date
  updatedAt: Date
  views?: number
}

export interface PropertyFilter {
  location?: string
  propertyType?: string
  listingType?: 'sale' | 'rent'
  minPrice?: number
  maxPrice?: number
  bedrooms?: number
  bathrooms?: number
  minArea?: number
  maxArea?: number
  furnished?: string
  amenities?: string[]
}

export interface PropertySearchParams {
  query?: string
  filters?: PropertyFilter
  page?: number
  limit?: number
  sortBy?: 'price' | 'area' | 'date' | 'featured'
  sortOrder?: 'asc' | 'desc'
}

export interface ContactForm {
  name: string
  phone: string
  email: string
  message: string
  propertyId?: string
  propertyTitle?: string
}

export interface PropertyStats {
  totalProperties: number
  totalViews: number
  averagePrice: number
  popularLocations: Array<{
    area: string
    count: number
  }>
} 