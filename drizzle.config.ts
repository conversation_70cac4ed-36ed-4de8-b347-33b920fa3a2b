import type { Config } from 'drizzle-kit';

import path from 'path';
import fs from 'fs';

// Load environment variables for drizzle CLI
const cwd = process.cwd();
const productionEnvPath = path.resolve(cwd, '.env.production');
const localEnvPath = path.resolve(cwd, '.env.local');

function loadEnvFile(filePath: string) {
  if (!fs.existsSync(filePath)) return;
  const content = fs.readFileSync(filePath, 'utf-8');
  for (const rawLine of content.split(/\r?\n/)) {
    const line = rawLine.trim();
    if (!line || line.startsWith('#')) continue;
    const idx = line.indexOf('=');
    if (idx === -1) continue;
    const key = line.slice(0, idx).trim();
    let value = line.slice(idx + 1).trim();
    if ((value.startsWith('"') && value.endsWith('"')) || (value.startsWith("'") && value.endsWith("'"))) {
      value = value.slice(1, -1);
    }
    if (process.env[key] === undefined) {
      process.env[key] = value;
    }
  }
}

if (fs.existsSync(productionEnvPath)) {
  loadEnvFile(productionEnvPath);
} else if (fs.existsSync(localEnvPath)) {
  loadEnvFile(localEnvPath);
}

// Import centralized database configuration
import { databaseConfig } from './lib/db/config';

// Use centralized database URL management
const dbUrl = databaseConfig.url;

if (!dbUrl) {
  throw new Error(`DATABASE_URL is not configured for ${databaseConfig.environment} environment. Check your environment variables.`);
}

console.log(`🔧 Drizzle using ${databaseConfig.environment} database: ${databaseConfig.host}:${databaseConfig.port}/${databaseConfig.database}`);

export default {
  schema: './lib/db/schema.ts',
  out: './lib/db/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: dbUrl,
  },
  verbose: true,
  strict: true,
} satisfies Config;