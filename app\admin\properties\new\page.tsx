import { getServerSession, authConfig } from '@/lib/auth/auth';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { PropertyForm } from '@/components/admin/PropertyForm';

export const metadata: Metadata = {
  title: 'Add New Property | Admin',
  description: 'Add a new property to the listings',
};

export default async function NewPropertyPage() {
  const session = await getServerSession(authConfig) as any;

  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Add New Property</h1>
          <p className="mt-2 text-gray-600">
            Fill in the details below to create a new property listing.
          </p>
        </div>
        
        <PropertyForm />
      </div>
    </div>
  );
}