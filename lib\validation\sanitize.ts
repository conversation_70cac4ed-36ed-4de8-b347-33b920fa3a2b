/**
 * Simple input sanitization utilities
 * Simplified version without external dependencies for better TypeScript compatibility
 */

/**
 * Basic string sanitization - removes dangerous characters
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') return '';
  
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+\s*=/gi, '') // Remove event handlers
    .trim();
}

/**
 * Sanitize email addresses
 */
export function sanitizeEmail(email: string): string {
  if (typeof email !== 'string') return '';
  
  // Basic email validation and sanitization
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const sanitized = email.toLowerCase().trim();
  
  return emailRegex.test(sanitized) ? sanitized : '';
}

/**
 * Sanitize phone numbers
 */
export function sanitizePhone(phone: string): string {
  if (typeof phone !== 'string') return '';
  
  // Remove all non-digit characters except + at the start
  return phone.replace(/[^\d+]/g, '').replace(/(?!^)\+/g, '');
}

/**
 * Sanitize URLs
 */
export function sanitizeUrl(url: string): string {
  if (typeof url !== 'string') return '';
  
  try {
    const urlObj = new URL(url);
    // Only allow http and https protocols
    if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
      return '';
    }
    return urlObj.toString();
  } catch {
    return '';
  }
}

/**
 * Recursively sanitize an object
 */
export function sanitizeInput(input: any): any {
  if (input === null || input === undefined) {
    return input;
  }
  
  if (typeof input === 'string') {
    return sanitizeString(input);
  }
  
  if (typeof input === 'number' || typeof input === 'boolean') {
    return input;
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeInput);
  }
  
  if (typeof input === 'object') {
    const sanitized: Record<string, any> = {};
    for (const [key, value] of Object.entries(input)) {
      const sanitizedKey = sanitizeString(key);
      if (sanitizedKey) {
        sanitized[sanitizedKey] = sanitizeInput(value);
      }
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Validate and sanitize common field types
 */
export const validators = {
  email: (value: string) => {
    const sanitized = sanitizeEmail(value);
    return sanitized ? { isValid: true, value: sanitized } : { isValid: false, value: '' };
  },
  
  phone: (value: string) => {
    const sanitized = sanitizePhone(value);
    return sanitized ? { isValid: true, value: sanitized } : { isValid: false, value: '' };
  },
  
  url: (value: string) => {
    const sanitized = sanitizeUrl(value);
    return sanitized ? { isValid: true, value: sanitized } : { isValid: false, value: '' };
  },
  
  text: (value: string) => {
    const sanitized = sanitizeString(value);
    return { isValid: true, value: sanitized };
  }
};