import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';
import { NextRequest } from 'next/server';

// Create Redis client - fallback to memory if Redis not available
const redis = process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN
  ? new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL,
      token: process.env.UPSTASH_REDIS_REST_TOKEN,
    })
  : undefined;

// In-memory fallback for development
class MemoryStore {
  private store = new Map<string, { count: number; reset: number }>();

  async get(key: string) {
    const item = this.store.get(key);
    if (!item || Date.now() > item.reset) {
      return null;
    }
    return item.count;
  }

  async set(key: string, count: number, ttl: number) {
    this.store.set(key, { count, reset: Date.now() + ttl * 1000 });
  }

  async incr(key: string) {
    const item = this.store.get(key);
    if (!item || Date.now() > item.reset) {
      this.store.set(key, { count: 1, reset: Date.now() + 60 * 1000 });
      return 1;
    }
    item.count++;
    return item.count;
  }
}

const memoryStore = new MemoryStore();

// Rate limit configurations
export const rateLimits = {
  // API endpoints
  api: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(100, '1 m'), // 100 requests per minute
    analytics: true,
  }),

  // Authentication endpoints (stricter)
  auth: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(5, '1 m'), // 5 attempts per minute
    analytics: true,
  }),

  // File upload endpoints
  upload: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(10, '1 m'), // 10 uploads per minute
    analytics: true,
  }),

  // Search endpoints
  search: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(50, '1 m'), // 50 searches per minute
    analytics: true,
  }),

  // Enquiry/contact forms
  contact: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(3, '1 m'), // 3 enquiries per minute
    analytics: true,
  }),

  // Admin endpoints (very strict)
  admin: new Ratelimit({
    redis: redis || memoryStore as any,
    limiter: Ratelimit.slidingWindow(30, '1 m'), // 30 requests per minute
    analytics: true,
  }),
};

// Get client IP address
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const cfConnectingIP = request.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return request.ip || 'unknown';
}

// Rate limiting utility function
export async function checkRateLimit(
  request: NextRequest,
  type: keyof typeof rateLimits,
  identifier?: string
) {
  const ip = getClientIP(request);
  const key = identifier || ip;
  
  try {
    const result = await rateLimits[type].limit(key);
    
    return {
      success: result.success,
      limit: result.limit,
      remaining: result.remaining,
      reset: result.reset,
      retryAfter: result.reset ? Math.round((result.reset - Date.now()) / 1000) : 0,
    };
  } catch (error) {
    console.error('Rate limiting error:', error);
    // Fail open - allow request if rate limiting fails
    return {
      success: true,
      limit: 100,
      remaining: 99,
      reset: Date.now() + 60000,
      retryAfter: 0,
    };
  }
}

// Rate limiting response headers
export function getRateLimitHeaders(result: {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}) {
  const headers = new Headers();
  
  headers.set('X-RateLimit-Limit', result.limit.toString());
  headers.set('X-RateLimit-Remaining', Math.max(0, result.remaining).toString());
  headers.set('X-RateLimit-Reset', result.reset.toString());
  
  if (result.retryAfter && result.retryAfter > 0) {
    headers.set('Retry-After', result.retryAfter.toString());
  }
  
  return headers;
}

// Middleware wrapper for API routes
export function withRateLimit(
  handler: (request: NextRequest) => Promise<Response>,
  type: keyof typeof rateLimits = 'api',
  getIdentifier?: (request: NextRequest) => string | Promise<string>
) {
  return async (request: NextRequest) => {
    try {
      const identifier = getIdentifier ? await getIdentifier(request) : undefined;
      const result = await checkRateLimit(request, type, identifier);
      
      if (!result.success) {
        const headers = getRateLimitHeaders(result);
        return new Response(
          JSON.stringify({
            error: 'Too many requests',
            message: 'Rate limit exceeded. Please try again later.',
            retryAfter: result.retryAfter,
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              ...Object.fromEntries(headers.entries()),
            },
          }
        );
      }
      
      const response = await handler(request);
      const headers = getRateLimitHeaders(result);
      
      // Add rate limit headers to successful responses
      headers.forEach((value, key) => {
        response.headers.set(key, value);
      });
      
      return response;
    } catch (error) {
      console.error('Rate limiting middleware error:', error);
      // Fail open - continue with original handler
      return handler(request);
    }
  };
}