# Armaan Sharma Properties - API Documentation

## Overview
This document provides comprehensive documentation for all API endpoints and database schema for the Armaan Sharma Properties website.

**Base URL:** `http://localhost:3000/api` (Development)
**Authentication:** NextAuth.js with JWT sessions
**Database:** PostgreSQL with Dr<PERSON>zle ORM

---

## Database Schema

### 1. Users Table (`users`)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password TEXT,                     -- For email/password auth
  name TEXT NOT NULL,
  phone VARCHAR(20),
  role VARCHAR(20) DEFAULT 'user' NOT NULL,  -- 'admin', 'agent', 'user'
  email_verified BOOLEAN DEFAULT false,
  image TEXT,
  provider VARCHAR(50) DEFAULT 'credentials',  -- 'google', 'facebook', 'credentials'
  provider_id TEXT,
  is_active BOOLEAN DEFAULT true,
  last_login TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX users_email_idx ON users(email);
CREATE INDEX users_role_idx ON users(role);
```

### 2. Properties Table (`properties`)
```sql
CREATE TABLE properties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  price DECIMAL(15,2) NOT NULL,
  price_per_sqft DECIMAL(10,2),
  
  -- Location details
  area TEXT NOT NULL,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  address TEXT NOT NULL,
  pincode VARCHAR(10),
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),
  
  -- Property specifications
  bedrooms INTEGER,
  bathrooms INTEGER,
  total_area INTEGER,              -- in sqft
  carpet_area INTEGER,             -- in sqft
  built_up_area INTEGER,           -- in sqft
  floors INTEGER,
  total_floors INTEGER,
  parking INTEGER DEFAULT 0,
  balconies INTEGER DEFAULT 0,
  furnished VARCHAR(20) DEFAULT 'unfurnished',  -- 'furnished', 'semi-furnished', 'unfurnished'
  age_of_property INTEGER,         -- in years
  facing VARCHAR(20),              -- 'north', 'south', 'east', 'west'
  
  -- Property details
  property_type VARCHAR(50) NOT NULL,  -- 'apartment', 'house', 'villa', 'plot', etc.
  sub_type VARCHAR(50),            -- 'studio', '1bhk', '2bhk', etc.
  listing_type VARCHAR(10) NOT NULL,   -- 'sale' or 'rent'
  status VARCHAR(20) DEFAULT 'available' NOT NULL,  -- 'available', 'sold', 'rented', 'under-negotiation'
  
  -- Features and amenities
  amenities JSONB DEFAULT '[]',    -- Array of amenity strings
  features JSONB DEFAULT '[]',     -- Array of feature strings
  nearby_places JSONB DEFAULT '[]', -- Array of nearby place objects
  
  -- Media
  images JSONB DEFAULT '[]',       -- Array of image URLs
  videos JSONB DEFAULT '[]',       -- Array of video URLs
  documents JSONB DEFAULT '[]',    -- Array of document URLs
  virtual_tour_url TEXT,
  
  -- Contact and ownership
  owner_id UUID REFERENCES users(id),
  agent_id UUID REFERENCES users(id),
  contact_name TEXT,
  contact_phone VARCHAR(20),
  contact_email VARCHAR(255),
  contact_whatsapp VARCHAR(20),
  
  -- Marketing
  featured BOOLEAN DEFAULT false,
  verified BOOLEAN DEFAULT false,
  published BOOLEAN DEFAULT true,
  views INTEGER DEFAULT 0,
  enquiries INTEGER DEFAULT 0,
  
  -- SEO
  meta_title TEXT,
  meta_description TEXT,
  meta_keywords TEXT,
  
  -- Timestamps
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL,
  published_at TIMESTAMP,
  sold_at TIMESTAMP
);

-- Indexes
CREATE INDEX properties_slug_idx ON properties(slug);
CREATE INDEX properties_city_idx ON properties(city);
CREATE INDEX properties_area_idx ON properties(area);
CREATE INDEX properties_type_idx ON properties(property_type);
CREATE INDEX properties_listing_type_idx ON properties(listing_type);
CREATE INDEX properties_status_idx ON properties(status);
CREATE INDEX properties_price_idx ON properties(price);
CREATE INDEX properties_featured_idx ON properties(featured);
CREATE INDEX properties_published_idx ON properties(published);
CREATE INDEX properties_location_idx ON properties(latitude, longitude);
```

### 3. Property Images Table (`property_images`)
```sql
CREATE TABLE property_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  url TEXT NOT NULL,
  alt TEXT,
  caption TEXT,
  order_num INTEGER DEFAULT 1,
  type VARCHAR(20) DEFAULT 'photo',  -- 'photo', 'floorplan', 'map'
  is_featured BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX property_images_property_idx ON property_images(property_id);
CREATE INDEX property_images_order_idx ON property_images(property_id, order_num);
```

### 4. Enquiries Table (`enquiries`)
```sql
CREATE TABLE enquiries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id) NOT NULL,
  user_id UUID REFERENCES users(id),
  
  -- Contact details
  name TEXT NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  message TEXT,
  
  -- Enquiry details
  enquiry_type VARCHAR(20) DEFAULT 'general',  -- 'general', 'viewing', 'price', 'loan'
  budget DECIMAL(15,2),
  status VARCHAR(20) DEFAULT 'new',  -- 'new', 'contacted', 'qualified', 'closed'
  source VARCHAR(50) DEFAULT 'website',  -- 'website', 'phone', 'whatsapp', 'email'
  
  -- Follow-up
  assigned_to UUID REFERENCES users(id),
  follow_up_date TIMESTAMP,
  notes TEXT,
  
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX enquiries_property_idx ON enquiries(property_id);
CREATE INDEX enquiries_status_idx ON enquiries(status);
CREATE INDEX enquiries_email_idx ON enquiries(email);
```

### 5. Favorites Table (`favorites`)
```sql
CREATE TABLE favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  
  UNIQUE(user_id, property_id)
);

-- Indexes
CREATE INDEX favorites_user_property_idx ON favorites(user_id, property_id);
```

### 6. Saved Searches Table (`saved_searches`)
```sql
CREATE TABLE saved_searches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  search_params JSONB NOT NULL,   -- Store search filters as JSON
  email_alerts BOOLEAN DEFAULT true,
  frequency VARCHAR(20) DEFAULT 'daily',  -- 'daily', 'weekly', 'monthly'
  last_sent TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX saved_searches_user_idx ON saved_searches(user_id);
```

### 7. Property Views Table (`property_views`)
```sql
CREATE TABLE property_views (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id) ON DELETE CASCADE NOT NULL,
  user_id UUID REFERENCES users(id),
  ip_address VARCHAR(45),
  user_agent TEXT,
  referrer TEXT,
  viewed_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Indexes
CREATE INDEX property_views_property_idx ON property_views(property_id);
CREATE INDEX property_views_user_idx ON property_views(user_id);
CREATE INDEX property_views_date_idx ON property_views(viewed_at);
```

---

## API Endpoints

### Authentication Endpoints

#### 1. NextAuth.js Endpoints
- **POST** `/api/auth/[...nextauth]` - NextAuth.js dynamic route
  - Handles: signin, signout, callback, csrf, session, providers
  - **Body**: Varies by endpoint
  - **Response**: Varies by endpoint

#### 2. User Registration
- **POST** `/api/auth/register`
  - **Description**: Register a new user
  - **Body**:
    ```json
    {
      "name": "string",
      "email": "string",
      "password": "string",
      "phone": "string (optional)"
    }
    ```
  - **Response**: 
    ```json
    {
      "success": true,
      "message": "User registered successfully",
      "user": {
        "id": "uuid",
        "email": "string",
        "name": "string"
      }
    }
    ```

### Property Endpoints

#### 1. Get Properties
- **GET** `/api/properties`
  - **Description**: Get paginated list of properties with filters
  - **Query Parameters**:
    - `page` (number): Page number (default: 1)
    - `limit` (number): Items per page (default: 12)
    - `search` (string): Search term
    - `location` (string): Filter by location
    - `propertyType` (string): Filter by property type
    - `listingType` (enum): 'sale' | 'rent'
    - `minPrice` (number): Minimum price
    - `maxPrice` (number): Maximum price
    - `bedrooms` (number): Number of bedrooms
    - `bathrooms` (number): Number of bathrooms
    - `minArea` (number): Minimum area in sqft
    - `maxArea` (number): Maximum area in sqft
    - `furnished` (enum): 'furnished' | 'semi-furnished' | 'unfurnished'
    - `amenities` (array): List of amenities
    - `sortBy` (enum): 'createdAt' | 'price' | 'totalArea' | 'views'
    - `sortOrder` (enum): 'asc' | 'desc'
  - **Response**:
    ```json
    {
      "properties": [Property[]],
      "pagination": {
        "page": 1,
        "limit": 12,
        "total": 100,
        "totalPages": 9
      }
    }
    ```

#### 2. Get Property by Slug
- **GET** `/api/properties/[slug]`
  - **Description**: Get detailed property information
  - **Parameters**: `slug` (string)
  - **Response**: Property object with full details

#### 3. Create Property (Admin Only)
- **POST** `/api/admin/properties`
  - **Auth Required**: Admin role
  - **Description**: Create a new property
  - **Body**: Property object (see schema)
  - **Response**: Created property object

#### 4. Update Property (Admin Only)
- **PUT** `/api/admin/properties/[id]`
  - **Auth Required**: Admin role
  - **Parameters**: `id` (uuid)
  - **Body**: Partial property object
  - **Response**: Updated property object

#### 5. Delete Property (Admin Only)
- **DELETE** `/api/admin/properties/[id]`
  - **Auth Required**: Admin role
  - **Parameters**: `id` (uuid)
  - **Response**: Success message

### Enquiry Endpoints

#### 1. Submit Enquiry
- **POST** `/api/enquiries`
  - **Description**: Submit a property enquiry
  - **Body**:
    ```json
    {
      "propertyId": "uuid",
      "name": "string",
      "email": "string",
      "phone": "string",
      "message": "string",
      "enquiryType": "general | viewing | price | loan",
      "budget": "number (optional)"
    }
    ```
  - **Response**:
    ```json
    {
      "success": true,
      "message": "Enquiry submitted successfully",
      "enquiry": { "id": "uuid", ... }
    }
    ```

### Favorites Endpoints

#### 1. Get User Favorites
- **GET** `/api/favorites`
  - **Auth Required**: Yes
  - **Description**: Get user's favorite properties
  - **Response**: Array of favorite properties

#### 2. Add/Remove Favorite
- **POST** `/api/favorites`
  - **Auth Required**: Yes
  - **Body**:
    ```json
    {
      "propertyId": "uuid",
      "action": "add | remove"
    }
    ```
  - **Response**: Success message

#### 3. Check Favorite Status
- **GET** `/api/favorites/check?propertyId=uuid`
  - **Auth Required**: Yes
  - **Query**: `propertyId` (uuid)
  - **Response**: `{ "isFavorite": boolean }`

### Saved Searches Endpoints

#### 1. Get Saved Searches
- **GET** `/api/saved-searches`
  - **Auth Required**: Yes
  - **Response**: Array of user's saved searches

#### 2. Create Saved Search
- **POST** `/api/saved-searches`
  - **Auth Required**: Yes
  - **Body**:
    ```json
    {
      "name": "string",
      "searchParams": "object",
      "emailAlerts": "boolean",
      "frequency": "daily | weekly | monthly"
    }
    ```

#### 3. Update Saved Search
- **PUT** `/api/saved-searches/[id]`
  - **Auth Required**: Yes
  - **Parameters**: `id` (uuid)
  - **Body**: Partial saved search object

#### 4. Delete Saved Search
- **DELETE** `/api/saved-searches/[id]`
  - **Auth Required**: Yes
  - **Parameters**: `id` (uuid)

### Admin Endpoints

#### 1. Admin Dashboard
- **GET** `/api/admin/dashboard`
  - **Auth Required**: Admin role
  - **Description**: Get admin dashboard statistics
  - **Response**:
    ```json
    {
      "stats": {
        "totalProperties": 150,
        "totalUsers": 1250,
        "totalEnquiries": 89,
        "monthlyViews": 5420
      },
      "recentActivity": [...]
    }
    ```

#### 2. Admin Settings
- **GET** `/api/admin/settings`
- **PUT** `/api/admin/settings`
  - **Auth Required**: Admin role
  - **Description**: Get/Update website settings

#### 3. Property Seeding (Temporarily Disabled)
- **POST** `/api/admin/seed`
  - **Auth Required**: Admin role
  - **Status**: Currently returns 503 (Service Unavailable)
  - **Note**: Disabled due to TypeScript compatibility issues

### Utility Endpoints

#### 1. File Upload
- **POST** `/api/upload`
  - **Auth Required**: Yes
  - **Content-Type**: multipart/form-data
  - **Body**: File(s)
  - **Response**: Upload results with URLs

#### 2. CSRF Token
- **GET** `/api/csrf-token`
  - **Description**: Get CSRF token for forms
  - **Response**: `{ "csrfToken": "string" }`

#### 3. Email Alerts
- **POST** `/api/email-alerts/send`
  - **Auth Required**: Admin role
  - **Description**: Send email alerts to users

#### 4. Unsubscribe
- **POST** `/api/unsubscribe`
  - **Description**: Unsubscribe from email alerts
  - **Body**: `{ "token": "string" }`

### SEO Endpoints

#### 1. Sitemap
- **GET** `/api/sitemap`
  - **Description**: Generate XML sitemap
  - **Response**: XML sitemap

#### 2. Robots.txt
- **GET** `/api/robots`
  - **Description**: Generate robots.txt
  - **Response**: Plain text robots.txt

### AI Integration Endpoints

#### 1. AI Property Search
- **POST** `/api/ai/search`
  - **Description**: AI-powered property search
  - **Body**: Search query object
  - **Response**: Matching properties with AI insights

#### 2. AI Property Recommendations
- **GET** `/api/ai/properties`
  - **Description**: Get AI-powered property recommendations
  - **Query**: User preferences
  - **Response**: Recommended properties

---

## Error Handling

All API endpoints follow consistent error response format:

```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": "ERROR_CODE",
    "details": {} // Additional error details
  }
}
```

### Common HTTP Status Codes
- **200**: Success
- **201**: Created
- **400**: Bad Request (Validation errors)
- **401**: Unauthorized (Authentication required)
- **403**: Forbidden (Insufficient permissions)
- **404**: Not Found
- **409**: Conflict (Duplicate data)
- **422**: Unprocessable Entity (Validation failed)
- **500**: Internal Server Error
- **503**: Service Unavailable

---

## Authentication Flow

1. **Login**: POST to `/api/auth/signin/credentials`
2. **Session**: Managed by NextAuth.js with JWT
3. **Protected Routes**: Use `getServerSession(authConfig)` on server-side
4. **Client-side**: Use `useSession()` hook from next-auth/react

---

## Rate Limiting

- **Global**: 100 requests per 15 minutes per IP
- **API Routes**: 50 requests per 15 minutes per IP  
- **Auth Routes**: 10 requests per 15 minutes per IP
- **Upload**: 5 requests per 15 minutes per user

---

## Development Notes

### Environment Variables Required
```env
DATABASE_URL=postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
```

### Database Connection
- Uses Drizzle ORM with PostgreSQL
- Connection pooling enabled
- Migrations managed through Drizzle Kit

### Security Features
- CSRF protection on all forms
- Input sanitization and validation
- Rate limiting per IP and user
- SQL injection prevention through ORM
- XSS protection through sanitization

---

*Last Updated: January 2025*
*Version: 1.0*