module.exports = {
  apps: [
    {
      name: 'property-trendz',
      script: 'npm',
      args: 'start',
      instances: 'max', // Use all available CPU cores for better performance
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      // Logging configuration
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Auto restart and monitoring
      autorestart: true,
      watch: false, // Disable in production for performance
      max_memory_restart: '2G', // Increased for production

      // Health monitoring
      min_uptime: '10s',
      max_restarts: 10, // Increased for production stability
      restart_delay: 4000,

      // Environment variables from file
      env_file: '.env.production',

      // Node.js options for production
      node_args: '--max-old-space-size=2048 --optimize-for-size',

      // Process management
      kill_timeout: 5000,
      wait_ready: true,
      listen_timeout: 10000,

      // Production-specific settings
      ignore_watch: ['node_modules', 'logs', '.git'],
      time: true,
    }
  ],

  // Deployment configuration
  deploy: {
    production: {
      user: 'deploy',
      host: ['property-trendz.com'], // Replace with your actual server IP
      ref: 'origin/main',
      repo: 'https://github.com/your-username/Properties-Trends.git', // Update with your actual repo
      path: '/var/www/property-trendz',
      'pre-deploy-local': '',
      'post-deploy': 'npm ci --only=production && npm run build && pm2 reload ecosystem.config.js --env production',
      'pre-setup': 'mkdir -p /var/www/property-trendz/logs',
      'ssh_options': 'ForwardAgent=yes'
    }
  }
};