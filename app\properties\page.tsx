'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, Grid3X3, List, MapPin, Bed, Bath, Maximize, Phone, MessageCircle, Heart } from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { AdvancedSearch } from '@/components/search/AdvancedSearch'
import { Pagination } from '@/components/ui/Pagination'
import { FavoriteButton } from '@/components/ui/FavoriteButton'

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } }
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

interface SearchFilters {
  search: string;
  location: string;
  propertyType: string;
  listingType: string;
  minPrice: string;
  maxPrice: string;
  bedrooms: string;
  bathrooms: string;
  minArea: string;
  maxArea: string;
  furnished: string;
  ageOfProperty: string;
  amenities: string[];
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

export default function PropertiesPage() {
  const [properties, setProperties] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    hasNext: false,
    hasPrev: false,
  })
  const [filters, setFilters] = useState<SearchFilters>({
    search: '',
    location: '',
    propertyType: '',
    listingType: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    bathrooms: '',
    minArea: '',
    maxArea: '',
    furnished: '',
    ageOfProperty: '',
    amenities: [],
    sortBy: 'createdAt',
    sortOrder: 'desc',
  })

  const fetchProperties = useCallback(async (page = 1) => {
    setLoading(true)
    try {
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', '12')
      
      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== '' && !(Array.isArray(value) && value.length === 0)) {
          if (Array.isArray(value)) {
            params.append(key, value.join(','))
          } else {
            params.append(key, value.toString())
          }
        }
      })
      
      const response = await fetch(`/api/properties?${params}`)
      const data = await response.json()
      
      if (response.ok) {
        setProperties(data.properties)
        setPagination(data.pagination)
      } else {
        console.error('Error fetching properties:', data.message)
      }
    } catch (error) {
      console.error('Error fetching properties:', error)
    } finally {
      setLoading(false)
    }
  }, [filters])

  useEffect(() => {
    fetchProperties(1)
  }, [fetchProperties])

  const handleFiltersChange = useCallback((newFilters: SearchFilters) => {
    setFilters(newFilters)
  }, [])

  const handlePageChange = (page: number) => {
    fetchProperties(page)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const formatPrice = (price: number, listingType: string) => {
    if (listingType === 'rent') {
      return `₹${price.toLocaleString('en-IN')}/month`
    }
    if (price >= 10000000) {
      return `₹${(price / 10000000).toFixed(1)} Cr`
    }
    if (price >= 100000) {
      return `₹${(price / 100000).toFixed(1)} L`
    }
    return `₹${price.toLocaleString('en-IN')}`
  }

  const PropertyCard = ({ property }: { property: any }) => {
    return (
      <div className="bg-white rounded-xl shadow-card overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative h-40 sm:h-44 md:h-48">
          <Image
            src={property.images?.[0] || '/placeholder-property.svg'}
            alt={property.title}
            fill
            className="object-cover"
          />
          <div className="absolute top-2 md:top-4 left-2 md:left-4 flex gap-1 md:gap-2">
            {property.featured && (
              <span className="bg-secondary-600 text-white px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-semibold">
                Featured
              </span>
            )}
            <span className="bg-primary-600 text-white px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-semibold capitalize">
              {property.listingType}
            </span>
          </div>
          <div className="absolute top-2 md:top-4 right-2 md:right-4">
            {property.id && <FavoriteButton propertyId={property.id} />}
          </div>
        </div>

        <div className="p-3 md:p-6">
          <Link href={`/properties/${property.slug}`}>
            <h3 className="text-lg md:text-xl font-semibold text-neutral-800 hover:text-primary-600 transition-colors mb-2 line-clamp-2">
              {property.title}
            </h3>
          </Link>

          <div className="flex items-center text-neutral-600 mb-3">
            <MapPin className="w-3 md:w-4 h-3 md:h-4 mr-1" />
            <span className="text-xs md:text-sm">{property.area}, {property.city}</span>
          </div>

          <div className="flex items-center gap-2 md:gap-4 text-xs md:text-sm text-neutral-600 mb-4">
            {property.bedrooms && (
              <div className="flex items-center">
                <Bed className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                <span className="hidden sm:inline">{property.bedrooms} Beds</span>
                <span className="sm:hidden">{property.bedrooms}</span>
              </div>
            )}
            {property.bathrooms && (
              <div className="flex items-center">
                <Bath className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                <span className="hidden sm:inline">{property.bathrooms} Baths</span>
                <span className="sm:hidden">{property.bathrooms}</span>
              </div>
            )}
            {property.totalArea && (
              <div className="flex items-center">
                <Maximize className="w-3 md:w-4 h-3 md:h-4 mr-1" />
                <span className="hidden sm:inline">{property.totalArea} sq ft</span>
                <span className="sm:hidden">{property.totalArea}</span>
              </div>
            )}
          </div>

          <div className="flex justify-between items-center">
            <div>
              <div className="text-lg md:text-2xl font-bold text-primary-600">
                {formatPrice(property.price, property.listingType)}
              </div>
              {property.pricePerSqft && (
                <div className="text-xs md:text-sm text-neutral-500">
                  ₹{property.pricePerSqft}/sq ft
                </div>
              )}
            </div>
            <div className="flex gap-1 md:gap-2">
              <button className="p-1.5 md:p-2 text-neutral-400 hover:text-primary-600 transition-colors">
                <Phone className="w-3 md:w-4 h-3 md:h-4" />
              </button>
              <button className="p-1.5 md:p-2 text-neutral-400 hover:text-green-600 transition-colors">
                <MessageCircle className="w-3 md:w-4 h-3 md:h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-neutral-50 pt-16">
      {/* Simple Header */}
      <div className="bg-white border-b">
        <div className="container-custom py-6">
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-display font-bold text-neutral-800">
                Properties in Delhi NCR
              </h1>
              <p className="text-neutral-600 mt-1">
                {pagination.totalCount} properties found
              </p>
            </div>

            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-lg ${viewMode === 'grid' ? 'bg-primary-600 text-white' : 'bg-neutral-200 text-neutral-600'}`}
                >
                  <Grid3X3 className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-lg ${viewMode === 'list' ? 'bg-primary-600 text-white' : 'bg-neutral-200 text-neutral-600'}`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="container-custom py-6">
        <AdvancedSearch onFiltersChange={handleFiltersChange} />
      </div>

      {/* Properties Grid */}
      <div className="container-custom section-padding">
        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4 md:gap-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-card overflow-hidden animate-pulse">
                <div className="h-40 sm:h-44 md:h-48 bg-neutral-200"></div>
                <div className="p-4 md:p-6">
                  <div className="h-4 bg-neutral-200 rounded mb-2"></div>
                  <div className="h-4 bg-neutral-200 rounded w-3/4 mb-4"></div>
                  <div className="flex gap-2 md:gap-4 mb-4">
                    <div className="h-3 bg-neutral-200 rounded w-12 md:w-16"></div>
                    <div className="h-3 bg-neutral-200 rounded w-12 md:w-16"></div>
                    <div className="h-3 bg-neutral-200 rounded w-12 md:w-16"></div>
                  </div>
                  <div className="h-6 bg-neutral-200 rounded w-20 md:w-24"></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 gap-4 md:gap-6">
            {properties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
        )}

        {!loading && properties.length === 0 && (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🏠</div>
            <h3 className="text-xl font-semibold text-neutral-800 mb-2">
              No properties found
            </h3>
            <p className="text-neutral-600">
              Try adjusting your search criteria or browse all properties.
            </p>
          </div>
        )}

        {/* Pagination */}
        {!loading && properties.length > 0 && pagination.totalPages > 1 && (
          <div className="mt-12">
            <Pagination
              currentPage={pagination.currentPage}
              totalPages={pagination.totalPages}
              totalCount={pagination.totalCount}
              hasNext={pagination.hasNext}
              hasPrev={pagination.hasPrev}
              onPageChange={handlePageChange}
            />
          </div>
        )}
      </div>
    </div>
  )
} 