import { NextRequest, NextResponse } from 'next/server';
import { getServerSession, authConfig } from '@/lib/auth/auth';
import { db } from '@/lib/db/config';
import { users } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

// GET - Get single user
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const user = await db
      .select({
        id: users.id,
        email: users.email,
        name: users.name,
        phone: users.phone,
        role: users.role,
        emailVerified: users.emailVerified,
        image: users.image,
        provider: users.provider,
        providerId: users.providerId,
        isActive: users.isActive,
        lastLogin: users.lastLogin,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .where(eq(users.id, params.id))
      .limit(1);

    if (!user[0]) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      user: user[0],
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userData = await request.json();
    const { name, email, phone, role, isActive, emailVerified } = userData;

    // Normalize email if provided
    const normalizedEmail = email ? email.toLowerCase().trim() : null;

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.id, params.id))
      .limit(1);

    if (!existingUser[0]) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent admin from deactivating themselves
    if (session.user.id === params.id && isActive === false) {
      return NextResponse.json(
        { message: 'You cannot deactivate your own account' },
        { status: 400 }
      );
    }

    // Prevent admin from changing their own role
    if (session.user.id === params.id && role !== existingUser[0].role) {
      return NextResponse.json(
        { message: 'You cannot change your own role' },
        { status: 400 }
      );
    }

    // Check if email is already taken by another user (case-insensitive)
    if (normalizedEmail && normalizedEmail !== existingUser[0].email) {
      const emailExists = await db
        .select()
        .from(users)
        .where(eq(users.email, normalizedEmail))
        .limit(1);

      if (emailExists[0]) {
        return NextResponse.json(
          { message: 'Email is already taken by another user' },
          { status: 409 }
        );
      }
    }

    // Update user with normalized email
    const updatedUser = await db
      .update(users)
      .set({
        name: name || existingUser[0].name,
        email: normalizedEmail || existingUser[0].email,
        phone: phone !== undefined ? phone : existingUser[0].phone,
        role: role || existingUser[0].role,
        isActive: isActive !== undefined ? isActive : existingUser[0].isActive,
        emailVerified: emailVerified !== undefined ? emailVerified : existingUser[0].emailVerified,
        updatedAt: new Date(),
      })
      .where(eq(users.id, params.id))
      .returning({
        id: users.id,
        email: users.email,
        name: users.name,
        phone: users.phone,
        role: users.role,
        emailVerified: users.emailVerified,
        isActive: users.isActive,
        updatedAt: users.updatedAt,
      });

    return NextResponse.json({
      message: 'User updated successfully',
      user: updatedUser[0],
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete user
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authConfig) as any;

    if (!session || session.user.role !== 'admin') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Prevent admin from deleting themselves
    if (session.user.id === params.id) {
      return NextResponse.json(
        { message: 'You cannot delete your own account' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db
      .select()
      .from(users)
      .where(eq(users.id, params.id))
      .limit(1);

    if (!existingUser[0]) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    await db
      .delete(users)
      .where(eq(users.id, params.id));

    return NextResponse.json({
      message: 'User deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
