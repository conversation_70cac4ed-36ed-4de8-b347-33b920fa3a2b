'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Mail, CheckCircle, XCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

export function UnsubscribePage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'invalid'>('loading');
  const [message, setMessage] = useState('');
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      setStatus('invalid');
      setMessage('Invalid unsubscribe link');
      return;
    }

    unsubscribeFromAlerts();
  }, [token]);

  const unsubscribeFromAlerts = async () => {
    try {
      const response = await fetch('/api/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token }),
      });

      const data = await response.json();

      if (response.ok) {
        setStatus('success');
        setMessage(data.message || 'Successfully unsubscribed from email alerts');
      } else {
        setStatus('error');
        setMessage(data.error || 'Failed to unsubscribe');
      }
    } catch (error) {
      setStatus('error');
      setMessage('Something went wrong. Please try again.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-card p-8 text-center">
        {status === 'loading' && (
          <>
            <LoadingSpinner />
            <h2 className="text-xl font-semibold text-gray-900 mt-4">
              Processing Unsubscribe Request
            </h2>
            <p className="text-gray-600 mt-2">
              Please wait while we process your request...
            </p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="w-16 h-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Successfully Unsubscribed
            </h2>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="space-y-3">
              <Link href="/properties" className="btn-primary w-full inline-block text-center">
                Browse Properties
              </Link>
              <Link href="/auth/signin" className="btn-outline w-full inline-flex items-center justify-center">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Sign In to Manage Preferences
              </Link>
            </div>
          </>
        )}

        {status === 'error' && (
          <>
            <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Unsubscribe Failed
            </h2>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="space-y-3">
              <Button onClick={unsubscribeFromAlerts} className="w-full">
                Try Again
              </Button>
              <Link href="/contact" className="btn-outline w-full inline-block text-center">
                Contact Support
              </Link>
            </div>
          </>
        )}

        {status === 'invalid' && (
          <>
            <Mail className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Invalid Unsubscribe Link
            </h2>
            <p className="text-gray-600 mb-6">
              {message}
            </p>
            <div className="space-y-3">
              <Link href="/saved-searches" className="btn-primary w-full inline-block text-center">
                Manage Email Preferences
              </Link>
              <Link href="/contact" className="btn-outline w-full inline-block text-center">
                Contact Support
              </Link>
            </div>
          </>
        )}

        <div className="mt-8 pt-6 border-t border-gray-200">
          <p className="text-xs text-gray-500">
            You can always manage your email preferences by signing in to your account.
          </p>
        </div>
      </div>
    </div>
  );
}