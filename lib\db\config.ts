import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

// Environment-based database URL configuration
interface DatabaseConfig {
  url: string;
  environment: 'development' | 'production' | 'test';
  isDocker: boolean;
}

/**
 * Centralized database URL management system
 * Automatically routes to correct database based on environment
 */
function getDatabaseConfig(): DatabaseConfig {
  const nodeEnv = process.env.NODE_ENV || 'development';

  // Define database URLs for different environments
  const DATABASE_URLS = {
    // Development: Docker containerized PostgreSQL
    development: 'postgresql://armaan_user:armaan_secure_password@localhost:5432/armaan_properties',

    // Production: PostgreSQL on production server
    production: 'postgresql://yash123414:<EMAIL>:5432/armaan_properties',

    // Test: Use development database or separate test database
    test: 'postgresql://armaan_user:armaan_secure_password@localhost:5432/armaan_properties_test'
  };

  // Determine environment based on multiple factors
  let environment: 'development' | 'production' | 'test';
  let databaseUrl: string;

  // Check if we're explicitly in production
  if (nodeEnv === 'production') {
    environment = 'production';
    databaseUrl = process.env.DATABASE_URL || DATABASE_URLS.production;
  }
  // Check if we're in test environment
  else if (nodeEnv === 'test') {
    environment = 'test';
    databaseUrl = process.env.DATABASE_URL || DATABASE_URLS.test;
  }
  // Default to development (includes when NODE_ENV is 'development' or undefined)
  else {
    environment = 'development';
    databaseUrl = process.env.DATABASE_URL || DATABASE_URLS.development;
  }

  // Additional check: if .env.production exists and is being used, force production
  if (process.env.NEXTAUTH_URL === 'https://property-trendz.com' && environment !== 'production') {
    environment = 'production';
    databaseUrl = DATABASE_URLS.production;
  }

  // Determine if using Docker (development environment indicator)
  const isDocker = environment === 'development' || databaseUrl.includes('localhost:5432');

  return {
    url: databaseUrl,
    environment,
    isDocker
  };
}

// Get database configuration
const dbConfig = getDatabaseConfig();

// Validate database URL
if (!dbConfig.url) {
  throw new Error(`DATABASE_URL is required for ${dbConfig.environment} environment`);
}

// Parse and validate URL format
let parsedUrl: URL;
try {
  parsedUrl = new URL(dbConfig.url);
} catch (error) {
  throw new Error(`Invalid DATABASE_URL format for ${dbConfig.environment} environment: ${error}`);
}

// Environment-specific connection options
const getConnectionOptions = (config: DatabaseConfig) => {
  const baseOptions = {
    max: config.environment === 'production' ? 20 : 10,
    idle_timeout: config.environment === 'production' ? 30 : 20,
    connect_timeout: 10,
    // SSL configuration based on environment
    ssl: config.environment === 'production' ? { rejectUnauthorized: false } : false,
  };

  // Additional production optimizations
  if (config.environment === 'production') {
    return {
      ...baseOptions,
      max_lifetime: 60 * 30, // 30 minutes
      prepare: false, // Disable prepared statements for better compatibility
    };
  }

  return baseOptions;
};

const connectionOptions = getConnectionOptions(dbConfig);

// Create postgres client with environment-specific configuration
const client = postgres(dbConfig.url, connectionOptions);

// Create Drizzle instance
export const db = drizzle(client, { schema });

// Export database configuration for other modules
export const databaseConfig = {
  url: dbConfig.url,
  environment: dbConfig.environment,
  isDocker: dbConfig.isDocker,
  host: parsedUrl.hostname,
  port: parseInt(parsedUrl.port) || 5432,
  database: parsedUrl.pathname.slice(1),
  username: parsedUrl.username,
} as const;

// Export connection client for advanced usage
export const postgresClient = client;

// Export types
export type DbType = typeof db;
export type DatabaseEnvironment = typeof dbConfig.environment;

// Log configuration in development
if (dbConfig.environment !== 'production') {
  console.log('🔗 Database Configuration:');
  console.log(`Environment: ${dbConfig.environment}`);
  console.log(`Host: ${databaseConfig.host}`);
  console.log(`Port: ${databaseConfig.port}`);
  console.log(`Database: ${databaseConfig.database}`);
  console.log(`Username: ${databaseConfig.username}`);
  console.log(`Docker: ${dbConfig.isDocker ? 'Yes' : 'No'}`);
}