'use client';

import { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  X, 
  File, 
  Image as ImageIcon, 
  CheckCircle, 
  AlertCircle,
  FileText,
  Trash2
} from 'lucide-react';
import { But<PERSON> } from './Button';

interface UploadResult {
  success: boolean;
  filename?: string;
  url?: string;
  thumbnailUrl?: string;
  error?: string;
  metadata?: {
    originalName: string;
    size: number;
    mimeType: string;
    dimensions?: { width: number; height: number };
  };
}

interface FileUploadProps {
  onUpload?: (results: UploadResult[]) => void;
  category?: 'property' | 'user' | 'document';
  maxFiles?: number;
  maxFileSize?: number;
  allowedTypes?: string[];
  className?: string;
  multiple?: boolean;
  accept?: string;
}

interface FileWithPreview extends File {
  id: string;
  preview?: string;
  status: 'pending' | 'uploading' | 'success' | 'error';
  progress: number;
  result?: UploadResult;
  error?: string;
}

export function FileUpload({
  onUpload,
  category = 'property',
  maxFiles = 10,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
  className = '',
  multiple = true,
  accept = 'image/*,application/pdf',
}: FileUploadProps) {
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [uploading, setUploading] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const generateFileId = () => Math.random().toString(36).substr(2, 9);

  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    if (file.size > maxFileSize) {
      return {
        isValid: false,
        error: `File size exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`,
      };
    }

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not allowed`,
      };
    }

    return { isValid: true };
  };

  const createFilePreview = (file: File): FileWithPreview => {
    const fileWithPreview: FileWithPreview = Object.assign(file, {
      id: generateFileId(),
      status: 'pending' as const,
      progress: 0,
    });

    // Create preview for images
    if (file.type.startsWith('image/')) {
      fileWithPreview.preview = URL.createObjectURL(file);
    }

    return fileWithPreview;
  };

  const addFiles = useCallback((newFiles: File[]) => {
    const validFiles: FileWithPreview[] = [];
    const errors: string[] = [];

    for (const file of newFiles) {
      if (files.length + validFiles.length >= maxFiles) {
        errors.push(`Maximum ${maxFiles} files allowed`);
        break;
      }

      const validation = validateFile(file);
      if (validation.isValid) {
        validFiles.push(createFilePreview(file));
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    }

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
    }

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }
  }, [files.length, maxFiles, maxFileSize, allowedTypes]);

  const removeFile = (id: string) => {
    setFiles(prev => {
      const fileToRemove = prev.find(f => f.id === id);
      if (fileToRemove?.preview) {
        URL.revokeObjectURL(fileToRemove.preview);
      }
      return prev.filter(f => f.id !== id);
    });
  };

  const clearAll = () => {
    files.forEach(file => {
      if (file.preview) {
        URL.revokeObjectURL(file.preview);
      }
    });
    setFiles([]);
  };

  const uploadFiles = async () => {
    if (files.length === 0) return;

    setUploading(true);

    const formData = new FormData();
    files.forEach(file => {
      formData.append('files', file);
    });
    formData.append('category', category);

    try {
      // Update all files to uploading status
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'uploading',
        progress: 0,
      })));

      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(file => ({
          ...file,
          progress: file.status === 'uploading' ? Math.min(file.progress + Math.random() * 30, 90) : file.progress,
        })));
      }, 500);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      const result = await response.json();

      if (response.ok) {
        // Update files with results
        const updatedFiles = files.map((file, index) => {
          const fileResult = result.files[index];
          return {
            ...file,
            status: (fileResult?.success ? 'success' : 'error') as 'success' | 'error',
            progress: 100,
            result: fileResult,
            error: fileResult?.error,
          };
        });

        setFiles(updatedFiles);

        // Call onUpload callback
        if (onUpload) {
          onUpload(result.files);
        }

        // Show success message
        if (result.uploadedCount > 0) {
          alert(`Successfully uploaded ${result.uploadedCount} file(s)`);
        }

        if (result.errors && result.errors.length > 0) {
          alert(`Upload errors:\n${result.errors.join('\n')}`);
        }
      } else {
        // Update all files to error status
        setFiles(prev => prev.map(file => ({
          ...file,
          status: 'error',
          progress: 0,
          error: result.error || 'Upload failed',
        })));

        alert(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      
      setFiles(prev => prev.map(file => ({
        ...file,
        status: 'error',
        progress: 0,
        error: 'Network error',
      })));

      alert('Upload failed due to network error');
    } finally {
      setUploading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    addFiles(droppedFiles);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      addFiles(selectedFiles);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const getFileIcon = (file: FileWithPreview) => {
    if (file.type.startsWith('image/')) {
      return ImageIcon;
    } else if (file.type === 'application/pdf') {
      return FileText;
    }
    return File;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={`w-full ${className}`}>
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 text-center transition-all duration-300 ${
          dragOver
            ? 'border-blue-400 bg-blue-50'
            : files.length > 0
            ? 'border-gray-200 bg-gray-50'
            : 'border-gray-300 hover:border-blue-400 hover:bg-blue-50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          accept={accept}
          onChange={handleFileSelect}
          className="hidden"
        />

        <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {dragOver ? 'Drop files here' : 'Upload Files'}
        </h3>
        
        <p className="text-gray-600 mb-4">
          Drag and drop files here, or{' '}
          <button
            type="button"
            onClick={openFileDialog}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            browse
          </button>
        </p>
        
        <p className="text-sm text-gray-500">
          Max {maxFiles} files, {Math.round(maxFileSize / 1024 / 1024)}MB each
        </p>
      </div>

      {/* File List */}
      <AnimatePresence>
        {files.length > 0 && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="mt-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">
                Files ({files.length})
              </h4>
              <div className="flex gap-2">
                {files.some(f => f.status === 'pending') && (
                  <Button onClick={uploadFiles} disabled={uploading}>
                    {uploading ? 'Uploading...' : 'Upload All'}
                  </Button>
                )}
                <Button variant="outline" onClick={clearAll} disabled={uploading}>
                  Clear All
                </Button>
              </div>
            </div>

            <div className="space-y-3">
              {files.map((file) => {
                const FileIcon = getFileIcon(file);
                
                return (
                  <motion.div
                    key={file.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 20 }}
                    className="flex items-center space-x-4 p-3 bg-white border border-gray-200 rounded-lg"
                  >
                    {/* File Preview/Icon */}
                    <div className="flex-shrink-0">
                      {file.preview ? (
                        <img
                          src={file.preview}
                          alt={file.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-100 rounded flex items-center justify-center">
                          <FileIcon className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* File Info */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                      
                      {/* Progress Bar */}
                      {file.status === 'uploading' && (
                        <div className="mt-2">
                          <div className="bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                              style={{ width: `${file.progress}%` }}
                            />
                          </div>
                        </div>
                      )}
                      
                      {/* Error Message */}
                      {file.status === 'error' && (
                        <p className="text-sm text-red-600 mt-1">
                          {file.error}
                        </p>
                      )}
                    </div>

                    {/* Status Icon */}
                    <div className="flex-shrink-0">
                      {file.status === 'success' && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                      {file.status === 'error' && (
                        <AlertCircle className="w-5 h-5 text-red-500" />
                      )}
                      {file.status === 'uploading' && (
                        <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                      )}
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={() => removeFile(file.id)}
                      disabled={uploading}
                      className="flex-shrink-0 p-1 text-gray-400 hover:text-red-500 disabled:opacity-50"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}