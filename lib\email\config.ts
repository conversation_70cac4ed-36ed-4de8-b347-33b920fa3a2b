import nodemailer from 'nodemailer';

// Email configuration
export const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
};

// Create reusable transporter
export const transporter = nodemailer.createTransport(emailConfig);

// Verify transporter configuration
export async function verifyEmailConfig(): Promise<boolean> {
  try {
    await transporter.verify();
    console.log('Email configuration verified successfully');
    return true;
  } catch (error) {
    console.error('Email configuration error:', error);
    return false;
  }
}

// Email templates
export const emailTemplates = {
  propertyAlert: {
    subject: 'New Properties Match Your Search Criteria',
    getHtml: (properties: any[], searchName: string, unsubscribeUrl: string) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Property Alert - Property Trendz</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .property-card { border: 1px solid #ddd; margin: 15px 0; padding: 15px; border-radius: 8px; }
            .property-image { width: 100%; max-width: 300px; height: 200px; object-fit: cover; border-radius: 4px; }
            .property-title { font-size: 18px; font-weight: bold; margin: 10px 0; }
            .property-price { color: #2563eb; font-size: 16px; font-weight: bold; }
            .property-details { color: #666; margin: 8px 0; }
            .btn { background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 0; }
            .footer { text-align: center; color: #666; font-size: 12px; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd; }
            .unsubscribe { color: #999; font-size: 11px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🏠 Property Trendz</h1>
              <p>New properties matching your saved search: "${searchName}"</p>
            </div>
            
            <div style="padding: 20px 0;">
              <h2>We found ${properties.length} new ${properties.length === 1 ? 'property' : 'properties'} for you!</h2>
              
              ${properties.map(property => `
                <div class="property-card">
                  ${property.images?.[0] ? `<img src="${property.images[0]}" alt="${property.title}" class="property-image">` : ''}
                  <div class="property-title">${property.title}</div>
                  <div class="property-price">₹${parseFloat(property.price).toLocaleString('en-IN')}${property.listingType === 'rent' ? '/month' : ''}</div>
                  <div class="property-details">
                    📍 ${property.area}, ${property.city} | 
                    🛏️ ${property.bedrooms || 'N/A'} BHK | 
                    📐 ${property.totalArea || 'N/A'} sq ft |
                    🏷️ For ${property.listingType}
                  </div>
                  <a href="${process.env.NEXT_PUBLIC_SITE_URL}/properties/${property.slug}" class="btn">View Details</a>
                </div>
              `).join('')}
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXT_PUBLIC_SITE_URL}/properties" class="btn">Browse All Properties</a>
            </div>
            
            <div class="footer">
              <p>Best regards,<br>Property Trendz Team</p>
              <p>📧 Email: ${process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>'}</p>
              <p>📞 Phone: ${process.env.NEXT_PUBLIC_CONTACT_PHONE || '+91 9810129777'}</p>
              <hr>
              <p class="unsubscribe">
                You're receiving this because you created a saved search. 
                <a href="${unsubscribeUrl}">Unsubscribe</a> | 
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}/favorites">Manage Preferences</a>
              </p>
            </div>
          </div>
        </body>
      </html>
    `,
  },

  welcomeEmail: {
    subject: 'Welcome to Property Trendz!',
    getHtml: (userName: string) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Welcome to Property Trendz</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #2563eb; color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
            .btn { background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 15px 0; }
            .feature { margin: 15px 0; padding: 15px; background: white; border-radius: 4px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 Welcome, ${userName}!</h1>
              <p>Thank you for joining Property Trendz</p>
            </div>
            <div class="content">
              <h2>Get started with these features:</h2>
              
              <div class="feature">
                <h3>💗 Save Favorites</h3>
                <p>Save properties you love and access them anytime from your dashboard.</p>
              </div>
              
              <div class="feature">
                <h3>⚖️ Compare Properties</h3>
                <p>Compare up to 3 properties side-by-side to make the best decision.</p>
              </div>
              
              <div class="feature">
                <h3>🔔 Property Alerts</h3>
                <p>Create saved searches and get notified when new matching properties are added.</p>
              </div>
              
              <div style="text-align: center;">
                <a href="${process.env.NEXT_PUBLIC_SITE_URL}/properties" class="btn">Start Browsing Properties</a>
              </div>
              
              <hr style="margin: 30px 0;">
              <p><strong>Need help?</strong> Contact us at ${process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>'} or ${process.env.NEXT_PUBLIC_CONTACT_PHONE || '+91 9810129777'}</p>
            </div>
          </div>
        </body>
      </html>
    `,
  },

  enquiryConfirmation: {
    subject: 'Your Property Enquiry - Property Trendz',
    getHtml: (userName: string, propertyTitle: string, propertyUrl: string) => `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>Enquiry Confirmation</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #10b981; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .btn { background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>✅ Enquiry Received!</h1>
            </div>
            <div class="content">
              <p>Dear ${userName},</p>
              <p>Thank you for your enquiry about <strong>"${propertyTitle}"</strong>.</p>
              <p>Our team will contact you within 24 hours with detailed information.</p>
              <div style="text-align: center; margin: 20px 0;">
                <a href="${propertyUrl}" class="btn">View Property Details</a>
              </div>
              <p>Best regards,<br>Property Trendz Team</p>
            </div>
          </div>
        </body>
      </html>
    `,
  },
};

// Send email function
export async function sendEmail({
  to,
  subject,
  html,
  text,
}: {
  to: string | string[];
  subject: string;
  html: string;
  text?: string;
}): Promise<boolean> {
  try {
    const info = await transporter.sendMail({
      from: `"Property Trendz" <${process.env.SMTP_USER}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
      text: text || html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
    });

    console.log('Email sent successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Failed to send email:', error);
    return false;
  }
}

// Bulk email function with rate limiting
export async function sendBulkEmails(
  emails: Array<{ to: string; subject: string; html: string; text?: string }>,
  batchSize = 10,
  delayMs = 1000
): Promise<{ sent: number; failed: number }> {
  let sent = 0;
  let failed = 0;

  for (let i = 0; i < emails.length; i += batchSize) {
    const batch = emails.slice(i, i + batchSize);
    
    await Promise.all(
      batch.map(async (email) => {
        const success = await sendEmail(email);
        if (success) {
          sent++;
        } else {
          failed++;
        }
      })
    );

    // Add delay between batches to avoid rate limiting
    if (i + batchSize < emails.length) {
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  return { sent, failed };
}