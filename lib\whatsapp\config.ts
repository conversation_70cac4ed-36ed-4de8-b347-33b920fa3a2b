interface WhatsAppMessageTemplate {
  message: string;
  url?: string;
}

interface PropertyInquiry {
  propertyTitle: string;
  propertyUrl: string;
  propertyPrice: string;
  customerName?: string;
  customerPhone?: string;
  customerEmail?: string;
  message?: string;
}

export class WhatsAppService {
  private static readonly BUSINESS_NUMBER = process.env.NEXT_PUBLIC_WHATSAPP_NUMBER || '919999999999';
  private static readonly BUSINESS_NAME = 'Property Trendz';

  /**
   * Create a WhatsApp URL for direct messaging
   */
  static createWhatsAppUrl(phoneNumber: string, message: string): string {
    const encodedMessage = encodeURIComponent(message);
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
  }

  /**
   * Create a WhatsApp URL for property inquiry
   */
  static createPropertyInquiryUrl(inquiry: PropertyInquiry): string {
    const message = this.formatPropertyInquiryMessage(inquiry);
    return this.createWhatsAppUrl(this.BUSINESS_NUMBER, message);
  }

  /**
   * Create a WhatsApp URL for general business inquiry
   */
  static createBusinessInquiryUrl(message: string, customerName?: string): string {
    let fullMessage = `Hello ${this.BUSINESS_NAME},\n\n`;
    
    if (customerName) {
      fullMessage += `My name is ${customerName}.\n\n`;
    }
    
    fullMessage += message;
    fullMessage += `\n\nI found you through your website: ${process.env.NEXT_PUBLIC_SITE_URL}`;
    
    return this.createWhatsAppUrl(this.BUSINESS_NUMBER, fullMessage);
  }

  /**
   * Create a WhatsApp URL for callback request
   */
  static createCallbackRequestUrl(customerName: string, customerPhone: string, preferredTime?: string): string {
    let message = `Hello ${this.BUSINESS_NAME},\n\n`;
    message += `I would like to request a callback.\n\n`;
    message += `My details:\n`;
    message += `Name: ${customerName}\n`;
    message += `Phone: ${customerPhone}\n`;
    
    if (preferredTime) {
      message += `Preferred time: ${preferredTime}\n`;
    }
    
    message += `\nPlease call me back at your convenience.\n\n`;
    message += `Website: ${process.env.NEXT_PUBLIC_SITE_URL}`;
    
    return this.createWhatsAppUrl(this.BUSINESS_NUMBER, message);
  }

  /**
   * Create a WhatsApp URL for property viewing request
   */
  static createViewingRequestUrl(inquiry: PropertyInquiry, preferredDate?: string, preferredTime?: string): string {
    let message = `Hello ${this.BUSINESS_NAME},\n\n`;
    message += `I would like to schedule a property viewing.\n\n`;
    message += `Property Details:\n`;
    message += `🏠 ${inquiry.propertyTitle}\n`;
    message += `💰 ${inquiry.propertyPrice}\n`;
    message += `🔗 ${inquiry.propertyUrl}\n\n`;
    
    if (inquiry.customerName) {
      message += `My details:\n`;
      message += `Name: ${inquiry.customerName}\n`;
      if (inquiry.customerPhone) {
        message += `Phone: ${inquiry.customerPhone}\n`;
      }
      if (inquiry.customerEmail) {
        message += `Email: ${inquiry.customerEmail}\n`;
      }
      message += `\n`;
    }
    
    if (preferredDate || preferredTime) {
      message += `Preferred viewing time:\n`;
      if (preferredDate) {
        message += `Date: ${preferredDate}\n`;
      }
      if (preferredTime) {
        message += `Time: ${preferredTime}\n`;
      }
      message += `\n`;
    }
    
    if (inquiry.message) {
      message += `Additional message:\n${inquiry.message}\n\n`;
    }
    
    message += `Please confirm the viewing appointment.\n\n`;
    message += `Thank you!`;
    
    return this.createWhatsAppUrl(this.BUSINESS_NUMBER, message);
  }

  /**
   * Format property inquiry message
   */
  private static formatPropertyInquiryMessage(inquiry: PropertyInquiry): string {
    let message = `Hello ${this.BUSINESS_NAME},\n\n`;
    message += `I'm interested in this property:\n\n`;
    message += `🏠 ${inquiry.propertyTitle}\n`;
    message += `💰 ${inquiry.propertyPrice}\n`;
    message += `🔗 ${inquiry.propertyUrl}\n\n`;
    
    if (inquiry.customerName || inquiry.customerPhone || inquiry.customerEmail) {
      message += `My contact details:\n`;
      if (inquiry.customerName) {
        message += `Name: ${inquiry.customerName}\n`;
      }
      if (inquiry.customerPhone) {
        message += `Phone: ${inquiry.customerPhone}\n`;
      }
      if (inquiry.customerEmail) {
        message += `Email: ${inquiry.customerEmail}\n`;
      }
      message += `\n`;
    }
    
    if (inquiry.message) {
      message += `Message: ${inquiry.message}\n\n`;
    }
    
    message += `Please provide more details about this property.\n\n`;
    message += `Thank you!`;
    
    return message;
  }

  /**
   * Get WhatsApp Web URL (for desktop)
   */
  static getWhatsAppWebUrl(message: string): string {
    const encodedMessage = encodeURIComponent(message);
    return `https://web.whatsapp.com/send?phone=${this.BUSINESS_NUMBER}&text=${encodedMessage}`;
  }

  /**
   * Check if device is mobile for WhatsApp app vs web
   */
  static isMobileDevice(): boolean {
    if (typeof window === 'undefined') return false;
    
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      window.navigator.userAgent
    );
  }

  /**
   * Open WhatsApp with appropriate URL (app vs web)
   */
  static openWhatsApp(message: string, useWeb = false): void {
    const url = useWeb || !this.isMobileDevice() 
      ? this.getWhatsAppWebUrl(message)
      : this.createWhatsAppUrl(this.BUSINESS_NUMBER, message);
    
    window.open(url, '_blank');
  }

  /**
   * Predefined message templates
   */
  static readonly templates = {
    generalInquiry: "I'm interested in your property services. Please provide more information.",
    priceInquiry: "Could you please provide pricing information for your properties?",
    viewingRequest: "I would like to schedule a property viewing. Please let me know available times.",
    callbackRequest: "Please call me back. I'm interested in your services.",
    documentation: "Could you please send me the documentation and legal papers for this property?",
    loanAssistance: "Do you provide home loan assistance? I need help with financing.",
    negotiation: "I'm interested in this property. Is there room for price negotiation?",
    areaInfo: "Could you provide more information about the area and nearby amenities?",
    techicalDetails: "Please share technical details, floor plans, and specifications.",
    posessionDate: "When is the possession date for this property?"
  };
}

// Utility function for React components
export function useWhatsApp() {
  return {
    sendPropertyInquiry: (inquiry: PropertyInquiry) => {
      const url = WhatsAppService.createPropertyInquiryUrl(inquiry);
      window.open(url, '_blank');
    },
    
    sendBusinessInquiry: (message: string, customerName?: string) => {
      const url = WhatsAppService.createBusinessInquiryUrl(message, customerName);
      window.open(url, '_blank');
    },
    
    requestCallback: (customerName: string, customerPhone: string, preferredTime?: string) => {
      const url = WhatsAppService.createCallbackRequestUrl(customerName, customerPhone, preferredTime);
      window.open(url, '_blank');
    },
    
    requestViewing: (inquiry: PropertyInquiry, preferredDate?: string, preferredTime?: string) => {
      const url = WhatsAppService.createViewingRequestUrl(inquiry, preferredDate, preferredTime);
      window.open(url, '_blank');
    },
    
    sendCustomMessage: (message: string) => {
      WhatsAppService.openWhatsApp(message);
    }
  };
}