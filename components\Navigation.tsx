'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Home, Phone, MessageCircle, Menu, X, User, LogOut, Settings, Heart, Search, Scale, Building2, Info, Mail } from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useSession, signOut } from 'next-auth/react'
import { Button } from '@/components/ui/Button'
import { useClickOutside } from '@/hooks/useClickOutside'

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const [isHeaderVisible, setIsHeaderVisible] = useState(true)
  const [hideTimeout, setHideTimeout] = useState<NodeJS.Timeout | null>(null)
  const pathname = usePathname()
  const { data: session, status } = useSession()

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/properties', label: 'Properties', icon: Building2 },
    { href: '/compare', label: 'Compare', icon: Scale },
    { href: '/about', label: 'About', icon: Info },
    { href: '/contact', label: 'Contact', icon: Mail }
  ]

  const isActive = (path: string) => pathname === path

  const handleSignOut = () => {
    signOut({ callbackUrl: '/' })
  }

  const userMenuRef = useClickOutside<HTMLDivElement>(() => {
    setIsUserMenuOpen(false)
  })

  // Clear timeout when component unmounts
  useEffect(() => {
    return () => {
      if (hideTimeout) {
        clearTimeout(hideTimeout)
      }
    }
  }, [])

  useEffect(() => {
    // Pages where header should always be visible (no hover animation)
    const staticHeaderPages = ['/', '/properties', '/compare', '/about', '/contact']
    const shouldShowStaticHeader = staticHeaderPages.includes(pathname)
    
    if (shouldShowStaticHeader) {
      setIsHeaderVisible(true)
      return // Don't add mouse event listener on main content pages
    }

    // All other pages (including admin pages) get the hover animation
    const handleMouseMove = (e: MouseEvent) => {
      const mouseY = e.clientY
      const showThreshold = 80 // Show header when mouse is within 80px of top
      
      // Clear any existing timeout
      if (hideTimeout) {
        clearTimeout(hideTimeout)
        setHideTimeout(null)
      }
      
      if (mouseY <= showThreshold) {
        setIsHeaderVisible(true)
      } else if (mouseY > 200 && !isUserMenuOpen) { // Don't hide if dropdown is open
        // Add 3 second delay before hiding
        const timeout = setTimeout(() => {
          if (!isUserMenuOpen) { // Double check dropdown is still closed
            setIsHeaderVisible(false)
          }
        }, 3000)
        setHideTimeout(timeout)
      }
    }

    document.addEventListener('mousemove', handleMouseMove)
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      if (hideTimeout) {
        clearTimeout(hideTimeout)
      }
    }
  }, [pathname, isUserMenuOpen, hideTimeout])

  return (
    <motion.nav 
      className="bg-white shadow-md fixed w-full z-50"
      initial={{ y: 0 }}
      animate={{ 
        y: isHeaderVisible ? 0 : -100,
        opacity: isHeaderVisible ? 1 : 0 
      }}
      transition={{ 
        duration: 0.3, 
        ease: 'easeInOut' 
      }}
      style={{ top: 0 }}
    >
      <div className="container-custom">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2"
          >
            <Home className="w-8 h-8 text-primary-600" />
            <Link href="/" className="text-xl font-display font-bold text-neutral-800 hover:text-primary-600 transition-colors">
              Property Trendz
            </Link>
          </motion.div>
          
          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link 
                key={item.href}
                href={item.href} 
                className={`transition-colors flex items-center gap-1 ${
                  isActive(item.href)
                    ? 'text-primary-600 font-semibold'
                    : 'text-neutral-700 hover:text-primary-600'
                }`}
              >
                {item.icon && <item.icon className="w-4 h-4" />}
                {item.label}
              </Link>
            ))}
          </div>

          {/* Contact Actions & Authentication */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-4"
          >
            <a
              href="https://wa.me/919810129777"
              target="_blank"
              rel="noopener noreferrer"
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 hover:scale-105 shadow-lg"
            >
              <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.087z"/>
              </svg>
              <span className="hidden sm:inline font-medium">WhatsApp</span>
            </a>

            {/* Authentication Section */}
            {status === 'loading' ? (
              <div className="w-8 h-8 animate-spin rounded-full border-2 border-primary-600 border-t-transparent"></div>
            ) : session ? (
              <div 
                className="relative" 
                ref={userMenuRef}
                onMouseEnter={() => {
                  // Clear hide timeout when hovering over user menu
                  if (hideTimeout) {
                    clearTimeout(hideTimeout)
                    setHideTimeout(null)
                  }
                }}
              >
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100 transition-colors"
                >
                  {session.user.image ? (
                    <img 
                      src={session.user.image} 
                      alt={session.user.name || ''} 
                      className="w-8 h-8 rounded-full"
                    />
                  ) : (
                    <User className="w-8 h-8 p-1 bg-primary-100 text-primary-600 rounded-full" />
                  )}
                  <span className="hidden md:inline text-sm font-medium">
                    {session.user.name || session.user.email}
                  </span>
                </button>

                {/* User Dropdown Menu */}
                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 z-50">
                    <div className="py-1">
                      <div className="px-4 py-2 text-sm text-gray-700 border-b">
                        <div className="font-medium">{session.user.name}</div>
                        <div className="text-xs text-gray-500">{session.user.email}</div>
                      </div>
                      <Link
                        href="/dashboard"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <User className="w-4 h-4 mr-2" />
                        My Profile
                      </Link>
                      <Link
                        href="/favorites"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Heart className="w-4 h-4 mr-2" />
                        My Favorites
                      </Link>
                      <Link
                        href="/saved-searches"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Search className="w-4 h-4 mr-2" />
                        Saved Searches
                      </Link>
                      <Link
                        href="/compare"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <Scale className="w-4 h-4 mr-2" />
                        Compare Properties
                      </Link>
                      {session.user.role === 'admin' && (
                        <Link
                          href="/admin"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                          onClick={() => setIsUserMenuOpen(false)}
                        >
                          <Settings className="w-4 h-4 mr-2" />
                          Admin Panel
                        </Link>
                      )}
                      <button
                        onClick={() => {
                          setIsUserMenuOpen(false)
                          handleSignOut()
                        }}
                        className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 flex items-center"
                      >
                        <LogOut className="w-4 h-4 mr-2" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="hidden md:flex items-center space-x-2">
                <Link href="/auth/signin">
                  <Button variant="outline" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/signup">
                  <Button size="sm">
                    Sign Up
                  </Button>
                </Link>
              </div>
            )}
            
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-neutral-600 hover:text-primary-600 transition-colors"
            >
              {isMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </motion.div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="md:hidden bg-white border-t border-neutral-200"
        >
          <div className="container-custom py-4">
            <div className="flex flex-col space-y-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`py-2 transition-colors flex items-center gap-2 ${
                    isActive(item.href)
                      ? 'text-primary-600 font-semibold'
                      : 'text-neutral-700 hover:text-primary-600'
                  }`}
                >
                  {item.icon && <item.icon className="w-4 h-4" />}
                  {item.label}
                </Link>
              ))}
              
              {/* Mobile Authentication */}
              {session ? (
                <div className="border-t pt-4 mt-4">
                  <div className="flex items-center space-x-3 mb-4">
                    {session.user.image ? (
                      <img 
                        src={session.user.image} 
                        alt={session.user.name || ''} 
                        className="w-10 h-10 rounded-full"
                      />
                    ) : (
                      <User className="w-10 h-10 p-2 bg-primary-100 text-primary-600 rounded-full" />
                    )}
                    <div>
                      <div className="font-medium text-gray-900">{session.user.name}</div>
                      <div className="text-sm text-gray-500">{session.user.email}</div>
                    </div>
                  </div>
                  <Link
                    href="/dashboard"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2"
                  >
                    <User className="w-4 h-4" />
                    <span>My Profile</span>
                  </Link>
                  <Link
                    href="/favorites"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2"
                  >
                    <Heart className="w-4 h-4" />
                    <span>My Favorites</span>
                  </Link>
                  <Link
                    href="/saved-searches"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2"
                  >
                    <Search className="w-4 h-4" />
                    <span>Saved Searches</span>
                  </Link>
                  <Link
                    href="/compare"
                    onClick={() => setIsMenuOpen(false)}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2"
                  >
                    <Scale className="w-4 h-4" />
                    <span>Compare Properties</span>
                  </Link>
                  {session.user.role === 'admin' && (
                    <Link
                      href="/admin"
                      onClick={() => setIsMenuOpen(false)}
                      className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2"
                    >
                      <Settings className="w-4 h-4" />
                      <span>Admin Panel</span>
                    </Link>
                  )}
                  <button
                    onClick={() => {
                      setIsMenuOpen(false)
                      handleSignOut()
                    }}
                    className="flex items-center space-x-2 text-gray-700 hover:text-primary-600 py-2 w-full text-left"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Sign Out</span>
                  </button>
                </div>
              ) : (
                <div className="border-t pt-4 mt-4 space-y-2">
                  <Link href="/auth/signin" onClick={() => setIsMenuOpen(false)}>
                    <Button variant="outline" className="w-full">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth/signup" onClick={() => setIsMenuOpen(false)}>
                    <Button className="w-full">
                      Sign Up
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </motion.nav>
  )
} 