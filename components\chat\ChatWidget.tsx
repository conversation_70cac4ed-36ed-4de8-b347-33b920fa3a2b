'use client';

import { useState, useEffect, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { MessageCircle, Send, X, Minimize2, Paperclip, Smile } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSocket, useChatRoom } from '@/lib/socket/client';
import { Button } from '@/components/ui/Button';

interface ChatWidgetProps {
  propertyId?: string;
  propertyTitle?: string;
  className?: string;
}

interface ChatMessage {
  id: string;
  roomId: string;
  senderId: string;
  senderName: string;
  senderRole: 'user' | 'admin' | 'agent';
  message: string;
  timestamp: Date;
  type: 'text' | 'image' | 'file';
  propertyId?: string;
  isRead: boolean;
}

export function ChatWidget({ propertyId, propertyTitle, className = '' }: ChatWidgetProps) {
  const { data: session } = useSession();
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [roomId, setRoomId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messageInputRef = useRef<HTMLInputElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const {
    isConnected,
    messages,
    currentRoom,
    typingUsers,
    joinRoom,
    leaveRoom,
    sendMessage,
    startTyping,
    stopTyping,
  } = useSocket();

  const { createRoom } = useChatRoom();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized && messageInputRef.current) {
      messageInputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  // Create or join chat room
  const handleStartChat = async () => {
    if (!session?.user) return;

    setIsLoading(true);
    try {
      const newRoomId = await createRoom({
        userId: session.user.id as string,
        userName: session.user.name || 'User',
        userEmail: session.user.email || '',
        propertyId,
      });

      setRoomId(newRoomId);
      joinRoom(newRoomId, session.user.id as string, session.user.name || 'User');
      
      // Send initial message if property context
      if (propertyId && propertyTitle) {
        setTimeout(() => {
          sendMessage({
            roomId: newRoomId,
            senderId: session.user.id as string,
            senderName: session.user.name || 'User',
            senderRole: 'user',
            message: `Hi! I'm interested in "${propertyTitle}". Could you provide more information?`,
            type: 'text',
            propertyId,
          });
        }, 1000);
      }
    } catch (error) {
      console.error('Failed to start chat:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle message send
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !roomId || !session?.user) return;

    sendMessage({
      roomId,
      senderId: session.user.id as string,
      senderName: session.user.name || 'User',
      senderRole: 'user',
      message: message.trim(),
      type: 'text',
      propertyId,
    });

    setMessage('');
    handleStopTyping();
  };

  // Handle typing indicators
  const handleTyping = () => {
    if (!roomId || !session?.user) return;

    startTyping(roomId, session.user.id as string, session.user.name || 'User');

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing
    typingTimeoutRef.current = setTimeout(() => {
      handleStopTyping();
    }, 3000);
  };

  const handleStopTyping = () => {
    if (!roomId || !session?.user) return;
    
    stopTyping(roomId, session.user.id as string);
    
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
  };

  // Format timestamp
  const formatTime = (timestamp: Date) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // Get typing users (excluding current user)
  const currentlyTyping = typingUsers.filter(
    user => user.userId !== session?.user?.id && user.isTyping
  );

  if (!session) {
    return null;
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <AnimatePresence>
        {!isOpen && (
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
            onClick={() => setIsOpen(true)}
            className="bg-blue-500 hover:bg-blue-600 text-white rounded-full p-4 shadow-2xl hover:shadow-3xl transition-all duration-200 hover:scale-110"
          >
            <MessageCircle className="w-6 h-6" />
          </motion.button>
        )}

        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.9 }}
            className="bg-white rounded-lg shadow-2xl border w-80 max-h-96 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="bg-blue-500 text-white p-4 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageCircle className="w-5 h-5" />
                <div>
                  <h3 className="font-semibold">Chat Support</h3>
                  <p className="text-xs opacity-90">
                    {isConnected ? (
                      <span className="text-green-200">● Online</span>
                    ) : (
                      <span className="text-red-200">● Connecting...</span>
                    )}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="hover:bg-blue-600 p-1 rounded"
                >
                  <Minimize2 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => {
                    setIsOpen(false);
                    if (currentRoom) {
                      leaveRoom();
                    }
                  }}
                  className="hover:bg-blue-600 p-1 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages Area */}
                <div className="flex-1 p-4 overflow-y-auto max-h-64 bg-gray-50">
                  {!roomId ? (
                    <div className="text-center">
                      <div className="mb-4">
                        <MessageCircle className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-600 text-sm">
                          Start a conversation with our team
                        </p>
                        {propertyTitle && (
                          <p className="text-xs text-gray-500 mt-1">
                            About: {propertyTitle}
                          </p>
                        )}
                      </div>
                      <Button
                        onClick={handleStartChat}
                        disabled={isLoading}
                        className="w-full"
                      >
                        {isLoading ? 'Starting...' : 'Start Chat'}
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {messages.map((msg) => (
                        <div
                          key={msg.id}
                          className={`flex ${
                            msg.senderId === session.user?.id
                              ? 'justify-end'
                              : 'justify-start'
                          }`}
                        >
                          <div
                            className={`max-w-xs px-3 py-2 rounded-lg text-sm ${
                              msg.senderId === session.user?.id
                                ? 'bg-blue-500 text-white'
                                : 'bg-white border'
                            }`}
                          >
                            {msg.senderId !== session.user?.id && (
                              <div className="text-xs text-gray-500 mb-1">
                                {msg.senderName}
                                {msg.senderRole === 'admin' && (
                                  <span className="ml-1 bg-green-100 text-green-800 px-1 rounded">
                                    Admin
                                  </span>
                                )}
                              </div>
                            )}
                            <div>{msg.message}</div>
                            <div
                              className={`text-xs mt-1 ${
                                msg.senderId === session.user?.id
                                  ? 'text-blue-100'
                                  : 'text-gray-400'
                              }`}
                            >
                              {formatTime(msg.timestamp)}
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Typing indicator */}
                      {currentlyTyping.length > 0 && (
                        <div className="flex justify-start">
                          <div className="bg-gray-200 px-3 py-2 rounded-lg text-sm text-gray-600">
                            <div className="flex items-center gap-1">
                              <div className="flex gap-1">
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                              </div>
                              <span className="ml-2 text-xs">
                                {currentlyTyping[0].userName} is typing...
                              </span>
                            </div>
                          </div>
                        </div>
                      )}

                      <div ref={messagesEndRef} />
                    </div>
                  )}
                </div>

                {/* Input Area */}
                {roomId && (
                  <div className="p-4 border-t bg-white">
                    <form onSubmit={handleSendMessage} className="flex gap-2">
                      <input
                        ref={messageInputRef}
                        type="text"
                        value={message}
                        onChange={(e) => {
                          setMessage(e.target.value);
                          handleTyping();
                        }}
                        placeholder="Type your message..."
                        className="flex-1 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                        disabled={!isConnected}
                      />
                      <button
                        type="submit"
                        disabled={!message.trim() || !isConnected}
                        className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white p-2 rounded-lg transition-colors"
                      >
                        <Send className="w-4 h-4" />
                      </button>
                    </form>
                  </div>
                )}
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}