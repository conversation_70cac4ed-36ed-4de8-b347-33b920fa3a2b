import { NextRequest, NextResponse } from 'next/server';
import { ZodSchema, ZodError } from 'zod';
import { sanitizeInput } from './sanitize';

export interface ValidationResult<T> {
  success: boolean;
  data?: T;
  error?: {
    message: string;
    details: Array<{
      field: string;
      message: string;
    }>;
  };
}

/**
 * Validate request body against a Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): Promise<ValidationResult<T>> {
  try {
    const body = await request.json();
    
    // Sanitize input first
    const sanitizedBody = sanitizeInput(body);
    
    // Validate against schema
    const validatedData = schema.parse(sanitizedBody);
    
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          message: 'Validation failed',
          details: error.issues.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
      };
    }
    
    return {
      success: false,
      error: {
        message: 'Invalid request body',
        details: [],
      },
    };
  }
}

/**
 * Validate query parameters against a Zod schema
 */
export function validateQueryParams<T>(
  request: NextRequest,
  schema: ZodSchema<T>
): ValidationResult<T> {
  try {
    const { searchParams } = new URL(request.url);
    const params: Record<string, any> = {};
    
    // Convert URL search params to object
    Array.from(searchParams.entries()).forEach(([key, value]) => {
      // Handle array parameters (comma-separated)
      if (value.includes(',')) {
        params[key] = value.split(',').map(v => v.trim());
      } else {
        // Try to parse as number if it looks like one
        const numValue = Number(value);
        if (!isNaN(numValue) && value !== '') {
          params[key] = numValue;
        } else if (value === 'true' || value === 'false') {
          // Parse boolean values
          params[key] = value === 'true';
        } else {
          params[key] = value;
        }
      }
    });
    
    // Sanitize input
    const sanitizedParams = sanitizeInput(params);
    
    // Validate against schema
    const validatedData = schema.parse(sanitizedParams);
    
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          message: 'Invalid query parameters',
          details: error.issues.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
      };
    }
    
    return {
      success: false,
      error: {
        message: 'Invalid query parameters',
        details: [],
      },
    };
  }
}

/**
 * Create a validation middleware wrapper for API routes
 */
export function withValidation<TBody = any, TQuery = any>(
  handler: (
    request: NextRequest,
    context: {
      body?: TBody;
      query?: TQuery;
      params?: Record<string, string>;
    }
  ) => Promise<NextResponse>,
  options: {
    body?: ZodSchema<TBody>;
    query?: ZodSchema<TQuery>;
  } = {}
) {
  return async (
    request: NextRequest,
    context?: { params?: Record<string, string> }
  ) => {
    try {
      const validationContext: {
        body?: TBody;
        query?: TQuery;
        params?: Record<string, string>;
      } = {
        params: context?.params,
      };

      // Validate request body if schema provided
      if (options.body) {
        const bodyValidation = await validateRequestBody(request, options.body);
        
        if (!bodyValidation.success) {
          return NextResponse.json(
            {
              error: 'Validation Error',
              message: bodyValidation.error?.message,
              details: bodyValidation.error?.details,
            },
            { status: 400 }
          );
        }
        
        validationContext.body = bodyValidation.data;
      }

      // Validate query parameters if schema provided
      if (options.query) {
        const queryValidation = validateQueryParams(request, options.query);
        
        if (!queryValidation.success) {
          return NextResponse.json(
            {
              error: 'Validation Error',
              message: queryValidation.error?.message,
              details: queryValidation.error?.details,
            },
            { status: 400 }
          );
        }
        
        validationContext.query = queryValidation.data;
      }

      // Call the original handler with validated data
      return await handler(request, validationContext);
    } catch (error) {
      console.error('Validation middleware error:', error);
      
      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: 'Something went wrong processing your request',
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Validate file uploads
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {}
): ValidationResult<File> {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    allowedExtensions = ['jpg', 'jpeg', 'png', 'webp'],
  } = options;

  // Check file size
  if (file.size > maxSize) {
    return {
      success: false,
      error: {
        message: 'File too large',
        details: [
          {
            field: 'file.size',
            message: `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`,
          },
        ],
      },
    };
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      success: false,
      error: {
        message: 'Invalid file type',
        details: [
          {
            field: 'file.type',
            message: `File type ${file.type} is not allowed. Allowed types: ${allowedTypes.join(', ')}`,
          },
        ],
      },
    };
  }

  // Check file extension
  const extension = file.name.split('.').pop()?.toLowerCase();
  if (!extension || !allowedExtensions.includes(extension)) {
    return {
      success: false,
      error: {
        message: 'Invalid file extension',
        details: [
          {
            field: 'file.name',
            message: `File extension .${extension} is not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`,
          },
        ],
      },
    };
  }

  return {
    success: true,
    data: file,
  };
}

/**
 * Sanitize and validate form data
 */
export function validateFormData(
  formData: FormData,
  schema: ZodSchema,
  fileFields: string[] = []
): ValidationResult<any> {
  try {
    const data: Record<string, any> = {};
    
    Array.from(formData.entries()).forEach(([key, value]) => {
      if (fileFields.includes(key)) {
        // Handle file uploads
        data[key] = value;
      } else {
        // Handle regular form fields
        data[key] = typeof value === 'string' ? value : value.toString();
      }
    });
    
    // Sanitize non-file data
    const sanitizedData = sanitizeInput(data);
    
    // Validate against schema
    const validatedData = schema.parse(sanitizedData);
    
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof ZodError) {
      return {
        success: false,
        error: {
          message: 'Form validation failed',
          details: error.issues.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
      };
    }
    
    return {
      success: false,
      error: {
        message: 'Invalid form data',
        details: [],
      },
    };
  }
}

/**
 * Create error response for validation failures
 */
export function createValidationErrorResponse(
  error: ValidationResult<any>['error'],
  status = 400
): NextResponse {
  return NextResponse.json(
    {
      error: 'Validation Error',
      message: error?.message || 'Request validation failed',
      details: error?.details || [],
    },
    { status }
  );
}

// Utility function to check if request has valid content type
export function hasValidContentType(
  request: NextRequest,
  allowedTypes: string[] = ['application/json']
): boolean {
  const contentType = request.headers.get('content-type');
  
  if (!contentType) {
    return false;
  }
  
  return allowedTypes.some(type => contentType.includes(type));
}