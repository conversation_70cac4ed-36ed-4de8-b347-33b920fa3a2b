# PostgreSQL Database Setup Guide

## Option A: Local PostgreSQL Installation

### Windows Installation:
1. Download PostgreSQL from: https://www.postgresql.org/download/windows/
2. Install with default settings
3. Remember the password you set for the `postgres` user
4. Default port is usually 5432

### Create Database:
```sql
-- Connect to PostgreSQL (using pgAdmin or psql)
CREATE DATABASE armaan_properties;
CREATE USER armaan_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE armaan_properties TO armaan_user;
```

### Connection String:
```
postgresql://armaan_user:your_secure_password@localhost:5432/armaan_properties
```

## Option B: Docker PostgreSQL (Recommended for Development)

### 1. Install Docker Desktop for Windows
- Download from: https://docs.docker.com/desktop/install/windows-install/

### 2. Create docker-compose.yml:
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: armaan_properties
      POSTGRES_USER: armaan_user
      POSTGRES_PASSWORD: armaan_secure_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Start Database:
```bash
docker-compose up -d
```

### Connection String:
```
postgresql://armaan_user:armaan_secure_password@localhost:5432/armaan_properties
```

## Option C: Cloud Database (Production Ready)

### Supabase (Free Tier Available):
1. Go to https://supabase.com/
2. Create new project
3. Note the database URL from Project Settings > Database

### Railway (Free Tier Available):
1. Go to https://railway.app/
2. Create new project
3. Add PostgreSQL service
4. Note the connection string

### Neon (Free Tier Available):
1. Go to https://neon.tech/
2. Create new project
3. Note the connection string

## Testing Connection

After setup, test the connection:
```bash
cd "C:\Users\<USER>\Downloads\Website"
npm run db:push
```

This will create all tables in your database.