import { NextRequest } from 'next/server';

export class CSRFError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CSRFError';
  }
}

// CSRF Token Configuration
const CSRF_CONFIG = {
  tokenName: '_csrf',
  cookieName: 'csrf-token',
  headerName: 'x-csrf-token',
  tokenLength: 32,
  cookieOptions: {
    httpOnly: false, // Allow client-side access for forms
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60 * 24, // 24 hours
    path: '/',
  },
};

/**
 * Generate a cryptographically secure CSRF token using Web Crypto API
 */
export function generateCSRFToken(): string {
  const array = new Uint8Array(CSRF_CONFIG.tokenLength);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Create a CSRF secret for token validation
 */
export function generateCSRFSecret(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Create a CSRF token hash using Web Crypto API
 */
export async function createTokenHash(token: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign('HMAC', key, encoder.encode(token));
  return Array.from(new Uint8Array(signature), byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Verify CSRF token against the secret
 */
export async function verifyCSRFToken(token: string, secret: string, expectedHash: string): Promise<boolean> {
  try {
    const computedHash = await createTokenHash(token, secret);
    return computedHash === expectedHash;
  } catch (error) {
    return false;
  }
}

/**
 * Get CSRF token from request headers or body
 */
export function getCSRFTokenFromRequest(request: NextRequest): string | null {
  // Try header first
  const headerToken = request.headers.get(CSRF_CONFIG.headerName);
  if (headerToken) {
    return headerToken;
  }

  // Try form data (for form submissions)
  const contentType = request.headers.get('content-type');
  if (contentType?.includes('application/x-www-form-urlencoded')) {
    try {
      const url = new URL(request.url);
      return url.searchParams.get(CSRF_CONFIG.tokenName);
    } catch {
      return null;
    }
  }

  return null;
}

/**
 * Get CSRF token from request cookies
 */
export function getCSRFCookieFromRequest(request: NextRequest): string | null {
  try {
    return request.cookies.get(CSRF_CONFIG.cookieName)?.value || null;
  } catch {
    return null;
  }
}

/**
 * Validate CSRF token for a request
 */
export async function validateCSRFToken(request: NextRequest): Promise<{
  isValid: boolean;
  error?: string;
}> {
  try {
    // Skip CSRF validation for safe methods
    const method = request.method.toUpperCase();
    if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
      return { isValid: true };
    }

    // Get CSRF secret from environment
    const csrfSecret = process.env.CSRF_SECRET;
    if (!csrfSecret) {
      return {
        isValid: false,
        error: 'CSRF secret not configured',
      };
    }

    // Get token from request
    const submittedToken = getCSRFTokenFromRequest(request);
    if (!submittedToken) {
      return {
        isValid: false,
        error: 'CSRF token missing from request',
      };
    }

    // Get stored token from cookies
    const storedToken = getCSRFCookieFromRequest(request);
    if (!storedToken) {
      return {
        isValid: false,
        error: 'CSRF token missing from cookies',
      };
    }

    // Verify token
    const expectedHash = await createTokenHash(storedToken, csrfSecret);
    const submittedHash = await createTokenHash(submittedToken, csrfSecret);

    if (expectedHash !== submittedHash) {
      return {
        isValid: false,
        error: 'CSRF token validation failed',
      };
    }

    return { isValid: true };
  } catch (error) {
    return {
      isValid: false,
      error: 'CSRF validation error',
    };
  }
}

/**
 * Middleware helper for CSRF protection
 */
export async function csrfProtection(request: NextRequest): Promise<{
  isValid: boolean;
  error?: string;
  token?: string;
}> {
  const validation = await validateCSRFToken(request);

  if (!validation.isValid) {
    return validation;
  }

  // Generate new token for safe methods or if none exists
  const method = request.method.toUpperCase();
  if (['GET', 'HEAD', 'OPTIONS'].includes(method)) {
    const existingToken = getCSRFCookieFromRequest(request);
    if (!existingToken) {
      const newToken = generateCSRFToken();
      return { isValid: true, token: newToken };
    }
    return { isValid: true, token: existingToken };
  }

  return { isValid: true };
}