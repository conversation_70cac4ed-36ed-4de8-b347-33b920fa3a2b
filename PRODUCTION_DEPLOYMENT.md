# Production Deployment Guide

This guide covers the complete process of deploying Property Trendz to production.

## 🔧 Pre-Deployment Checklist

### Environment Configuration
- [ ] Update `.env.production` with actual production values
- [ ] Generate secure secrets for all security-related variables
- [ ] Configure production database URL
- [ ] Set up SMTP email configuration
- [ ] Update domain and contact information

### Security
- [ ] Generate strong NEXTAUTH_SECRET (32+ characters)
- [ ] Generate secure CSRF_SECRET (64+ characters)
- [ ] Set up SSL certificates for HTTPS
- [ ] Configure firewall rules
- [ ] Set up database access restrictions

### Infrastructure
- [ ] Production server with Node.js 18+
- [ ] PostgreSQL database server
- [ ] Redis server (optional, for rate limiting)
- [ ] Nginx reverse proxy (recommended)
- [ ] Domain name and DNS configuration

## 🚀 Deployment Steps

### 1. Server Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2

# Install PostgreSQL (if not using external database)
sudo apt install postgresql postgresql-contrib
```

### 2. Application Deployment

```bash
# Clone repository
git clone https://github.com/your-username/Properties-Trends.git
cd Properties-Trends

# Install dependencies
npm ci --only=production

# Copy and configure environment
cp .env.production.example .env.production
# Edit .env.production with your actual values

# Build application
npm run build

# Start with PM2
npm run deploy:start
```

### 3. Database Setup

```bash
# Create database and user
sudo -u postgres psql
CREATE DATABASE property_trendz;
CREATE USER property_user WITH PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE property_trendz TO property_user;
\q

# Run database migrations
npm run db:push
```

### 4. Nginx Configuration

Create `/etc/nginx/sites-available/property-trendz`:

```nginx
server {
    listen 80;
    server_name property-trendz.com www.property-trendz.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name property-trendz.com www.property-trendz.com;

    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    location /uploads/ {
        alias /var/www/property-trendz/public/uploads/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/property-trendz /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 🔒 Security Hardening

### 1. Firewall Configuration
```bash
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. SSL Certificate (Let's Encrypt)
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d property-trendz.com -d www.property-trendz.com
```

### 3. Database Security
```bash
# Configure PostgreSQL for production
sudo nano /etc/postgresql/14/main/postgresql.conf
# Set: listen_addresses = 'localhost'

sudo nano /etc/postgresql/14/main/pg_hba.conf
# Configure authentication methods
```

## 📊 Monitoring and Maintenance

### 1. PM2 Monitoring
```bash
# View application status
pm2 status

# View logs
pm2 logs property-trendz

# Monitor resources
pm2 monit
```

### 2. Log Rotation
```bash
# Configure PM2 log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### 3. Backup Strategy
```bash
# Database backup script
#!/bin/bash
pg_dump -h localhost -U property_user property_trendz > backup_$(date +%Y%m%d_%H%M%S).sql

# Schedule with cron
0 2 * * * /path/to/backup_script.sh
```

## 🔄 Updates and Maintenance

### Application Updates
```bash
# Pull latest changes
git pull origin main

# Install new dependencies
npm ci --only=production

# Rebuild application
npm run build

# Restart with zero downtime
pm2 reload property-trendz
```

### Database Migrations
```bash
# Run new migrations
npm run db:push

# Or use migration files
npm run db:migrate
```

## 🚨 Troubleshooting

### Common Issues

1. **Application won't start**
   - Check environment variables in `.env.production`
   - Verify database connection
   - Check PM2 logs: `pm2 logs property-trendz`

2. **Database connection errors**
   - Verify DATABASE_URL format
   - Check PostgreSQL service: `sudo systemctl status postgresql`
   - Test connection: `npm run db:test`

3. **SSL certificate issues**
   - Renew certificates: `sudo certbot renew`
   - Check Nginx configuration: `sudo nginx -t`

4. **Performance issues**
   - Monitor with PM2: `pm2 monit`
   - Check server resources: `htop`
   - Review application logs

### Health Checks
```bash
# Application health
curl https://property-trendz.com/api/health

# Database health
npm run db:test

# PM2 status
pm2 status
```

## 📞 Support

For deployment issues or questions:
- Check the application logs: `pm2 logs property-trendz`
- Review Nginx logs: `sudo tail -f /var/log/nginx/error.log`
- Monitor system resources: `htop` or `pm2 monit`
