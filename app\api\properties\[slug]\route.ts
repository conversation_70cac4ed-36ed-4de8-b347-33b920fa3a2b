import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { properties, propertyViews } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get property by slug
    const property = await db
      .select()
      .from(properties)
      .where(
        and(
          eq(properties.slug, params.slug),
          eq(properties.published, true),
          or(
            eq(properties.status, 'for-sale'),
            eq(properties.status, 'for-rent')
          )
        )
      )
      .limit(1);

    if (!property[0]) {
      return NextResponse.json(
        { message: 'Property not found' },
        { status: 404 }
      );
    }

    // Track property view
    const clientIP = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown';
    const userAgent = request.headers.get('user-agent') || '';
    const referrer = request.headers.get('referer') || '';

    try {
      // Insert view record
      await db.insert(propertyViews).values({
        propertyId: property[0].id,
        ipAddress: clientIP,
        userAgent,
        referrer,
      });

      // Update view count on property
      await db
        .update(properties)
        .set({ 
          views: (property[0].views || 0) + 1,
          updatedAt: new Date(),
        })
        .where(eq(properties.id, property[0].id));
    } catch (viewError) {
      // Don't fail the request if view tracking fails
      console.error('Failed to track view:', viewError);
    }

    // Format the response
    const formattedProperty = {
      ...property[0],
      price: property[0].price ? parseFloat(property[0].price) : 0,
      pricePerSqft: property[0].pricePerSqft ? parseFloat(property[0].pricePerSqft) : null,
      latitude: property[0].latitude ? parseFloat(property[0].latitude) : null,
      longitude: property[0].longitude ? parseFloat(property[0].longitude) : null,
      views: (property[0].views || 0) + 1, // Include the new view
    };

    return NextResponse.json({
      property: formattedProperty,
    });
  } catch (error) {
    console.error('Property detail API error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}