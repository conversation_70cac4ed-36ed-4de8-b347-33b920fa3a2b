'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Save, 
  Globe, 
  Phone, 
  Mail, 
  MessageSquare,
  Search,
  FileText,
  Settings
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from 'react-hot-toast';

interface WebsiteSettings {
  siteName: string;
  siteDescription: string;
  siteKeywords: string;
  contactPhone: string;
  contactEmail: string;
  contactWhatsapp: string;
  address: string;
  socialMedia: {
    facebook: string;
    instagram: string;
    twitter: string;
    linkedin: string;
  };
  seo: {
    metaTitle: string;
    metaDescription: string;
    ogImage: string;
    analyticsId: string;
  };
  aboutContent: string;
  servicesContent: string;
  heroTitle: string;
  heroSubtitle: string;
  heroImage: string;
}

const initialSettings: WebsiteSettings = {
  siteName: 'Armaan Sharma Properties',
  siteDescription: 'Find Your Dream Home in Delhi NCR',
  siteKeywords: 'real estate, properties, Delhi NCR, apartments, houses',
  contactPhone: '+91 98765 43210',
  contactEmail: '<EMAIL>',
  contactWhatsapp: '+91 98765 43210',
  address: 'Delhi NCR, India',
  socialMedia: {
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: '',
  },
  seo: {
    metaTitle: 'Armaan Sharma Properties - Find Your Dream Home in Delhi NCR',
    metaDescription: 'Discover the best properties in Delhi NCR with Armaan Sharma Properties. Houses, apartments, and commercial spaces for sale and rent.',
    ogImage: '',
    analyticsId: '',
  },
  aboutContent: '',
  servicesContent: '',
  heroTitle: 'Find Your Dream Home',
  heroSubtitle: 'Discover the best properties in Delhi NCR',
  heroImage: '',
};

export function ContentManagement() {
  const [settings, setSettings] = useState<WebsiteSettings>(initialSettings);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings({ ...initialSettings, ...data });
      }
    } catch (error) {
      console.error('Failed to fetch settings:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setSettings(prev => ({
        ...prev,
        [parent]: {
          ...(prev[parent as keyof WebsiteSettings] as any),
          [child]: value,
        },
      }));
    } else {
      setSettings(prev => ({ ...prev, [field]: value }));
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch('/api/admin/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        toast.success('Settings saved successfully!');
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('An error occurred while saving settings');
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'general', label: 'General', icon: Globe },
    { id: 'contact', label: 'Contact', icon: Phone },
    { id: 'seo', label: 'SEO', icon: Search },
    { id: 'content', label: 'Content', icon: FileText },
  ];

  return (
    <form onSubmit={handleSave} className="space-y-8">
      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-card p-6">
        <div className="flex flex-wrap gap-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              type="button"
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 py-2 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <tab.icon className="w-4 h-4 mr-2" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* General Settings */}
      {activeTab === 'general' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">General Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Name
              </label>
              <Input
                value={settings.siteName}
                onChange={(e) => handleInputChange('siteName', e.target.value)}
                placeholder="Armaan Sharma Properties"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Site Description
              </label>
              <Input
                value={settings.siteDescription}
                onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                placeholder="Find Your Dream Home in Delhi NCR"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Keywords (comma separated)
              </label>
              <Input
                value={settings.siteKeywords}
                onChange={(e) => handleInputChange('siteKeywords', e.target.value)}
                placeholder="real estate, properties, Delhi NCR, apartments"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hero Title
              </label>
              <Input
                value={settings.heroTitle}
                onChange={(e) => handleInputChange('heroTitle', e.target.value)}
                placeholder="Find Your Dream Home"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hero Subtitle
              </label>
              <Input
                value={settings.heroSubtitle}
                onChange={(e) => handleInputChange('heroSubtitle', e.target.value)}
                placeholder="Discover the best properties in Delhi NCR"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Hero Background Image URL
              </label>
              <Input
                value={settings.heroImage}
                onChange={(e) => handleInputChange('heroImage', e.target.value)}
                placeholder="https://example.com/hero-image.jpg"
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Contact Settings */}
      {activeTab === 'contact' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Contact Information</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <Input
                value={settings.contactPhone}
                onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                placeholder="+91 98765 43210"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Address
              </label>
              <Input
                value={settings.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                placeholder="<EMAIL>"
                type="email"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                WhatsApp Number
              </label>
              <Input
                value={settings.contactWhatsapp}
                onChange={(e) => handleInputChange('contactWhatsapp', e.target.value)}
                placeholder="+91 98765 43210"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <Input
                value={settings.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Delhi NCR, India"
              />
            </div>
          </div>

          <div className="mt-8">
            <h4 className="text-md font-semibold text-gray-900 mb-4">Social Media Links</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Facebook
                </label>
                <Input
                  value={settings.socialMedia.facebook}
                  onChange={(e) => handleInputChange('socialMedia.facebook', e.target.value)}
                  placeholder="https://facebook.com/armaansharmaproperties"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Instagram
                </label>
                <Input
                  value={settings.socialMedia.instagram}
                  onChange={(e) => handleInputChange('socialMedia.instagram', e.target.value)}
                  placeholder="https://instagram.com/armaansharmaproperties"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Twitter
                </label>
                <Input
                  value={settings.socialMedia.twitter}
                  onChange={(e) => handleInputChange('socialMedia.twitter', e.target.value)}
                  placeholder="https://twitter.com/armaanproperties"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  LinkedIn
                </label>
                <Input
                  value={settings.socialMedia.linkedin}
                  onChange={(e) => handleInputChange('socialMedia.linkedin', e.target.value)}
                  placeholder="https://linkedin.com/company/armaan-sharma-properties"
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* SEO Settings */}
      {activeTab === 'seo' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">SEO Settings</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meta Title
              </label>
              <Input
                value={settings.seo.metaTitle}
                onChange={(e) => handleInputChange('seo.metaTitle', e.target.value)}
                placeholder="Armaan Sharma Properties - Find Your Dream Home"
              />
              <p className="text-sm text-gray-500 mt-1">
                {settings.seo.metaTitle.length}/60 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Meta Description
              </label>
              <textarea
                value={settings.seo.metaDescription}
                onChange={(e) => handleInputChange('seo.metaDescription', e.target.value)}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Discover the best properties in Delhi NCR..."
              />
              <p className="text-sm text-gray-500 mt-1">
                {settings.seo.metaDescription.length}/160 characters
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Open Graph Image URL
              </label>
              <Input
                value={settings.seo.ogImage}
                onChange={(e) => handleInputChange('seo.ogImage', e.target.value)}
                placeholder="https://example.com/og-image.jpg"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Google Analytics ID
              </label>
              <Input
                value={settings.seo.analyticsId}
                onChange={(e) => handleInputChange('seo.analyticsId', e.target.value)}
                placeholder="G-XXXXXXXXXX"
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Content Settings */}
      {activeTab === 'content' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-card p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Page Content</h3>
          
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                About Us Content
              </label>
              <textarea
                value={settings.aboutContent}
                onChange={(e) => handleInputChange('aboutContent', e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Write about your company, mission, and values..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Services Content
              </label>
              <textarea
                value={settings.servicesContent}
                onChange={(e) => handleInputChange('servicesContent', e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="Describe your services and what you offer..."
              />
            </div>
          </div>
        </motion.div>
      )}

      {/* Save Button */}
      <div className="flex justify-end bg-white rounded-lg shadow-card p-6">
        <Button
          type="submit"
          disabled={loading}
          loading={loading}
        >
          <Save className="w-4 h-4 mr-2" />
          Save Settings
        </Button>
      </div>
    </form>
  );
}