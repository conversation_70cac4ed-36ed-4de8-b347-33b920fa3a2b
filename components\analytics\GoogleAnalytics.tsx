'use client';

import { GoogleAnalytics as GA } from '@next/third-parties/google';
import { usePathname, useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

interface GoogleAnalyticsProps {
  gaId: string;
}

export function GoogleAnalytics({ gaId }: GoogleAnalyticsProps) {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    if (typeof window !== 'undefined' && window.gtag) {
      const url = pathname + searchParams.toString();
      
      // Send page view event
      window.gtag('config', gaId, {
        page_title: document.title,
        page_location: window.location.href,
        page_path: url,
      });
    }
  }, [pathname, searchParams, gaId]);

  if (!gaId || gaId === 'G-XXXXXXXXXX') {
    return null;
  }

  return <GA gaId={gaId} />;
}

// Custom analytics events
export const analytics = {
  // Property events
  viewProperty: (propertyId: string, propertyTitle: string, price: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'view_property', {
        property_id: propertyId,
        property_title: propertyTitle,
        property_price: price,
        currency: 'INR',
      });
    }
  },

  searchProperties: (searchTerm: string, filters: Record<string, any>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: searchTerm,
        search_filters: JSON.stringify(filters),
      });
    }
  },

  contactAgent: (propertyId: string, contactMethod: 'phone' | 'whatsapp' | 'email') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'contact_agent', {
        property_id: propertyId,
        contact_method: contactMethod,
      });
    }
  },

  submitEnquiry: (propertyId: string, enquiryType: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'submit_enquiry', {
        property_id: propertyId,
        enquiry_type: enquiryType,
      });
    }
  },

  addToFavorites: (propertyId: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_favorites', {
        property_id: propertyId,
      });
    }
  },

  removeFromFavorites: (propertyId: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'remove_from_favorites', {
        property_id: propertyId,
      });
    }
  },

  addToComparison: (propertyId: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_comparison', {
        property_id: propertyId,
      });
    }
  },

  viewComparison: (propertyIds: string[]) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'view_comparison', {
        property_count: propertyIds.length,
        property_ids: propertyIds.join(','),
      });
    }
  },

  shareProperty: (propertyId: string, method: 'link' | 'whatsapp' | 'facebook' | 'twitter') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'share', {
        method: method,
        content_type: 'property',
        item_id: propertyId,
      });
    }
  },

  // User engagement events
  signUp: (method: 'email' | 'google' | 'facebook') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'sign_up', {
        method: method,
      });
    }
  },

  login: (method: 'email' | 'google' | 'facebook') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'login', {
        method: method,
      });
    }
  },

  // Custom conversion events
  propertyLeadGenerated: (propertyId: string, leadType: 'phone' | 'email' | 'whatsapp') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'generate_lead', {
        property_id: propertyId,
        lead_type: leadType,
        currency: 'INR',
        value: 100, // Assign a value to leads for conversion tracking
      });
    }
  },

  fileDownload: (fileName: string, fileType: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'file_download', {
        file_name: fileName,
        file_type: fileType,
      });
    }
  },

  // E-commerce style events for property interactions
  beginCheckout: (propertyId: string, propertyPrice: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'begin_checkout', {
        currency: 'INR',
        value: propertyPrice,
        items: [{
          item_id: propertyId,
          item_name: 'Property Enquiry',
          item_category: 'Real Estate',
          price: propertyPrice,
          quantity: 1,
        }],
      });
    }
  },

  // Page performance events
  pageLoadTime: (loadTime: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'timing_complete', {
        name: 'page_load',
        value: Math.round(loadTime),
      });
    }
  },

  // Error tracking
  trackError: (error: string, page: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error,
        fatal: false,
        page_location: page,
      });
    }
  },
};

// Enhanced ecommerce events for property website
export const ecommerce = {
  viewItemList: (properties: Array<{ id: string; title: string; price: number; category: string }>) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'view_item_list', {
        item_list_id: 'property_search_results',
        item_list_name: 'Property Search Results',
        items: properties.map((property, index) => ({
          item_id: property.id,
          item_name: property.title,
          item_category: property.category,
          price: property.price,
          currency: 'INR',
          index: index,
        })),
      });
    }
  },

  selectItem: (property: { id: string; title: string; price: number; category: string }, listName = 'Property Search Results') => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'select_item', {
        item_list_id: 'property_search_results',
        item_list_name: listName,
        items: [{
          item_id: property.id,
          item_name: property.title,
          item_category: property.category,
          price: property.price,
          currency: 'INR',
        }],
      });
    }
  },

  viewItem: (property: { id: string; title: string; price: number; category: string }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'view_item', {
        currency: 'INR',
        value: property.price,
        items: [{
          item_id: property.id,
          item_name: property.title,
          item_category: property.category,
          price: property.price,
          currency: 'INR',
          quantity: 1,
        }],
      });
    }
  },

  addToWishlist: (property: { id: string; title: string; price: number; category: string }) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'add_to_wishlist', {
        currency: 'INR',
        value: property.price,
        items: [{
          item_id: property.id,
          item_name: property.title,
          item_category: property.category,
          price: property.price,
          currency: 'INR',
          quantity: 1,
        }],
      });
    }
  },
};

// Type declaration for window.gtag
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}