'use client'

import { motion, AnimatePresence } from 'framer-motion'
import {
  Award, Users, MapPin, Clock, CheckCircle, Star, Target, TrendingUp,
  Phone, MessageCircle, Mail, Calendar, ArrowRight, Building,
  Shield, Heart, Lightbulb, UserCheck, Trophy, Globe, Home,
  ChevronRight, Play, Quote, Linkedin, Twitter, Facebook
} from 'lucide-react'
import Image from 'next/image'
import Link from 'next/link'
import { useState, useEffect } from 'react'

// Animation variants
const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.6 } }
}

const fadeInLeft = {
  initial: { opacity: 0, x: -60 },
  animate: { opacity: 1, x: 0, transition: { duration: 0.6 } }
}

const fadeInRight = {
  initial: { opacity: 0, x: 60 },
  animate: { opacity: 1, x: 0, transition: { duration: 0.6 } }
}

const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

const scaleIn = {
  initial: { opacity: 0, scale: 0.8 },
  animate: { opacity: 1, scale: 1, transition: { duration: 0.6 } }
}

export default function AboutPage() {
  const [activeTab, setActiveTab] = useState('story')
  const [currentTestimonial, setCurrentTestimonial] = useState(0)

  const stats = [
    { icon: Award, number: '35+', label: 'Years Experience', color: 'text-yellow-600' },
    { icon: Users, number: '5000+', label: 'Happy Customers', color: 'text-blue-600' },
    { icon: MapPin, number: '100+', label: 'Areas Covered', color: 'text-green-600' },
    { icon: Building, number: '10000+', label: 'Properties Sold', color: 'text-purple-600' }
  ]

  const teamMembers = [
    {
      name: 'Vishal Bahardwaj',
      position: 'Founder and CEO',
      experience: '35+ Years',
      bio: 'Visionary leader with over three decades of experience in Delhi NCR real estate market.',
      specialties: ['Luxury Properties', 'Commercial Real Estate', 'Investment Advisory']
    },
    {
      name: 'Lakshay Sharma',
      position: 'Head of Sales',
      experience: '15+ Years',
      bio: 'Expert in residential sales with a track record of helping over 1000 families find their dream homes.',
      specialties: ['Residential Sales', 'Customer Relations', 'Market Analysis']
    },
    {
      name: 'Armaan Sharma',
      position: 'Investment Advisor',
      experience: '12+ Years',
      bio: 'Strategic investment consultant specializing in high-yield property portfolios.',
      specialties: ['Investment Strategy', 'Portfolio Management', 'Market Research']
    },
    {
      name: 'FrostvelStudios',
      position: 'Technology Partner',
      experience: 'Established 2020',
      bio: 'Innovative software development company specializing in modern web applications, digital solutions, and cutting-edge technology implementations for real estate platforms.',
      specialties: ['Web Development', 'Digital Solutions', 'Technology Innovation'],
      isCompany: true,
      githubUrl: 'https://github.com/ForstvalStudio'
    }
  ]

  const services = [
    {
      icon: Home,
      title: 'Property Sales',
      description: 'Expert guidance for buying and selling residential and commercial properties across Delhi NCR.',
      features: ['Market Analysis', 'Price Negotiation', 'Documentation Support'],
      color: 'bg-blue-500'
    },
    {
      icon: Building,
      title: 'Property Rentals',
      description: 'Comprehensive rental services for landlords and tenants with transparent agreements.',
      features: ['Tenant Verification', 'Rental Management', 'Legal Support'],
      color: 'bg-green-500'
    },
    {
      icon: TrendingUp,
      title: 'Investment Advisory',
      description: 'Strategic real estate investment consultation to maximize your property portfolio returns.',
      features: ['ROI Analysis', 'Market Trends', 'Portfolio Planning'],
      color: 'bg-purple-500'
    },
    {
      icon: Shield,
      title: 'Legal Support',
      description: 'Complete documentation and legal assistance for all property transactions.',
      features: ['Title Verification', 'Contract Review', 'Registration'],
      color: 'bg-orange-500'
    },
    {
      icon: Target,
      title: 'Property Valuation',
      description: 'Accurate market valuations based on current trends and comprehensive analysis.',
      features: ['Market Comparison', 'Professional Assessment', 'Detailed Reports'],
      color: 'bg-red-500'
    },
    {
      icon: CheckCircle,
      title: 'Property Inspection',
      description: 'Thorough property inspections and due diligence for informed decision making.',
      features: ['Structural Assessment', 'Quality Check', 'Compliance Verification'],
      color: 'bg-indigo-500'
    }
  ]

  const testimonials = [
    {
      name: 'Rohit & Kavya Mehta',
      location: 'Gurgaon',
      rating: 5,
      text: 'Property Trendz helped us find our dream home in Gurgaon. Their team was professional, transparent, and guided us through every step. Highly recommended!',
      image: 'https://images.unsplash.com/photo-1521119989659-a83eee488004?w=100&h=100&fit=crop&crop=face',
      property: '3BHK Apartment'
    },
    {
      name: 'Suresh Agarwal',
      location: 'Delhi',
      rating: 5,
      text: 'Excellent service for commercial property investment. Their market insights and legal support made the entire process smooth and profitable.',
      image: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=100&h=100&fit=crop&crop=face',
      property: 'Commercial Space'
    },
    {
      name: 'Anita Sharma',
      location: 'Noida',
      rating: 5,
      text: 'Sold my property through Property Trendz at the best market price. Their negotiation skills and market knowledge are exceptional.',
      image: 'https://images.unsplash.com/photo-1580489944761-15a19d654956?w=100&h=100&fit=crop&crop=face',
      property: '2BHK Flat'
    }
  ]

  const values = [
    {
      icon: Heart,
      title: 'Customer First',
      description: 'Your needs and satisfaction are our top priority in every interaction.',
      color: 'bg-red-100 text-red-600'
    },
    {
      icon: Shield,
      title: 'Transparency',
      description: 'Clear communication and honest dealings in every transaction.',
      color: 'bg-blue-100 text-blue-600'
    },
    {
      icon: Trophy,
      title: 'Excellence',
      description: 'Commitment to delivering exceptional service and outstanding results.',
      color: 'bg-yellow-100 text-yellow-600'
    },
    {
      icon: Lightbulb,
      title: 'Innovation',
      description: 'Using modern technology to enhance your property experience.',
      color: 'bg-purple-100 text-purple-600'
    },
    {
      icon: UserCheck,
      title: 'Integrity',
      description: 'Building trust through ethical practices and reliable service.',
      color: 'bg-green-100 text-green-600'
    },
    {
      icon: Globe,
      title: 'Local Expertise',
      description: 'Deep understanding of Delhi NCR markets and regulations.',
      color: 'bg-indigo-100 text-indigo-600'
    }
  ]

  const achievements = [
    {
      year: '1989',
      title: 'Company Founded',
      description: 'Started with a vision to transform real estate in Delhi NCR'
    },
    {
      year: '1995',
      title: 'First 100 Properties',
      description: 'Successfully facilitated our first 100 property transactions'
    },
    {
      year: '2005',
      title: 'Commercial Expansion',
      description: 'Expanded into commercial real estate and investment advisory'
    },
    {
      year: '2015',
      title: 'Digital Transformation',
      description: 'Launched online platform and digital property services'
    },
    {
      year: '2020',
      title: 'Industry Recognition',
      description: 'Awarded "Best Real Estate Consultant" in Delhi NCR'
    },
    {
      year: '2024',
      title: '10,000+ Properties',
      description: 'Milestone achievement of facilitating over 10,000 property deals'
    }
  ]

  // Auto-rotate testimonials
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length)
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="min-h-screen bg-neutral-50 pt-16">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with overlay */}
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=1920&h=1080&fit=crop"
            alt="Delhi NCR Skyline"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-br from-neutral-900/80 via-neutral-800/70 to-primary-900/80"></div>
        </div>

        <div className="container-custom relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto text-white"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <span className="inline-block bg-primary-600/20 backdrop-blur-sm border border-primary-400/30 text-primary-300 px-6 py-2 rounded-full text-sm font-medium mb-6">
                Trusted Since 1989
              </span>
            </motion.div>

            <h1 className="text-5xl md:text-7xl font-display font-bold mb-8 leading-tight">
              <span className="block text-white">
                History of this
              </span>
              <span className="block text-white bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text">
                Company
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-200 leading-relaxed mb-12 max-w-4xl mx-auto">
              With over <span className="text-primary-400 font-semibold">35 years of experience</span> in the Delhi NCR real estate market,
              we are your trusted partner for finding the perfect property. Our mission is
              to make property transactions <span className="text-secondary-400 font-semibold">transparent, efficient, and stress-free</span>.
            </p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16"
            >
              <Link
                href="tel:+919810129777"
                className="btn-primary bg-primary-600 hover:bg-primary-700 text-white px-8 py-4 text-lg font-semibold flex items-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Call Us Now</span>
              </Link>
              <Link
                href="/contact"
                className="btn-outline border-white text-white hover:bg-white hover:text-neutral-800 px-8 py-4 text-lg font-semibold flex items-center space-x-2"
              >
                <MessageCircle className="w-5 h-5" />
                <span>Get in Touch</span>
              </Link>
            </motion.div>

            {/* Quick Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto"
            >
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 + index * 0.1 }}
                  className="text-center"
                >
                  <div className={`w-12 h-12 mx-auto mb-3 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center ${stat.color}`}>
                    <stat.icon className="w-6 h-6" />
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-white mb-1">{stat.number}</div>
                  <div className="text-sm text-gray-300">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center"
          >
            <motion.div
              animate={{ y: [0, 12, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-3 bg-white/60 rounded-full mt-2"
            />
          </motion.div>
        </motion.div>
      </section>

      {/* Company Story with Timeline */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-800 mb-6">
              Our Journey
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              From humble beginnings to becoming Delhi NCR's most trusted real estate partner
            </p>
          </motion.div>

          {/* Timeline */}
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-primary-600 to-secondary-600 rounded-full"></div>

            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`relative flex items-center mb-12 ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                  <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300">
                    <div className="text-primary-600 font-bold text-lg mb-2">{achievement.year}</div>
                    <h3 className="text-xl font-semibold text-neutral-800 mb-3">{achievement.title}</h3>
                    <p className="text-neutral-600">{achievement.description}</p>
                  </div>
                </div>

                <div className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-600 rounded-full border-4 border-white shadow-lg"></div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Meet Our Team */}
      <section className="section-padding bg-gradient-to-br from-neutral-50 to-neutral-100">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-800 mb-6">
              Meet Our Expert Team
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Our experienced professionals are dedicated to making your property dreams a reality
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {teamMembers.map((member, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
              >
                {/* Avatar placeholder instead of image */}
                <div className="relative overflow-hidden h-64 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                  <div className="w-24 h-24 bg-primary-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-2xl">
                      {member.isCompany ? 'FS' : member.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-semibold text-neutral-800 mb-2">{member.name}</h3>
                  <p className="text-primary-600 font-medium mb-1">{member.position}</p>
                  <p className="text-sm text-neutral-500 mb-3">{member.experience}</p>
                  <p className="text-neutral-600 text-sm mb-4">{member.bio}</p>

                  {/* GitHub link for company */}
                  {member.isCompany && member.githubUrl && (
                    <div className="mb-4">
                      <a
                        href={member.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 text-sm font-medium"
                      >
                        <span>View on GitHub</span>
                        <ArrowRight className="w-4 h-4" />
                      </a>
                    </div>
                  )}

                  <div className="space-y-2">
                    <p className="text-xs font-medium text-neutral-700 mb-2">Specialties:</p>
                    <div className="flex flex-wrap gap-1">
                      {member.specialties.map((specialty, idx) => (
                        <span
                          key={idx}
                          className="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Our Services */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-800 mb-6">
              Our Services
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Comprehensive real estate solutions tailored to meet your specific needs and exceed your expectations
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {services.map((service, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-neutral-100"
              >
                <div className="p-8">
                  <div className={`w-16 h-16 ${service.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <service.icon className="w-8 h-8 text-white" />
                  </div>

                  <h3 className="text-2xl font-semibold text-neutral-800 mb-4 group-hover:text-primary-600 transition-colors">
                    {service.title}
                  </h3>

                  <p className="text-neutral-600 mb-6 leading-relaxed">
                    {service.description}
                  </p>

                  <div className="space-y-2">
                    <p className="text-sm font-medium text-neutral-700 mb-3">Key Features:</p>
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500" />
                        <span className="text-sm text-neutral-600">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="px-8 pb-8">
                  <button className="w-full btn-outline group-hover:btn-primary transition-all duration-300 flex items-center justify-center space-x-2">
                    <span>Learn More</span>
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="section-padding bg-gradient-to-br from-primary-50 to-secondary-50">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-800 mb-6">
              What Our Clients Say
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              Real stories from satisfied customers who found their dream properties with us
            </p>
          </motion.div>

          <div className="relative max-w-4xl mx-auto">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentTestimonial}
                initial={{ opacity: 0, x: 100 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -100 }}
                transition={{ duration: 0.5 }}
                className="bg-white rounded-3xl shadow-xl p-8 md:p-12"
              >
                <div className="flex items-center mb-6">
                  <Quote className="w-12 h-12 text-primary-600 mr-4" />
                  <div className="flex space-x-1">
                    {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                      <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                </div>

                <p className="text-lg md:text-xl text-neutral-700 leading-relaxed mb-8 italic">
                  "{testimonials[currentTestimonial].text}"
                </p>

                <div className="flex items-center">
                  <Image
                    src={testimonials[currentTestimonial].image}
                    alt={testimonials[currentTestimonial].name}
                    width={60}
                    height={60}
                    className="rounded-full mr-4"
                  />
                  <div>
                    <h4 className="font-semibold text-neutral-800 text-lg">
                      {testimonials[currentTestimonial].name}
                    </h4>
                    <p className="text-neutral-600">
                      {testimonials[currentTestimonial].location} • {testimonials[currentTestimonial].property}
                    </p>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>

            {/* Testimonial indicators */}
            <div className="flex justify-center space-x-2 mt-8">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-200 ${
                    index === currentTestimonial
                      ? 'bg-primary-600 scale-125'
                      : 'bg-neutral-300 hover:bg-neutral-400'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="section-padding bg-white">
        <div className="container-custom">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold text-neutral-800 mb-6">
              Our Core Values
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto">
              The principles that guide everything we do and define who we are as a company
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {values.map((value, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="group text-center bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-neutral-100"
              >
                <div className={`w-20 h-20 ${value.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <value.icon className="w-10 h-10" />
                </div>
                <h3 className="text-xl font-semibold text-neutral-800 mb-4 group-hover:text-primary-600 transition-colors">
                  {value.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="section-padding bg-gradient-to-br from-neutral-900 to-neutral-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/20 to-secondary-600/20"></div>
        <div className="container-custom relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold mb-6">
              Why Choose Property Trendz?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We combine local expertise with modern technology to deliver unmatched service and exceptional results
            </p>
          </motion.div>

          <motion.div
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {[
              {
                icon: Trophy,
                title: 'Award-Winning Service',
                description: 'Recognized for excellence in customer service and industry innovation',
                color: 'bg-yellow-500'
              },
              {
                icon: Target,
                title: 'Local Expertise',
                description: 'Deep knowledge of Delhi NCR markets, trends, and regulations',
                color: 'bg-blue-500'
              },
              {
                icon: Lightbulb,
                title: 'Modern Technology',
                description: 'Advanced tools and platforms for seamless property transactions',
                color: 'bg-purple-500'
              },
              {
                icon: UserCheck,
                title: 'Trusted Partnerships',
                description: 'Strong relationships with developers, banks, and legal experts',
                color: 'bg-green-500'
              },
              {
                icon: Clock,
                title: '24/7 Support',
                description: 'Round-the-clock assistance for all your property needs',
                color: 'bg-red-500'
              },
              {
                icon: CheckCircle,
                title: 'Guaranteed Results',
                description: 'Commitment to achieving your property goals with measurable outcomes',
                color: 'bg-indigo-500'
              }
            ].map((feature, index) => (
              <motion.div
                key={index}
                variants={fadeInUp}
                className="group bg-white/10 backdrop-blur-sm rounded-2xl p-8 hover:bg-white/20 transition-all duration-300 border border-white/20"
              >
                <div className={`w-16 h-16 ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-semibold mb-4 group-hover:text-primary-400 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-300 leading-relaxed">
                  {feature.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="section-padding bg-gradient-to-r from-primary-600 to-secondary-600 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-black/20 to-transparent"></div>
        <div className="container-custom relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="text-center"
          >
            <h2 className="text-4xl md:text-5xl font-display font-bold mb-6">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-blue-100 mb-12 max-w-3xl mx-auto leading-relaxed">
              Whether you're buying, selling, or investing, our team of experts is here to guide you
              every step of the way. Let's make your property dreams a reality with our proven expertise.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
              <Link
                href="/properties"
                className="btn-primary bg-white text-primary-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold flex items-center space-x-2"
              >
                <Home className="w-5 h-5" />
                <span>Browse Properties</span>
              </Link>
              <Link
                href="/contact"
                className="btn-outline border-white text-white hover:bg-white hover:text-primary-600 px-8 py-4 text-lg font-semibold flex items-center space-x-2"
              >
                <Calendar className="w-5 h-5" />
                <span>Schedule Consultation</span>
              </Link>
            </div>

            <div className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 text-blue-100">
              <div className="flex items-center space-x-2">
                <Phone className="w-5 h-5 text-primary-300" />
                <span>+91 98101 29777</span>
              </div>
              <div className="flex items-center space-x-2">
                <MessageCircle className="w-5 h-5 text-green-400" />
                <span>WhatsApp Support</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="w-5 h-5 text-blue-300" />
                <span><EMAIL></span>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}