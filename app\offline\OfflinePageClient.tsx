'use client';

import Link from 'next/link';
import { Home, RefreshCw, WifiOff } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { useEffect } from 'react';

export function OfflinePageClient() {
  useEffect(() => {
    // Auto-refresh when back online
    const handleOnline = () => {
      window.location.reload();
    };
    
    window.addEventListener('online', handleOnline);
    
    // Periodic connection check
    const interval = setInterval(() => {
      if (navigator.onLine) {
        window.location.reload();
      }
    }, 10000);

    return () => {
      window.removeEventListener('online', handleOnline);
      clearInterval(interval);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        {/* Offline Icon */}
        <div className="mb-6">
          <div className="relative mx-auto w-20 h-20">
            <WifiOff className="w-20 h-20 text-gray-400 mx-auto" />
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-bold">!</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            You're Offline
          </h1>
          <p className="text-gray-600 mb-6 leading-relaxed">
            It looks like you're not connected to the internet. Please check your 
            connection and try again. Some cached content may still be available.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-4 mb-6">
          <Button
            onClick={() => window.location.reload()}
            className="w-full flex items-center justify-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Try Again
          </Button>
          
          <Link href="/" className="block">
            <Button
              variant="outline"
              className="w-full flex items-center justify-center gap-2"
            >
              <Home className="w-4 h-4" />
              Go to Homepage
            </Button>
          </Link>
        </div>

        {/* Offline Features */}
        <div className="border-t pt-6">
          <h3 className="text-sm font-medium text-gray-900 mb-3">
            Available Offline
          </h3>
          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center justify-between">
              <span>Browse cached properties</span>
              <span className="text-green-600">✓</span>
            </div>
            <div className="flex items-center justify-between">
              <span>View favorites</span>
              <span className="text-green-600">✓</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Saved searches</span>
              <span className="text-green-600">✓</span>
            </div>
          </div>
        </div>

        {/* Connection Status */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-center gap-2 text-sm">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-gray-600">No internet connection</span>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            The page will automatically refresh when connection is restored.
          </p>
        </div>
      </div>
    </div>
  );
}