import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db/config';
import { enquiries } from '@/lib/db/schema';
import { z } from 'zod';

// Contact form validation schema
const contactSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  message: z.string().min(1, 'Message is required').max(1000, 'Message too long'),
  reasonForContact: z.string().min(1, 'Reason for contact is required'),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  propertyType: z.string().optional(),
});

// POST - Submit contact form
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = contactSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          message: 'Validation failed',
          errors: validationResult.error.errors 
        },
        { status: 400 }
      );
    }

    const { name, email, phone, message, reasonForContact, budget, timeline, propertyType } = validationResult.data;

    // Map reasonForContact to enquiryType for database compatibility
    const enquiryTypeMapping: Record<string, string> = {
      'buying': 'property_interest',
      'selling': 'property_interest',
      'investing': 'property_interest',
      'rental': 'rental',
      'valuation': 'valuation',
      'consultation': 'general',
      'general': 'general'
    };

    const enquiryType = enquiryTypeMapping[reasonForContact] || 'general';

    // Create detailed message with additional information
    let detailedMessage = message;
    if (budget || timeline || propertyType) {
      detailedMessage += '\n\nAdditional Details:';
      if (propertyType) detailedMessage += `\n- Property Type: ${propertyType}`;
      if (budget) detailedMessage += `\n- Budget: ${budget}`;
      if (timeline) detailedMessage += `\n- Timeline: ${timeline}`;
    }

    // Create enquiry record for contact form submission
    const newEnquiry = await db
      .insert(enquiries)
      .values({
        name,
        email,
        phone: phone || '',
        message: detailedMessage,
        enquiryType,
        budget: budget || null,
        status: 'new',
        source: 'contact_form',
        propertyId: null, // No specific property for general contact
        notes: `Reason: ${reasonForContact}${timeline ? ` | Timeline: ${timeline}` : ''}${propertyType ? ` | Property Type: ${propertyType}` : ''}`,
      })
      .returning();

    // Here you could also send email notifications to admin
    // TODO: Implement email notification to admin

    return NextResponse.json(
      {
        message: 'Thank you for your message! We will get back to you soon.',
        enquiry: {
          id: newEnquiry[0].id,
          name: newEnquiry[0].name,
          email: newEnquiry[0].email,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Contact form submission error:', error);
    return NextResponse.json(
      { message: 'Failed to submit your message. Please try again.' },
      { status: 500 }
    );
  }
}
