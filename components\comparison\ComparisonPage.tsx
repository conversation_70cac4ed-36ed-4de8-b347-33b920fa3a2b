'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Scale, 
  Plus, 
  X, 
  Search, 
  MapPin, 
  Bed, 
  Bath, 
  Square, 
  Car, 
  Heart,
  Eye,
  Share2,
  Download,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { FavoriteButton } from '@/components/ui/FavoriteButton';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';

interface Property {
  id: string;
  title: string;
  slug: string;
  price: string;
  area: string;
  city: string;
  propertyType: string;
  listingType: string;
  bedrooms: number;
  bathrooms: number;
  totalArea: number;
  carpetArea?: number;
  builtUpArea?: number;
  parking?: number;
  balconies?: number;
  furnished: string;
  ageOfProperty?: number;
  facing?: string;
  floors?: number;
  totalFloors?: number;
  images: string[];
  amenities: string[];
  features: string[];
  status: string;
  pricePerSqft?: number;
  createdAt: string;
}

const maxComparisons = 3;

export function ComparisonPage() {
  const [selectedProperties, setSelectedProperties] = useState<Property[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<Property[]>([]);
  const [loading, setLoading] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  // Load comparison from localStorage or URL parameters on mount
  useEffect(() => {
    const loadProperties = async () => {
      // Check for URL parameters first
      const urlParams = new URLSearchParams(window.location.search);
      const propertyIds = urlParams.get('properties');
      
      if (propertyIds) {
        // Load properties from URL
        try {
          const ids = propertyIds.split(',');
          const response = await fetch(`/api/properties?ids=${ids.join(',')}`);
          if (response.ok) {
            const data = await response.json();
            setSelectedProperties(data.properties);
            // Save to localStorage for consistency
            localStorage.setItem('propertyComparison', JSON.stringify(data.properties));
            return;
          }
        } catch (error) {
          console.error('Error loading properties from URL:', error);
        }
      }
      
      // Fallback to localStorage
      const savedComparison = localStorage.getItem('propertyComparison');
      if (savedComparison) {
        try {
          const parsed = JSON.parse(savedComparison);
          setSelectedProperties(parsed);
        } catch (error) {
          console.error('Error loading saved comparison:', error);
        }
      }
    };
    
    loadProperties();
  }, []);

  // Save to localStorage whenever selection changes
  useEffect(() => {
    localStorage.setItem('propertyComparison', JSON.stringify(selectedProperties));
  }, [selectedProperties]);

  const searchProperties = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/properties?search=${encodeURIComponent(query)}&limit=10`);
      if (response.ok) {
        const data = await response.json();
        // Filter out already selected properties
        const filtered = data.properties.filter((prop: Property) => 
          !selectedProperties.some(selected => selected.id === prop.id)
        );
        setSearchResults(filtered);
      }
    } catch (error) {
      console.error('Error searching properties:', error);
    } finally {
      setLoading(false);
    }
  };

  const addProperty = (property: Property) => {
    if (selectedProperties.length < maxComparisons && 
        !selectedProperties.some(p => p.id === property.id)) {
      setSelectedProperties([...selectedProperties, property]);
      setSearchTerm('');
      setSearchResults([]);
      setShowSearch(false);
    }
  };

  const removeProperty = (propertyId: string) => {
    setSelectedProperties(selectedProperties.filter(p => p.id !== propertyId));
  };

  const clearAll = () => {
    setSelectedProperties([]);
    localStorage.removeItem('propertyComparison');
  };

  const formatPrice = (price: string, listingType: string) => {
    const numPrice = parseFloat(price);
    if (listingType === 'rent') {
      return `₹${numPrice.toLocaleString('en-IN')}/month`;
    }
    if (numPrice >= 10000000) {
      return `₹${(numPrice / 10000000).toFixed(1)} Cr`;
    }
    if (numPrice >= 100000) {
      return `₹${(numPrice / 100000).toFixed(1)} L`;
    }
    return `₹${numPrice.toLocaleString('en-IN')}`;
  };

  const exportComparison = () => {
    const comparisonData = {
      properties: selectedProperties,
      comparedOn: new Date().toISOString(),
      url: window.location.href
    };
    
    const blob = new Blob([JSON.stringify(comparisonData, null, 2)], {
      type: 'application/json'
    });
    
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `property-comparison-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const shareComparison = async () => {
    const propertyIds = selectedProperties.map(p => p.id).join(',');
    const shareUrl = `${window.location.origin}/compare?properties=${propertyIds}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Property Comparison',
          text: 'Check out this property comparison',
          url: shareUrl,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      await navigator.clipboard.writeText(shareUrl);
      alert('Comparison link copied to clipboard!');
    }
  };

  const ComparisonRow = ({ label, getValue, isHighlighted = false }: {
    label: string;
    getValue: (property: Property) => string | number;
    isHighlighted?: boolean;
  }) => {
    const gridCols = selectedProperties.length === 1 ? 'grid-cols-2' : 
                     selectedProperties.length === 2 ? 'grid-cols-3' : 
                     'grid-cols-4';
    
    return (
      <div className={`grid ${gridCols} gap-4 py-3 border-b border-gray-100 ${
        isHighlighted ? 'bg-blue-50' : ''
      }`}>
        <div className="font-medium text-gray-700">{label}</div>
        {selectedProperties.map((property) => (
          <div key={property.id} className="text-gray-900">
            {getValue(property) || 'N/A'}
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 pt-16">
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                <Scale className="w-6 h-6 mr-2 text-blue-600" />
                Compare Properties
              </h1>
              <p className="text-gray-600 mt-1">
                Compare up to {maxComparisons} properties side by side
              </p>
            </div>

            {selectedProperties.length > 0 && (
              <div className="flex gap-2">
                <Button variant="outline" onClick={exportComparison}>
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" onClick={shareComparison}>
                  <Share2 className="w-4 h-4 mr-2" />
                  Share
                </Button>
                <Button variant="outline" onClick={clearAll}>
                  <X className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {selectedProperties.length === 0 ? (
          // Empty State
          <div className="text-center py-16">
            <Scale className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              Start Comparing Properties
            </h3>
            <p className="text-gray-600 mb-6">
              Add up to {maxComparisons} properties to compare their features side by side
            </p>
            <Button onClick={() => setShowSearch(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Properties
            </Button>
          </div>
        ) : (
          // Comparison View
          <div className="space-y-6">
            {/* Add More Properties */}
            {selectedProperties.length < maxComparisons && (
              <div className="bg-white rounded-lg shadow-card p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">Add More Properties</h3>
                    <p className="text-gray-600">
                      You can add {maxComparisons - selectedProperties.length} more {selectedProperties.length === maxComparisons - 1 ? 'property' : 'properties'}
                    </p>
                  </div>
                  <Button onClick={() => setShowSearch(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Property
                  </Button>
                </div>
              </div>
            )}

            {/* Property Cards */}
            <div className={`grid gap-6 ${
              selectedProperties.length === 1 ? 'grid-cols-1 max-w-md mx-auto' :
              selectedProperties.length === 2 ? 'grid-cols-1 md:grid-cols-2' :
              'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
            }`}>
              {selectedProperties.map((property) => (
                <motion.div
                  key={property.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-white rounded-lg shadow-card overflow-hidden"
                >
                  <div className="relative h-48">
                    <Image
                      src={property.images?.[0] || '/placeholder-property.svg'}
                      alt={property.title}
                      fill
                      className="object-cover"
                    />
                    <div className="absolute top-3 right-3 flex gap-2">
                      <FavoriteButton propertyId={property.id} size="sm" />
                      <button
                        onClick={() => removeProperty(property.id)}
                        className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                    <div className="absolute bottom-3 left-3">
                      <span className="bg-black/70 text-white px-2 py-1 rounded text-sm">
                        {property.listingType === 'sale' ? 'For Sale' : 'For Rent'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <Link href={`/properties/${property.slug}`}>
                      <h3 className="font-semibold text-gray-900 hover:text-blue-600 transition-colors mb-2 line-clamp-2">
                        {property.title}
                      </h3>
                    </Link>
                    
                    <div className="flex items-center text-gray-600 text-sm mb-2">
                      <MapPin className="w-4 h-4 mr-1" />
                      {property.area}, {property.city}
                    </div>
                    
                    <div className="text-xl font-bold text-blue-600 mb-3">
                      {formatPrice(property.price, property.listingType)}
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center gap-3">
                        <span className="flex items-center">
                          <Bed className="w-4 h-4 mr-1" />
                          {property.bedrooms}
                        </span>
                        <span className="flex items-center">
                          <Bath className="w-4 h-4 mr-1" />
                          {property.bathrooms}
                        </span>
                        <span className="flex items-center">
                          <Square className="w-4 h-4 mr-1" />
                          {property.totalArea}
                        </span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Detailed Comparison Table */}
            {selectedProperties.length > 1 && (
              <div className="bg-white rounded-lg shadow-card overflow-hidden">
                <div className="p-6 border-b border-gray-200">
                  <h3 className="text-lg font-semibold">Detailed Comparison</h3>
                </div>
                
                <div className="overflow-x-auto">
                  <div className="p-6">
                    {/* Headers */}
                    <div className={`grid ${selectedProperties.length === 1 ? 'grid-cols-2' : 
                                             selectedProperties.length === 2 ? 'grid-cols-3' : 
                                             'grid-cols-4'} gap-4 pb-4 border-b-2 border-gray-200 font-semibold`}>
                      <div>Features</div>
                      {selectedProperties.map((property) => (
                        <div key={property.id} className="text-center">
                          <Link href={`/properties/${property.slug}`} className="text-blue-600 hover:text-blue-800">
                            Property {selectedProperties.indexOf(property) + 1}
                          </Link>
                        </div>
                      ))}
                    </div>

                    {/* Basic Info */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Basic Information</h4>
                      <ComparisonRow label="Price" getValue={(p) => formatPrice(p.price, p.listingType)} isHighlighted />
                      <ComparisonRow label="Price per sq ft" getValue={(p) => p.pricePerSqft ? `₹${p.pricePerSqft.toLocaleString()}` : 'N/A'} />
                      <ComparisonRow label="Property Type" getValue={(p) => p.propertyType} />
                      <ComparisonRow label="Listing Type" getValue={(p) => `For ${p.listingType}`} />
                      <ComparisonRow label="Status" getValue={(p) => p.status} />
                    </div>

                    {/* Specifications */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Specifications</h4>
                      <ComparisonRow label="Bedrooms" getValue={(p) => p.bedrooms} isHighlighted />
                      <ComparisonRow label="Bathrooms" getValue={(p) => p.bathrooms} />
                      <ComparisonRow label="Total Area" getValue={(p) => `${p.totalArea} sq ft`} isHighlighted />
                      <ComparisonRow label="Carpet Area" getValue={(p) => p.carpetArea ? `${p.carpetArea} sq ft` : 'N/A'} />
                      <ComparisonRow label="Built-up Area" getValue={(p) => p.builtUpArea ? `${p.builtUpArea} sq ft` : 'N/A'} />
                      <ComparisonRow label="Parking" getValue={(p) => p.parking || 0} />
                      <ComparisonRow label="Balconies" getValue={(p) => p.balconies || 0} />
                      <ComparisonRow label="Furnished" getValue={(p) => p.furnished} />
                    </div>

                    {/* Building Details */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Building Details</h4>
                      <ComparisonRow label="Age" getValue={(p) => p.ageOfProperty !== null ? `${p.ageOfProperty} years` : 'N/A'} />
                      <ComparisonRow label="Facing" getValue={(p) => p.facing || 'N/A'} />
                      <ComparisonRow label="Floor" getValue={(p) => {
                        if (p.floors && p.totalFloors) {
                          return `${p.floors} of ${p.totalFloors}`;
                        }
                        return p.floors ? `Floor ${p.floors}` : 'N/A';
                      }} />
                    </div>

                    {/* Amenities */}
                    <div className="mt-6">
                      <h4 className="font-semibold text-gray-900 mb-3">Amenities</h4>
                      <div className={`grid ${selectedProperties.length === 1 ? 'grid-cols-2' : 
                                             selectedProperties.length === 2 ? 'grid-cols-3' : 
                                             'grid-cols-4'} gap-4 py-3`}>
                        <div className="font-medium text-gray-700">Available Amenities</div>
                        {selectedProperties.map((property) => (
                          <div key={property.id}>
                            <div className="space-y-1 text-sm">
                              {property.amenities?.slice(0, 5).map((amenity, index) => (
                                <div key={index} className="text-green-600">• {amenity}</div>
                              ))}
                              {property.amenities?.length > 5 && (
                                <div className="text-gray-500">+ {property.amenities.length - 5} more</div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="mt-8 pt-6 border-t border-gray-200">
                      <div className={`grid ${selectedProperties.length === 1 ? 'grid-cols-2' : 
                                             selectedProperties.length === 2 ? 'grid-cols-3' : 
                                             'grid-cols-4'} gap-4`}>
                        <div className="font-medium text-gray-700">Actions</div>
                        {selectedProperties.map((property) => (
                          <div key={property.id} className="space-y-2">
                            <Link href={`/properties/${property.slug}`}>
                              <Button size="sm" className="w-full">
                                <Eye className="w-4 h-4 mr-1" />
                                View Details
                              </Button>
                            </Link>
                            <Button size="sm" variant="outline" className="w-full">
                              <ArrowRight className="w-4 h-4 mr-1" />
                              Contact Agent
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Search Modal */}
      {showSearch && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Add Property to Compare</h3>
                <button
                  onClick={() => setShowSearch(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search properties by title, location, or area..."
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    searchProperties(e.target.value);
                  }}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div className="p-6 max-h-96 overflow-y-auto">
              {loading ? (
                <div className="flex justify-center py-8">
                  <LoadingSpinner />
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-3">
                  {searchResults.map((property) => (
                    <div
                      key={property.id}
                      onClick={() => addProperty(property)}
                      className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 cursor-pointer transition-colors"
                    >
                      <img
                        src={property.images?.[0] || '/placeholder-property.svg'}
                        alt={property.title}
                        className="w-16 h-16 rounded-lg object-cover"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{property.title}</h4>
                        <p className="text-sm text-gray-600">{property.area}, {property.city}</p>
                        <p className="text-sm font-semibold text-blue-600">
                          {formatPrice(property.price, property.listingType)}
                        </p>
                      </div>
                      <Plus className="w-5 h-5 text-blue-600" />
                    </div>
                  ))}
                </div>
              ) : searchTerm && !loading ? (
                <div className="text-center py-8 text-gray-500">
                  No properties found for "{searchTerm}"
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  Start typing to search for properties
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}