import { NextRequest, NextResponse } from 'next/server';
import { generateCSRFToken } from '@/lib/security/csrf';

// CSRF Token Configuration
const CSRF_CONFIG = {
  cookieName: 'csrf-token',
  cookieOptions: {
    httpOnly: false, // Allow client-side access for forms
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 60 * 60 * 24, // 24 hours
    path: '/',
  },
};

/**
 * GET /api/csrf-token - Generate and return a new CSRF token
 */
export async function GET(request: NextRequest) {
  try {
    // Generate new CSRF token
    const token = generateCSRFToken();

    // Create response with token
    const response = NextResponse.json({
      token,
      message: 'CSRF token generated successfully'
    }, {
      headers: {
        'Cache-Control': 'no-store, no-cache, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

    // Set CSRF token in cookie using NextResponse
    response.cookies.set(CSRF_CONFIG.cookieName, token, CSRF_CONFIG.cookieOptions);

    return response;
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate CSRF token' },
      { status: 500 }
    );
  }
}