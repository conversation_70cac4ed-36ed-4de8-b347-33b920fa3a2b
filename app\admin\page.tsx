import { getServerSession, authConfig } from '@/lib/auth/auth';
import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { AdminDashboard } from '@/components/admin/AdminDashboard';

export const metadata: Metadata = {
  title: 'Admin Dashboard | Property Trendz',
  description: 'Admin dashboard for managing properties, users, and website content',
};

export default async function AdminPage() {
  const session = await getServerSession(authConfig) as any;

  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin');
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminDashboard />
    </div>
  );
}