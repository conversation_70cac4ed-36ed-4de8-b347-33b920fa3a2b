import { db } from '@/lib/db/config';
import { properties, users, enquiries, propertyImages } from '@/lib/db/schema';
import { and, eq, desc, asc, count, sql, like, inArray } from 'drizzle-orm';
import { createCachedFunction, CACHE_TAGS, CACHE_DURATIONS } from '@/lib/performance/cache';

// Optimized property queries with caching
export const getPropertiesWithPagination = createCachedFunction(
  async (page: number = 1, limit: number = 12, filters?: any) => {
    const offset = (page - 1) * limit;
    
    // Build where conditions
    const conditions = [];
    if (filters?.type) {
      conditions.push(eq(properties.propertyType, filters.type));
    }
    if (filters?.status) {
      conditions.push(eq(properties.status, filters.status));
    }
    if (filters?.minPrice) {
      conditions.push(sql`${properties.price} >= ${filters.minPrice}`);
    }
    if (filters?.maxPrice) {
      conditions.push(sql`${properties.price} <= ${filters.maxPrice}`);
    }
    if (filters?.city) {
      conditions.push(like(properties.city, `%${filters.city}%`));
    }

    // Get total count for pagination
    const [totalResult] = await db
      .select({ count: count() })
      .from(properties)
      .where(and(...conditions));

    // Get properties with images
    const result = await db
      .select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        type: properties.propertyType,
        status: properties.status,
        price: properties.price,
        area: properties.area,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        city: properties.city,
        state: properties.state,
        slug: properties.slug,
        featured: properties.featured,
        createdAt: properties.createdAt,
        updatedAt: properties.updatedAt,
        // Get first image
        mainImage: sql<string>`(
          SELECT ${propertyImages.url} 
          FROM ${propertyImages} 
          WHERE ${propertyImages.propertyId} = ${properties.id}
          ORDER BY ${propertyImages.order} ASC 
          LIMIT 1
        )`,
        // Get total image count
        imageCount: sql<number>`(
          SELECT COUNT(*) 
          FROM ${propertyImages} 
          WHERE ${propertyImages.propertyId} = ${properties.id}
        )`,
      })
      .from(properties)
      .where(and(...conditions))
      .orderBy(desc(properties.featured), desc(properties.createdAt))
      .limit(limit)
      .offset(offset);

    return {
      properties: result,
      total: totalResult.count,
      totalPages: Math.ceil(totalResult.count / limit),
      currentPage: page,
      hasNext: page * limit < totalResult.count,
      hasPrev: page > 1,
    };
  },
  {
    tags: [CACHE_TAGS.properties],
    revalidate: CACHE_DURATIONS.medium,
  }
);

export const getFeaturedProperties = createCachedFunction(
  async (limit: number = 6) => {
    return await db
      .select({
        id: properties.id,
        title: properties.title,
        type: properties.propertyType,
        price: properties.price,
        area: properties.area,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        city: properties.city,
        state: properties.state,
        slug: properties.slug,
        mainImage: sql<string>`(
          SELECT ${propertyImages.url} 
          FROM ${propertyImages} 
          WHERE ${propertyImages.propertyId} = ${properties.id}
          ORDER BY ${propertyImages.order} ASC 
          LIMIT 1
        )`,
      })
      .from(properties)
      .where(and(eq(properties.featured, true), eq(properties.published, true)))
      .orderBy(desc(properties.createdAt))
      .limit(limit);
  },
  {
    tags: [CACHE_TAGS.properties],
    revalidate: CACHE_DURATIONS.long,
  }
);

export const getPropertyById = createCachedFunction(
  async (id: string) => {
    const [property] = await db
      .select()
      .from(properties)
      .where(eq(properties.id, id))
      .limit(1);

    if (!property) return null;

    // Get all images for this property
    const images = await db
      .select()
      .from(propertyImages)
      .where(eq(propertyImages.propertyId, id))
      .orderBy(asc(propertyImages.order));

    return {
      ...property,
      images,
    };
  },
  {
    tags: [CACHE_TAGS.properties],
    revalidate: CACHE_DURATIONS.medium,
  }
);

export const getPropertyBySlug = createCachedFunction(
  async (slug: string) => {
    const [property] = await db
      .select()
      .from(properties)
      .where(and(eq(properties.slug, slug), eq(properties.published, true)))
      .limit(1);

    if (!property) return null;

    // Get all images for this property
    const images = await db
      .select()
      .from(propertyImages)
      .where(eq(propertyImages.propertyId, property.id))
      .orderBy(asc(propertyImages.order));

    return {
      ...property,
      images,
    };
  },
  {
    tags: [CACHE_TAGS.properties],
    revalidate: CACHE_DURATIONS.medium,
  }
);

export const getPropertiesWithMinimalData = createCachedFunction(
  async () => {
    return await db
      .select({
        id: properties.id,
        title: properties.title,
        slug: properties.slug,
        price: properties.price,
        type: properties.propertyType,
        city: properties.city,
        updatedAt: properties.updatedAt,
      })
      .from(properties)
      .where(eq(properties.published, true))
      .orderBy(desc(properties.updatedAt));
  },
  {
    tags: [CACHE_TAGS.properties],
    revalidate: CACHE_DURATIONS.long,
  }
);

export const searchProperties = createCachedFunction(
  async (query: string, limit: number = 12) => {
    const searchTerm = `%${query}%`;
    
    return await db
      .select({
        id: properties.id,
        title: properties.title,
        description: properties.description,
        type: properties.propertyType,
        price: properties.price,
        area: properties.area,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        city: properties.city,
        state: properties.state,
        slug: properties.slug,
        mainImage: sql<string>`(
          SELECT ${propertyImages.url} 
          FROM ${propertyImages} 
          WHERE ${propertyImages.propertyId} = ${properties.id}
          ORDER BY ${propertyImages.order} ASC 
          LIMIT 1
        )`,
      })
      .from(properties)
      .where(
        and(
          eq(properties.published, true),
          sql`(
            ${properties.title} ILIKE ${searchTerm} OR 
            ${properties.description} ILIKE ${searchTerm} OR 
            ${properties.city} ILIKE ${searchTerm} OR 
            ${properties.state} ILIKE ${searchTerm}
          )`
        )
      )
      .orderBy(desc(properties.featured), desc(properties.createdAt))
      .limit(limit);
  },
  {
    tags: [CACHE_TAGS.properties, CACHE_TAGS.search],
    revalidate: CACHE_DURATIONS.short,
  }
);

export const getPropertyStats = createCachedFunction(
  async () => {
    const [totalProperties] = await db
      .select({ count: count() })
      .from(properties)
      .where(eq(properties.published, true));

    const [totalUsers] = await db
      .select({ count: count() })
      .from(users);

    const [totalEnquiries] = await db
      .select({ count: count() })
      .from(enquiries);

    const [featuredProperties] = await db
      .select({ count: count() })
      .from(properties)
      .where(and(eq(properties.featured, true), eq(properties.published, true)));

    // Property type distribution
    const propertyTypes = await db
      .select({
        type: properties.propertyType,
        count: count(),
      })
      .from(properties)
      .where(eq(properties.published, true))
      .groupBy(properties.propertyType);

    // Recent activity (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [recentEnquiries] = await db
      .select({ count: count() })
      .from(enquiries)
      .where(sql`${enquiries.createdAt} >= ${thirtyDaysAgo}`);

    return {
      totalProperties: totalProperties.count,
      totalUsers: totalUsers.count,
      totalEnquiries: totalEnquiries.count,
      featuredProperties: featuredProperties.count,
      propertyTypes,
      recentEnquiries: recentEnquiries.count,
    };
  },
  {
    tags: [CACHE_TAGS.properties, CACHE_TAGS.users, CACHE_TAGS.enquiries, CACHE_TAGS.analytics],
    revalidate: CACHE_DURATIONS.long,
  }
);

// Utility function to invalidate caches when properties are modified
export function invalidatePropertyCaches() {
  // This would be used with Next.js revalidateTag in the actual implementation
  console.log('Property caches invalidated');
}

// Similar optimized queries for users and enquiries
export const getUserDashboardData = createCachedFunction(
  async (userId: string) => {
    // Get user's enquiries
    const userEnquiries = await db
      .select({
        id: enquiries.id,
        propertyId: enquiries.propertyId,
        status: enquiries.status,
        createdAt: enquiries.createdAt,
        propertyTitle: properties.title,
        propertyPrice: properties.price,
        propertyCity: properties.city,
      })
      .from(enquiries)
      .leftJoin(properties, eq(enquiries.propertyId, properties.id))
      .where(eq(enquiries.userId, userId))
      .orderBy(desc(enquiries.createdAt))
      .limit(10);

    // Get user's favorite properties (if favorites table exists)
    // This would need to be implemented based on your schema

    return {
      enquiries: userEnquiries,
      // favorites: userFavorites,
    };
  },
  {
    tags: [CACHE_TAGS.enquiries, CACHE_TAGS.users],
    revalidate: CACHE_DURATIONS.short,
  }
);