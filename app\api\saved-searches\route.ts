import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authConfig } from '@/lib/auth/auth.config';
import type { Session } from 'next-auth';
import { db } from '@/lib/db/config';
import { savedSearches } from '@/lib/db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { withValidation } from '@/lib/validation/middleware';
import { z } from 'zod';

const savedSearchSchema = z.object({
  name: z.string().min(1, 'Search name is required').max(100, 'Name too long'),
  searchParams: z.object({
    search: z.string().optional(),
    location: z.string().optional(),
    propertyType: z.string().optional(),
    listingType: z.enum(['sale', 'rent']).optional(),
    minPrice: z.number().min(0).optional(),
    maxPrice: z.number().min(0).optional(),
    bedrooms: z.number().int().min(0).max(20).optional(),
    bathrooms: z.number().int().min(0).max(20).optional(),
    minArea: z.number().min(0).optional(),
    maxArea: z.number().min(0).optional(),
    furnished: z.enum(['furnished', 'semi-furnished', 'unfurnished']).optional(),
    amenities: z.array(z.string()).optional(),
  }),
  emailAlerts: z.boolean().default(true),
  frequency: z.enum(['daily', 'weekly', 'monthly']).default('daily'),
});

// GET /api/saved-searches - Get user's saved searches
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userSavedSearches = await db
      .select()
      .from(savedSearches)
      .where(eq(savedSearches.userId, session.user.id))
      .orderBy(desc(savedSearches.createdAt));

    return NextResponse.json({ 
      savedSearches: userSavedSearches,
      total: userSavedSearches.length 
    });

  } catch (error) {
    console.error('Error fetching saved searches:', error);
    return NextResponse.json(
      { error: 'Failed to fetch saved searches' }, 
      { status: 500 }
    );
  }
}

// POST /api/saved-searches - Create new saved search
export const POST = withValidation(
  async (request: NextRequest, { body }) => {
    try {
      const session = await getServerSession(authConfig) as Session | null;
      
      if (!session?.user?.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      const { name, searchParams, emailAlerts, frequency } = body!;

      // Check if user already has a saved search with this name
      const existingSearch = await db
        .select({ id: savedSearches.id })
        .from(savedSearches)
        .where(and(
          eq(savedSearches.userId, session.user.id),
          eq(savedSearches.name, name)
        ))
        .limit(1);

      if (existingSearch.length > 0) {
        return NextResponse.json(
          { error: 'A saved search with this name already exists' },
          { status: 409 }
        );
      }

      // Create saved search
      const newSavedSearch = await db
        .insert(savedSearches)
        .values({
          userId: session.user.id,
          name,
          searchParams,
          emailAlerts,
          frequency,
          isActive: true,
        })
        .returning();

      return NextResponse.json({
        message: 'Saved search created successfully',
        savedSearch: newSavedSearch[0],
      });

    } catch (error) {
      console.error('Error creating saved search:', error);
      return NextResponse.json(
        { error: 'Failed to create saved search' },
        { status: 500 }
      );
    }
  },
  {
    body: savedSearchSchema,
  }
);

// PUT /api/saved-searches - Update saved search
export const PUT = withValidation(
  async (request: NextRequest, { body }) => {
    try {
      const session = await getServerSession(authConfig) as Session | null;
      
      if (!session?.user?.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      const { searchParams } = new URL(request.url);
      const id = searchParams.get('id');

      if (!id) {
        return NextResponse.json({ error: 'Search ID required' }, { status: 400 });
      }

      const { name, searchParams: params, emailAlerts, frequency } = body!;

      // Verify ownership
      const existingSearch = await db
        .select({ id: savedSearches.id })
        .from(savedSearches)
        .where(and(
          eq(savedSearches.id, id),
          eq(savedSearches.userId, session.user.id)
        ))
        .limit(1);

      if (existingSearch.length === 0) {
        return NextResponse.json({ error: 'Saved search not found' }, { status: 404 });
      }

      // Update saved search
      const updatedSearch = await db
        .update(savedSearches)
        .set({
          name,
          searchParams: params,
          emailAlerts,
          frequency,
          updatedAt: new Date(),
        })
        .where(eq(savedSearches.id, id))
        .returning();

      return NextResponse.json({
        message: 'Saved search updated successfully',
        savedSearch: updatedSearch[0],
      });

    } catch (error) {
      console.error('Error updating saved search:', error);
      return NextResponse.json(
        { error: 'Failed to update saved search' },
        { status: 500 }
      );
    }
  },
  {
    body: savedSearchSchema,
  }
);

// DELETE /api/saved-searches - Delete saved search
export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig) as Session | null;
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Search ID required' }, { status: 400 });
    }

    // Verify ownership and delete
    const deletedSearch = await db
      .delete(savedSearches)
      .where(and(
        eq(savedSearches.id, id),
        eq(savedSearches.userId, session.user.id)
      ))
      .returning();

    if (deletedSearch.length === 0) {
      return NextResponse.json({ error: 'Saved search not found' }, { status: 404 });
    }

    return NextResponse.json({
      message: 'Saved search deleted successfully',
      savedSearch: deletedSearch[0],
    });

  } catch (error) {
    console.error('Error deleting saved search:', error);
    return NextResponse.json(
      { error: 'Failed to delete saved search' },
      { status: 500 }
    );
  }
}