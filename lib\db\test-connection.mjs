#!/usr/bin/env node

// Load environment variables
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment files based on NODE_ENV
const nodeEnv = process.env.NODE_ENV || 'development';
if (nodeEnv === 'production') {
  dotenv.config({ path: path.join(process.cwd(), '.env.production') });
} else {
  dotenv.config({ path: path.join(process.cwd(), '.env.local') });
}

console.log('🔍 Environment Diagnostics:');
console.log('Current working directory:', process.cwd());
console.log('NODE_ENV:', nodeEnv);
console.log('Environment file:', nodeEnv === 'production' ? '.env.production' : '.env.local');

// Import centralized database configuration
const { databaseConfig } = await import('./config.js');

console.log('\n🔗 Centralized Database Configuration:');
console.log('Environment:', databaseConfig.environment);
console.log('Host:', databaseConfig.host);
console.log('Port:', databaseConfig.port);
console.log('Database:', databaseConfig.database);
console.log('Username:', databaseConfig.username);
console.log('Docker:', databaseConfig.isDocker ? 'Yes' : 'No');
console.log('URL Set:', databaseConfig.url ? 'Yes ✅' : 'Missing ❌');

// Test database connection using postgres.js directly
console.log('\n🔄 Testing database connection...');

try {
  // Import postgres
  const postgres = (await import('postgres')).default;

  // Create connection using centralized configuration
  const sql = postgres(databaseConfig.url);
  
  // Simple test query
  const result = await sql`SELECT NOW() as current_time, version() as pg_version`;
  console.log('✅ Database connection successful!');
  console.log('Current time:', result[0].current_time);
  console.log('PostgreSQL version:', result[0].pg_version);
  
  // Test users table
  console.log('\n📋 Testing users table...');
  const usersResult = await sql`SELECT COUNT(*) as user_count FROM users`;
  console.log('Total users:', usersResult[0].user_count);
  
  // Test admin users
  const adminResult = await sql`SELECT email FROM users WHERE role = 'admin' LIMIT 1`;
  if (adminResult.length > 0) {
    console.log('✅ Admin user found:', adminResult[0].email);
  } else {
    console.log('❌ No admin users found');
  }
  
  // Test properties table
  const propertiesResult = await sql`SELECT COUNT(*) as property_count FROM properties`;
  console.log('Total properties:', propertiesResult[0].property_count);
  
  console.log('\n🎉 All database tests passed!');
  
  // Close connection
  await sql.end();
  
} catch (error) {
  console.error('\n❌ Database connection failed:');
  console.error('Error type:', error.constructor.name);
  console.error('Error message:', error.message);
  
  if (error.cause) {
    console.error('Underlying cause:', error.cause);
  }
  
  console.log('\n🔧 Debugging tips:');
  console.log('1. Ensure PostgreSQL container is running: docker ps');
  console.log('2. Check DATABASE_URL format in .env.local');
  console.log('3. Verify database credentials');
  console.log('4. Test direct connection: docker exec armaan_properties_db psql -U armaan_user -d armaan_properties');
}

process.exit(0);