# Development Database Configuration (Docker)
# This will automatically route to Docker containerized PostgreSQL
DATABASE_URL="postgresql://Yash123414:z098opl%40ZAQ@localhost:5432/armaan_properties"
NODE_ENV="development"

# NextAuth.js Configuration
NEXTAUTH_SECRET="your-super-secret-key-32-characters-long-minimum"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers
# Google OAuth - Get from: https://console.cloud.google.com/
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Facebook OAuth - Get from: https://developers.facebook.com/apps/
FACEBOOK_CLIENT_ID="your-facebook-app-id"
FACEBOOK_CLIENT_SECRET="your-facebook-app-secret"

# Cloudinary Configuration - Get from: https://cloudinary.com/console
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# Site Configuration
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_SITE_NAME="Armaan Sharma Properties"

# Contact Information
NEXT_PUBLIC_CONTACT_PHONE="+91 98765 43210"
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_CONTACT_ADDRESS="Delhi NCR, India"

# Email Configuration (Optional - for contact forms)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# Google Maps API (Optional)
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"

# Analytics (Optional)
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"

# Admin Settings
ADMIN_EMAIL="<EMAIL>"