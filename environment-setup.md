# Environment Variables Setup Guide

## 1. Google OAuth Setup

### Step 1: Create Google Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API

### Step 2: Create OAuth Credentials
1. Go to **APIs & Services > Credentials**
2. Click **Create Credentials > OAuth 2.0 Client IDs**
3. Configure consent screen if required
4. Set application type to **Web Application**
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google`
   - `https://yourdomain.com/api/auth/callback/google` (for production)

### Step 3: Update Environment Variables
```env
GOOGLE_CLIENT_ID="your-actual-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-actual-client-secret"
```

---

## 2. Facebook OAuth Setup

### Step 1: Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/apps/)
2. Click **Create App**
3. Select **Consumer** app type
4. Fill in app details

### Step 2: Configure Facebook Login
1. Add **Facebook Login** product
2. Go to **Facebook Login > Settings**
3. Add Valid OAuth Redirect URIs:
   - `http://localhost:3000/api/auth/callback/facebook`
   - `https://yourdomain.com/api/auth/callback/facebook` (for production)

### Step 3: Update Environment Variables
```env
FACEBOOK_CLIENT_ID="your-facebook-app-id"
FACEBOOK_CLIENT_SECRET="your-facebook-app-secret"
```

---

## 3. Cloudinary Setup

### Step 1: Create Account
1. Go to [Cloudinary](https://cloudinary.com/)
2. Sign up for free account
3. Go to Dashboard

### Step 2: Get Credentials
1. Find your **Cloud Name**, **API Key**, and **API Secret** on dashboard
2. Update environment variables:

```env
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"
```

---

## 4. Optional Services

### Google Maps API (For location features)
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable **Maps JavaScript API**
3. Create API key
4. Restrict API key to your domain
5. Update:
```env
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-maps-api-key"
```

### Google Analytics (For tracking)
1. Go to [Google Analytics](https://analytics.google.com/)
2. Create property
3. Get Measurement ID (G-XXXXXXXXXX)
4. Update:
```env
NEXT_PUBLIC_GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
```

---

## 5. Email Configuration (Optional)

For contact form functionality, configure SMTP:

### Gmail SMTP Setup:
1. Enable 2-factor authentication on your Gmail account
2. Generate App Password: Google Account > Security > App Passwords
3. Update:
```env
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-16-character-app-password"
```

---

## 6. Security Notes

1. **Never commit `.env.local` to version control**
2. Use different secrets for production
3. Rotate secrets regularly
4. Use environment-specific URLs for OAuth callbacks

---

## 7. Testing Configuration

After setup, test each service:

```bash
# Test database connection
npm run db:push

# Test OAuth (start dev server and try login)
npm run dev

# Test Cloudinary (upload image in admin panel)
# Visit http://localhost:3000/admin after login
```

---

## 8. Production Deployment

For production (Vercel/Netlify/etc.):

1. Add all environment variables to your hosting platform
2. Update OAuth redirect URLs with production domain
3. Use production database URL
4. Update `NEXT_PUBLIC_SITE_URL` to production domain
5. Use strong, unique `NEXTAUTH_SECRET`