# Production Environment Configuration
# Configured for property-trendz.com domain

# --- Core Application ---
NODE_ENV=production
NEXT_PUBLIC_SITE_URL="https://property-trendz.com"
NEXT_PUBLIC_SITE_NAME="Property Trendz"

# --- Analytics (Optional) ---
# Uncomment and add your Google Analytics ID for tracking
# NEXT_PUBLIC_GA_ID="G-XXXXXXXXXX"
# NEXT_PUBLIC_GTM_ID="GTM-XXXXXXX"

# --- Database (PostgreSQL) ---
# Production database URL
DATABASE_URL="postgresql://yash123414:<EMAIL>:5432/armaan_properties"

# --- Authentication (NextAuth.js) ---
NEXTAUTH_URL="https://property-trendz.com"
NEXTAUTH_SECRET="ucntcomptmeimgr8strivl"
COOKIE_DOMAIN="property-trendz.com"

# --- Security ---
# Secure CSRF protection key (64+ characters)
CSRF_SECRET="8f9e2a7b4c6d1e3f5a8b9c2d4e6f7a1b3c5d8e9f2a4b6c8d1e3f5a7b9c2d4e6f8a1b3c5d7e9f2a4b6c8d1e3f5a7b9c"

# --- Rate Limiting (Upstash Redis - Recommended for production) ---
# For production, use Redis for distributed rate limiting
# Sign up at https://upstash.com/ and get your credentials
# UPSTASH_REDIS_REST_URL="https://your-redis-instance.upstash.io"
# UPSTASH_REDIS_REST_TOKEN="your-redis-token"

# --- Email Configuration (SMTP) ---
# Configure your production email service
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-specific-password"

# --- Public Contact Information ---
# Update with your actual business contact details
NEXT_PUBLIC_CONTACT_EMAIL="<EMAIL>"
NEXT_PUBLIC_CONTACT_PHONE="+1234567890"
NEXT_PUBLIC_CONTACT_ADDRESS="Your Business Address"

# --- WhatsApp Business ---
# Your WhatsApp business number (digits only, with country code)
NEXT_PUBLIC_WHATSAPP_NUMBER="1234567890"

# --- File Upload Configuration ---
# Ensure this directory exists and has proper permissions
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="10485760"

# --- Cloudinary (Optional - for advanced image management) ---
# Uncomment and configure if you want to use Cloudinary for image optimization
# Sign up at https://cloudinary.com/ and get your credentials
# NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME="your-cloud-name"
# CLOUDINARY_API_KEY="your-api-key"
# CLOUDINARY_API_SECRET="your-api-secret"

# --- OAuth Providers (Optional) ---
# Configure only if you want to enable social login
# Google OAuth: https://console.cloud.google.com/
# GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
# GOOGLE_CLIENT_SECRET="your-google-client-secret"
# Facebook OAuth: https://developers.facebook.com/apps/
# FACEBOOK_CLIENT_ID="your-facebook-app-id"
# FACEBOOK_CLIENT_SECRET="your-facebook-app-secret"

# --- SEO and Verification (Optional) ---
# Add your site verification codes for search engines
# NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION="your-google-verification-code"
# NEXT_PUBLIC_BING_SITE_VERIFICATION="your-bing-verification-code"

# --- Performance and CDN (Optional) ---
# Configure CDN for static assets if using one
# NEXT_PUBLIC_CDN_URL="https://cdn.yourdomain.com"

# --- Monitoring and Logging (Optional) ---
# Configure error tracking and monitoring
# SENTRY_DSN="your-sentry-dsn"
# LOG_LEVEL="info"

# --- Additional Security ---
# Secure encryption and JWT keys
ENCRYPTION_KEY="a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"
JWT_SECRET="9f8e7d6c5b4a3928374650192837465019283746501928374650192837465019"

